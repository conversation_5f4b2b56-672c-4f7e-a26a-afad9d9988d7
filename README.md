## 上海交通大学宁波人工智能研究院门户官网
> 该项目基于springboot+jsp实现独立式部署的内容管理系统，其主要功能有模板编写。栏目编写。内容编写等



## 运行条件
> 运行的前提条件有以下：  
* JDK17
* MySQL8



## 运行说明
> 运行使用运行脚本。采用maven构建项目。springboot形式运行
* 操作一 启动命令 nohup java -jar naii-gateway.jar & echo $! > naii-gateway.pid
* 操作二 停止命令 kill $(cat naii-gateway.pid) 
* 操作三 删除进程文件 rm naii-gateway.pid



## 测试说明
> 对内容管理的后台进行功能遍历性测试，对前端展示的功能进行功能遍历性测试。 



## 技术架构
> 采用SpringBoot + hibernate + JSP方式
