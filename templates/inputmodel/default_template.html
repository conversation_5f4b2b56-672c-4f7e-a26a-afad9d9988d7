<div class="layui-form-item">
    <label class="layui-form-label" id="label_columnName">文章标题</label>
    <div class="layui-input-block">
        <input type="text" name="title" required lay-verify="required" autocomplete="off" placeholder="限制30字以内"
               class="layui-input" value="{news.title}">
    </div>
</div>
<div class="layui-form-item" id="sitecolumn_editUseTitlepic" style="display:none;">
    <label class="layui-form-label" id="label_columnName">标题图片</label>
    <div class="layui-input-block">
        <input name="titlepic" id="titlePicInput" type="text" autocomplete="off" placeholder="点击右侧添加"
               class="layui-input" value="{titlepicImage}" style="padding-right: 120px;">
        <button type="button" class="layui-btn" id="uploadImagesButton" style="float: right;margin-top: -38px;">
            <i class="layui-icon layui-icon-upload"></i>
        </button>
        <a href="" id="titlePicA" style="float: right;margin-top: -38px;margin-right: 60px; display: none;"
           title="预览原始图片" target="_black">
            <img id="titlePicImg" src=""
                 onerror="this.style.display='none';" style="height: 36px;max-width: 57px; padding-top: 1px; display: none;"
                 alt="预览原始图片">
        </a><input class="layui-upload-file" type="file" name="fileName">
    </div>
</div>
<div class="layui-form-item" id="sitecolumn_editUseExtendPhotos" style="display:none;">
    <div id="photosDefaultValue" style="display:none;">{news.extend.photos}</div>
    <input type="hidden" value="0" id="photos_i" style="display:none;"/>
    <label class="layui-form-label" id="label_columnName">文章图集</label>
    <div class="layui-input-block" id="photoInputList" style="min-height: 0px;">
        <div id="photos_input_item_{i}" style="padding-top:5px;">
            <input name="extend.photos" id="titlePicInput{i}" type="text" autocomplete="off" placeholder="点击右侧添加"
                   class="layui-input" value="{value}" style="padding-right: 174px;">
            <button type="button" name="{i}" class="layui-btn uploadImagesButton" id="uploadImagesButton{i}"
                    style="float: right;margin-top: -38px;">
                <i class="layui-icon layui-icon-upload"></i>
            </button>
            <a href="" id="titlePicA{i}" style="float: right;margin-top: -38px;margin-right: 116px; display: none;"
               title="预览原始图片" target="_black">
                <img id="titlePicImg{i}" src=""
                     onerror="this.style.display='none';" style="height: 36px;max-width: 57px; padding-top: 1px; display: none;"
                     alt="预览原始图片">
            </a><input class="layui-upload-file" type="file" name="fileName">
            <a href="javascript:deletePhotosInput('{i}');" class="layui-btn"
               style="float: right;margin-top: -38px;margin-right: 58px;" title="删除">
                <i class="layui-icon layui-icon-delete"></i>
            </a>
        </div>
        <!-- item模版结束 -->
    </div>
    <div style="padding-top:5px; padding-left:110px;">
        <a href="javascript:appendPhotosInput('');" class="layui-btn layui-btn-sm layui-btn-primary layui-btn-radius"
           style="float:left;">向图集添加一个图片输入框</a>
    </div>
</div>
<script type="text/javascript" src="/js/manager/cms/news_extend_photos.js"></script>
<div class="layui-form-item" id="sitecolumn_editUseIntro" style="display:none;">
    <label class="layui-form-label" id="label_columnName">内容简介</label>
    <div class="layui-input-block">
        <textarea class="layui-input" name="intro" style="height:70px;">{news.intro}</textarea>
    </div>
</div>
<!-- 配置文件 -->
<script src="/js/tinymce/tinymce.min.js"></script>
<!-- 编辑器源码文件 -->
<div class="layui-form-item" id="sitecolumn_editUseText" style="display:none;">
    <label class="layui-form-label" id="sucai">内容正文</label>
    <div class="layui-input-block" id="ueditorUpperDiv" style="border: 0px;">
        <label for="myEditor"></label><textarea class="layui-input" id="myEditor" name="text"
                                                style="height: auto; padding-left: 0px; border: 0px;">{text}</textarea>
    </div>
</div>
<!-- 实例化编辑器 -->
<script type="text/javascript">
    // 默认文件扩展类型配置
    const DEFAULT_EXTS = {
        image: ".jpg,.jpeg,.png,.gif,.ico,.svg",
        media: ".mp3,.mp4,.avi,.mov,.m4v",
        file: ".pdf,.txt,.zip,.rar,.7z,.doc,.docx,.xls,.xlsx,.ppt,.pptx"
    };

    // 初始化 TinyMCE
    tinymce.init({
        selector: '#myEditor',
        language: "zh_CN",
        placeholder: "在这里输入文字",
        min_width: 420,
        height: 770,
        branding: false,
        font_formats:
            "微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;宋体=simsun,serif;仿宋体=FangSong,serif;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;", //字体样式
        plugins:
            "print preview searchreplace autolink directionality visualblocks visualchars fullscreen image link media template code codesample table charmap hr pagebreak nonbreaking anchor insertdatetime advlist lists wordcount textpattern autosave emoticons indent2em",
        toolbar: [
            "fullscreen undo redo restoredraft | cut copy paste pastetext | forecolor backcolor bold italic underline strikethrough link anchor | alignleft aligncenter alignright alignjustify outdent indent | bullist numlist | blockquote subscript superscript removeformat powerpaste ",
            "styleselect formatselect fontselect fontsizeselect |  table image axupimgs media emoticons charmap hr pagebreak insertdatetime  selectall visualblocks searchreplace | code print preview | indent2em lineheight formatpainter",
        ],
        fontsize_formats: '12px 14px 16px 18px 24px 36px 48px 56px 72px',
        external_plugins: {
            powerpaste: `/js/tinymce/plugins/powerpaste/plugin.min.js` // ${this.baseUrl}
        },
        powerpaste_word_import: 'propmt', // 参数:propmt, merge, clear
        powerpaste_html_import: 'propmt', // 参数:propmt, merge, clear
        powerpaste_allow_local_images: true, // 粘贴图片
        paste_data_images: true,
        //图片固定宽高自定义样式
        content_style: 'body { background: #0d121c;color:#fff } p {margin: 5px 0;} img {max-width:100%;height:auto}',
        // 粘贴内容处理
        paste_postprocess: function (editor, fragment) {
            // 处理粘贴图片的跨域请求
            var imgs = fragment.node.getElementsByTagName('img');
            for (let i = 0; i < imgs.length; ++i) {
                imgs[i].crossOrigin = null;
            }
            // 处理粘贴视频
           var videos = fragment.node.getElementsByTagName('video');
           console.log("videos:", videos);
           for (let i = 0; i < videos.length; ++i) {
               videos[i].crossOrigin = null; // 防止跨域
               // 如果你想处理视频源路径、大小等属性，可以在这里加逻辑
           }
           // 处理粘贴文件
           var files = fragment.node.getElementsByTagName('a');
           for (let i = 0; i < files.length; ++i) {
               var href = files[i].getAttribute('href');
               if (href && (href.endsWith('.pdf') || href.endsWith('.txt') || href.endsWith('.zip'))) {
                   // 如果粘贴的是文件，处理相应逻辑，比如设置样式或者添加提示
                   files[i].style.color = '#2196F3'; // 设置文件链接颜色为蓝色
               }
           }
        },
        images_upload_handler: async (blobInfo, success, failure) => {
            try {
                const response = await uploadFile('/upload/tinymce.naii', blobInfo.blob(), blobInfo.filename(), {
                    size: "3MB",
                    exts: DEFAULT_EXTS.image
                });
                success(response.url);
            } catch (err) {
                failure(err.message || "文件上传失败");
            }
        },
        file_picker_types: "file image media",
        file_picker_callback: (callback, value, meta) => {
            const acceptType = DEFAULT_EXTS[meta.filetype] || "*";
            createFileInput(acceptType, async (file) => {
                try {
                    const response = await uploadFile('/upload/file.naii', file, file.name, {
                        accept: meta.filetype,
                        size: "100MB",
                        exts: acceptType
                    });
                    callback(response.url, { title: file.name });
                } catch (err) {
                    alert(err.message || "文件上传失败");
                }
            });
        }
    });

    /**
     * 创建文件输入框
     * @param {string} accept 文件接受类型
     * @param {function} callback 回调函数，返回用户选择的文件
     */
    function createFileInput(accept, callback) {
        const input = document.createElement("input");
        input.type = "file";
        input.accept = accept;
        input.style.display = "none";
        document.body.appendChild(input);

        input.addEventListener("change", () => {
            if (input.files && input.files.length > 0) {
                callback(input.files[0]);
            }
            document.body.removeChild(input);
        });

        input.click();
    }

    /**
     * 文件上传通用方法
     * @param {string} url 接口地址
     * @param {File} file 文件对象
     * @param {string} fileName 文件名
     * @param {object} params 附加参数
     * @returns {Promise<object>} 上传结果
     */
    async function uploadFile(url, file, fileName, params) {
        const formData = new FormData();
        formData.append("file", file);
        for (const key in params) {
            formData.append(key, params[key]);
        }
        const response = await fetch(url, {
            method: "POST",
            body: formData
        });
        if (!response.ok) {
            throw new Error(`HTTP Error: ${response.status}`);
        }
        const responseText = await response.text();
        let result;
             try {
                 result = JSON.parse(responseText);
             } catch (e) {
                 throw new Error("Failed to parse JSON response: " + e.message);
             }
             if (!result || !result.url) {
                 throw new Error(result.info);
             }
        return result;
    }

    /**
     * 保存编辑内容
     * @returns {string} 处理后的内容
     */
    function save() {
        const editorContent = tinymce.get('myEditor').getContent();
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = editorContent;

        // 设置图片样式
        const images = tempDiv.getElementsByTagName('img');
        for (const img of images) {
            img.style.maxWidth = '100%';
            img.style.height = 'auto';
        }

        return tempDiv.innerHTML;
    }

    // 页面加载完成后，如果有图片URL，则显示预览
    document.addEventListener('DOMContentLoaded', function() {
        try {
            // 处理标题图片预览
            var titlePicInput = document.getElementById('titlePicInput');
            var titlePicA = document.getElementById('titlePicA');
            var titlePicImg = document.getElementById('titlePicImg');
            
            if (titlePicInput && titlePicInput.value && titlePicInput.value.length > 0 && titlePicInput.value !== '{titlepicImage}') {
                var imageUrl = titlePicInput.value;
                titlePicA.href = imageUrl;
                titlePicImg.src = imageUrl + '?x-oss-process=image/resize,h_38';
                titlePicA.style.display = 'block';
                titlePicImg.style.display = 'block';
            }
            
            // 处理图集预览
            var photosHtml = document.getElementById('photosDefaultValue').innerHTML.trim();
            if (photosHtml && photosHtml.length > 0 && photosHtml !== '{news.extend.photos}') {
                // 如果是JSON数组格式
                if (photosHtml.startsWith('[') && photosHtml.endsWith(']')) {
                    var photos = JSON.parse(photosHtml);
                    for (var i = 0; i < photos.length; i++) {
                        if (photos[i] && photos[i].length > 0) {
                            var previewLink = document.getElementById('titlePicA' + i);
                            var previewImg = document.getElementById('titlePicImg' + i);
                            if (previewLink && previewImg) {
                                previewLink.href = photos[i];
                                previewImg.src = photos[i] + '?x-oss-process=image/resize,h_38';
                                previewLink.style.display = 'block';
                                previewImg.style.display = 'block';
                            }
                        }
                    }
                } else {
                    // 单个图片URL
                    if (photosHtml.length > 0) {
                        var previewLink = document.getElementById('titlePicA0');
                        var previewImg = document.getElementById('titlePicImg0');
                        if (previewLink && previewImg) {
                            previewLink.href = photosHtml;
                            previewImg.src = photosHtml + '?x-oss-process=image/resize,h_38';
                            previewLink.style.display = 'block';
                            previewImg.style.display = 'block';
                        }
                    }
                }
            }
            
            // 为标题图片上传按钮添加事件监听
            const uploadImagesButton = document.getElementById('uploadImagesButton');
            if (uploadImagesButton) {
                uploadImagesButton.addEventListener('click', function() {
                    // 获取相关元素
                    const titlePicInput = document.getElementById('titlePicInput');
                    const titlePicA = document.getElementById('titlePicA');
                    const titlePicImg = document.getElementById('titlePicImg');

                    // 当输入框有值时显示预览
                    if (titlePicInput && titlePicA && titlePicImg) {
                        // 这里可以添加上传逻辑
                    }
                });
            }

            // 为图集上传按钮添加事件监听
            const uploadImagesButtons = document.querySelectorAll('.uploadImagesButton');
            uploadImagesButtons.forEach(function(button) {
                button.addEventListener('click', function() {
                    const index = this.getAttribute('name');
                    const input = document.getElementById('titlePicInput' + index);
                    const previewLink = document.getElementById('titlePicA' + index);
                    const previewImg = document.getElementById('titlePicImg' + index);

                    // 当输入框有值时显示预览
                    if (input && previewLink && previewImg && input.value && input.value !== '') {
                        previewLink.style.display = 'block';
                        previewLink.href = input.value;
                        previewImg.src = input.value + '?x-oss-process=image/resize,h_38';
                    }
                });
            });
        } catch (e) {
            console.log('图片预览初始化出错:', e);
        }
    });

    /**
     * 保存编辑内容
     * @returns {string} 处理后的内容
     */
    function save() {
        const editorContent = tinymce.get('myEditor').getContent();
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = editorContent;

        // 设置图片样式
        const images = tempDiv.getElementsByTagName('img');
        for (const img of images) {
            img.style.maxWidth = '100%';
            img.style.height = 'auto';
        }

        return tempDiv.innerHTML;
    }
</script>
