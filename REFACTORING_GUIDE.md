# NAII Gateway 重构指导文档

## 概述

本文档提供了如何使用新创建的统一类来替换项目中重复代码的详细指导。通过这次重构，我们将显著减少代码重复，提高代码质量和可维护性。

## 重构内容

### 1. 全局配置类重构

#### 原有问题
项目中存在4个重复的全局配置类：
- `cn.edu.sjtu.gateway.admin.G`
- `cn.edu.sjtu.gateway.manager.G`
- `cn.edu.sjtu.gateway.domain.G`
- `cn.edu.sjtu.gateway.vm.Global`

#### 解决方案
创建统一的全局配置类：`cn.edu.sjtu.gateway.common.config.GlobalConfig`

#### 迁移步骤

1. **替换版本常量**
```java
// 原有代码
cn.edu.sjtu.gateway.admin.G.VERSION
cn.edu.sjtu.gateway.manager.G.VERSION

// 替换为
cn.edu.sjtu.gateway.common.config.GlobalConfig.Version.CURRENT_VERSION
```

2. **替换路径常量**
```java
// 原有代码
cn.edu.sjtu.gateway.admin.G.CACHE_FILE
cn.edu.sjtu.gateway.manager.G.CACHE_FILE

// 替换为
cn.edu.sjtu.gateway.common.config.GlobalConfig.Path.CACHE_FILE
```

3. **替换模板常量**
```java
// 原有代码
cn.edu.sjtu.gateway.admin.G.TEMPLATE_PC_DEFAULT

// 替换为
cn.edu.sjtu.gateway.common.config.GlobalConfig.Template.PC_DEFAULT
```

4. **替换域名管理方法**
```java
// 原有代码
cn.edu.sjtu.gateway.admin.G.getFirstAutoAssignDomain()

// 替换为
cn.edu.sjtu.gateway.common.config.GlobalConfig.getFirstAutoAssignDomain()
```

### 2. 工具类重构

#### 原有问题
- `cn.edu.sjtu.gateway.tools.StringUtil` (完整版本)
- `cn.edu.sjtu.gateway.admin.util.StringUtil` (简化版本)

#### 解决方案
创建统一的工具类：`cn.edu.sjtu.gateway.common.util.StringUtils`

#### 迁移步骤

1. **替换字符串比较方法**
```java
// 原有代码
cn.edu.sjtu.gateway.tools.StringUtil.StringEqual(s1, s2)

// 替换为
cn.edu.sjtu.gateway.common.util.StringUtils.equals(s1, s2)
```

2. **替换字符串验证方法**
```java
// 原有代码
cn.edu.sjtu.gateway.admin.util.StringUtil.isEnglishAndNumber(str)

// 替换为
cn.edu.sjtu.gateway.common.util.StringUtils.isEnglishAndNumber(str)
```

3. **替换XSS过滤方法**
```java
// 原有代码
cn.edu.sjtu.gateway.tools.StringUtil.filterXss(text)

// 替换为
cn.edu.sjtu.gateway.common.util.StringUtils.filterXss(text)
```

### 3. 日期工具类重构

#### 解决方案
创建统一的日期工具类：`cn.edu.sjtu.gateway.common.util.DateUtils`

#### 迁移步骤

1. **替换时间戳方法**
```java
// 原有代码
cn.edu.sjtu.gateway.tools.DateUtil.timeForUnix10()

// 替换为
cn.edu.sjtu.gateway.common.util.DateUtils.currentTimeSeconds()
```

2. **替换日期格式化方法**
```java
// 原有代码
cn.edu.sjtu.gateway.tools.DateUtil.intToString(unixTime, format)

// 替换为
cn.edu.sjtu.gateway.common.util.DateUtils.formatUnixTime(unixTime, format)
```

### 4. 文件工具类重构

#### 解决方案
创建统一的文件工具类：`cn.edu.sjtu.gateway.common.util.FileUtils`

#### 迁移步骤

1. **替换文件读取方法**
```java
// 原有代码
cn.edu.sjtu.gateway.tools.file.FileUtil.read(path)

// 替换为
cn.edu.sjtu.gateway.common.util.FileUtils.readFile(path)
```

2. **替换文件写入方法**
```java
// 原有代码
cn.edu.sjtu.gateway.tools.file.FileUtil.write(path, content)

// 替换为
cn.edu.sjtu.gateway.common.util.FileUtils.writeFile(path, content)
```

### 5. 实体类重构

#### 原有问题
- `cn.edu.sjtu.gateway.supermanager.bean.Application`
- `cn.edu.sjtu.gateway.superadmin.bean.Application`

#### 解决方案
创建统一的实体类：
- `cn.edu.sjtu.gateway.common.entity.BaseEntity` (基础实体)
- `cn.edu.sjtu.gateway.common.entity.Application` (应用实体)

#### 迁移步骤

1. **继承基础实体类**
```java
// 原有代码
public class MyEntity {
    private Integer id;
    private Date createTime;
    // ...
}

// 替换为
public class MyEntity extends BaseEntity {
    // 只需要定义业务特有字段
}
```

2. **使用统一的Application实体**
```java
// 原有代码
cn.edu.sjtu.gateway.supermanager.bean.Application

// 替换为
cn.edu.sjtu.gateway.common.entity.Application
```

### 6. 控制器重构

#### 解决方案
创建统一的控制器基类：`cn.edu.sjtu.gateway.common.controller.BaseController`

#### 迁移步骤

1. **继承基础控制器**
```java
// 原有代码
@Controller
public class MyController {
    // 重复的通用方法
}

// 替换为
@Controller
public class MyController extends BaseController {
    // 只需要定义业务特有方法
}
```

2. **使用统一的响应方法**
```java
// 原有代码
model.addAttribute("state", 1);
model.addAttribute("info", "操作成功");
return "common/prompt";

// 替换为
return success(model, "操作成功", redirectUrl);
```

### 7. 响应结果重构

#### 解决方案
创建统一的响应类：
- `cn.edu.sjtu.gateway.common.response.Result`
- `cn.edu.sjtu.gateway.common.response.PageResult`

#### 迁移步骤

1. **使用统一的API响应**
```java
// 原有代码
@ResponseBody
public Map<String, Object> getData() {
    Map<String, Object> result = new HashMap<>();
    result.put("code", 200);
    result.put("message", "成功");
    result.put("data", data);
    return result;
}

// 替换为
@ResponseBody
public Result<DataType> getData() {
    return Result.success(data);
}
```

2. **使用统一的分页响应**
```java
// 原有代码
@ResponseBody
public Map<String, Object> getPageData() {
    Map<String, Object> result = new HashMap<>();
    result.put("records", records);
    result.put("total", total);
    result.put("current", current);
    result.put("size", size);
    return result;
}

// 替换为
@ResponseBody
public PageResult<DataType> getPageData() {
    return PageResult.of(records, total, current, size);
}
```

### 8. 异常处理重构

#### 解决方案
创建统一的异常处理：
- `cn.edu.sjtu.gateway.common.exception.BusinessException`
- `cn.edu.sjtu.gateway.common.exception.SystemException`
- `cn.edu.sjtu.gateway.common.exception.GlobalExceptionHandler`

#### 迁移步骤

1. **使用业务异常**
```java
// 原有代码
if (user == null) {
    throw new RuntimeException("用户不存在");
}

// 替换为
if (user == null) {
    throw BusinessException.userNotFound();
}
```

2. **使用系统异常**
```java
// 原有代码
try {
    // 数据库操作
} catch (Exception e) {
    throw new RuntimeException("数据库操作失败");
}

// 替换为
try {
    // 数据库操作
} catch (Exception e) {
    throw SystemException.databaseOperationError(e.getMessage());
}
```

## 重构优先级

### 高优先级（立即执行）
1. 全局配置类重构 - 影响面广，收益大
2. 工具类重构 - 使用频率高，重复度高
3. 异常处理重构 - 提升系统稳定性

### 中优先级（逐步执行）
1. 控制器基类重构 - 减少重复代码
2. 响应结果重构 - 统一API格式
3. 实体类重构 - 规范数据模型

### 低优先级（后续优化）
1. 包结构调整 - 优化项目结构
2. 模块整合 - 合并重复模块

## 重构注意事项

### 1. 向后兼容
- 保留原有类的引用，标记为@Deprecated
- 提供迁移期的适配器类
- 逐步替换，避免一次性大改动

### 2. 测试验证
- 每个重构步骤都要进行充分测试
- 重点测试核心业务功能
- 确保性能不受影响

### 3. 文档更新
- 及时更新API文档
- 更新开发规范文档
- 提供重构指导培训

### 4. 代码审查
- 重构代码必须经过代码审查
- 确保符合新的编码规范
- 验证重构的正确性

## 重构收益

### 代码质量提升
- **代码重复率降低**: 预计减少30-40%的重复代码
- **维护成本降低**: 统一管理，减少维护工作量
- **代码可读性提升**: 清晰的模块结构和命名规范

### 开发效率提升
- **开发效率提升**: 统一的工具类和基类
- **测试效率提升**: 减少重复测试代码
- **部署效率提升**: 简化的模块结构

### 系统性能优化
- **包大小减少**: 预计减少10-15%的包大小
- **启动速度提升**: 减少重复类加载
- **内存占用优化**: 减少重复对象创建

## 实施计划

### 第1周：基础重构
- [ ] 创建统一的全局配置类
- [ ] 创建统一的工具类
- [ ] 创建统一的实体基类

### 第2周：架构重构
- [ ] 创建统一的控制器基类
- [ ] 创建统一的响应类
- [ ] 创建统一的异常处理

### 第3周：代码迁移
- [ ] 迁移全局配置引用
- [ ] 迁移工具类引用
- [ ] 迁移实体类定义

### 第4周：测试验证
- [ ] 功能测试
- [ ] 性能测试
- [ ] 兼容性测试

### 第5周：文档更新
- [ ] 更新API文档
- [ ] 更新开发规范
- [ ] 编写迁移指南

## 总结

通过这次系统性的重构，NAII Gateway项目将获得：
- 更清晰的代码结构
- 更高的代码质量
- 更好的可维护性
- 更强的扩展性

这将为项目的长期发展奠定坚实的技术基础，提升团队的开发效率和代码质量。
