<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.edu.sjtu.gateway</groupId>
    <artifactId>naii-gateway</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>naii-gateway</name>
    <description>上海交通大学宁波人工智能研究院门户内容管理系统</description>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.6</version>
    </parent>
    <properties>
        <java.version>17</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <!-- 编译时的编码 -->
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <spring-boot.version>2.7.6</spring-boot.version>
    </properties>

    <dependencies>
        <!-- NAII系列依赖 -->
        <dependency>
            <groupId>cn.edu.sjtu.gateway</groupId>
            <artifactId>naii-http</artifactId>
            <version>naii-1.1.1</version>
        </dependency>
        <dependency>
            <groupId>cn.edu.sjtu.gateway</groupId>
            <artifactId>naii-javadoc</artifactId>
            <version>naii-1.1.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-beanutils</artifactId>
                    <groupId>commons-beanutils</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>ezmorph</artifactId>
                    <groupId>net.sf.ezmorph</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>json-lib</artifactId>
                    <groupId>net.sf.json-lib</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>eu.bitwalker</groupId>
            <artifactId>UserAgentUtils</artifactId>
            <version>1.21</version> <!-- 请检查是否有更新的版本 -->
        </dependency>
        <!-- sftp 支持 -->
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>0.1.55</version>
            <scope>provided</scope>
        </dependency>
        <!-- ssh 支持 -->
        <dependency>
            <groupId>ch.ethz.ganymed</groupId>
            <artifactId>ganymed-ssh2</artifactId>
            <version>262</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.8.0</version> <!-- 请检查是否有更新的版本 -->
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.2.1</version> <!-- 选择一个合适的版本 -->
        </dependency>
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-ehcache</artifactId>
            <version>1.8.0</version>
        </dependency>
        <dependency>
            <groupId>cn.edu.sjtu.gateway</groupId>
            <artifactId>naii-autopublish</artifactId>
            <version>naii-1.1.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.11.0</version> <!-- 请检查是否有更新的版本 -->
        </dependency>
        <dependency>
            <groupId>commons-configuration</groupId>
            <artifactId>commons-configuration</artifactId>
            <version>1.10</version> <!-- 请检查是否有更新的版本 -->
        </dependency>
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-core</artifactId>
            <version>1.8.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-beanutils</artifactId>
                    <groupId>commons-beanutils</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-web</artifactId>
            <version>1.8.0</version> <!-- 请使用最新的版本号 -->
        </dependency>
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-spring</artifactId>
            <version>1.8.0</version>
        </dependency>
        <dependency>
            <groupId>org.crazycake</groupId>
            <artifactId>shiro-redis</artifactId>
            <version>3.3.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-beanutils</artifactId>
                    <groupId>commons-beanutils</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jsr305</artifactId>
                    <groupId>com.google.code.findbugs</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>plexus-component-annotations</artifactId>
                    <groupId>org.codehaus.plexus</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>plexus-utils</artifactId>
                    <groupId>org.codehaus.plexus</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-io</artifactId>
                    <groupId>commons-io</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>doxia-decoration-model</artifactId>
                    <groupId>org.apache.maven.doxia</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>doxia-logging-api</artifactId>
                    <groupId>org.apache.maven.doxia</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-artifact</artifactId>
                    <groupId>org.apache.maven</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-model</artifactId>
                    <groupId>org.apache.maven</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-plugin-api</artifactId>
                    <groupId>org.apache.maven</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>plexus-interpolation</artifactId>
                    <groupId>org.codehaus.plexus</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>doxia-core</artifactId>
                    <groupId>org.apache.maven.doxia</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>plexus-velocity</artifactId>
                    <groupId>org.codehaus.plexus</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-digester</artifactId>
                    <groupId>commons-digester</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>velocity</artifactId>
                    <groupId>org.apache.velocity</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>doxia-sink-api</artifactId>
                    <groupId>org.apache.maven.doxia</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>doxia-site-renderer</artifactId>
                    <groupId>org.apache.maven.doxia</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-shared-utils</artifactId>
                    <groupId>org.apache.maven.shared</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>qdox</artifactId>
                    <groupId>com.thoughtworks.qdox</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>plexus-archiver</artifactId>
                    <groupId>org.codehaus.plexus</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-core</artifactId>
                    <groupId>org.apache.maven</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>plexus-container-default</artifactId>
                    <groupId>org.codehaus.plexus</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-repository-metadata</artifactId>
                    <groupId>org.apache.maven</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>wagon-provider-api</artifactId>
                    <groupId>org.apache.maven.wagon</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-settings</artifactId>
                    <groupId>org.apache.maven</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>plexus-java</artifactId>
                    <groupId>org.codehaus.plexus</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>shiro-core</artifactId>
                    <groupId>org.apache.shiro</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>maven-common-artifact-filters</artifactId>
                    <groupId>org.apache.maven.shared</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>5.3.20</version>
        </dependency>
        <dependency>
            <groupId>cn.edu.sjtu.gateway</groupId>
            <artifactId>naii-template</artifactId>
            <version>naii-1.1.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-configuration2</artifactId>
            <version>2.7</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <version>2.7.6</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <artifactId>spring-web</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.edu.sjtu.gateway</groupId>
            <artifactId>naii-framework</artifactId>
            <version>naii-1.1.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-core</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.33</version> <!-- 请使用最新的版本 -->
        </dependency>
        <dependency>
            <groupId>cn.edu.sjtu.gateway</groupId>
            <artifactId>naii-log-core</artifactId>
            <version>naii-1.1.1</version>
        </dependency>
        <dependency>
            <groupId>cn.edu.sjtu.gateway</groupId>
            <artifactId>naii-page</artifactId>
            <version>naii-1.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>2.0.19.graal</version>
        </dependency>
        <dependency>
            <groupId>javax.persistence</groupId>
            <artifactId>javax.persistence-api</artifactId>
            <version>2.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.ant</groupId>
            <artifactId>ant</artifactId>
            <version>1.10.11</version> <!-- 或者当前最新版本 -->
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-jasper</artifactId>
        </dependency>
        <!-- JSTL -->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>jstl</artifactId>
        </dependency>
        <!-- JSTL实现包 -->
        <dependency>
            <groupId>org.apache.taglibs</groupId>
            <artifactId>taglibs-standard-impl</artifactId>
            <version>1.2.5</version>
        </dependency>
    </dependencies>
    <build>
        <finalName>naii-gateway</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>1.4.2.RELEASE</version>
                <configuration>
                    <!-- 1.4.2.RELEASE导致多个main方法的情况下需要指定主类 -->
                    <mainClass>cn.edu.sjtu.gateway.Application</mainClass>
                    <skip>false</skip>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- 用于将静态资源复制到指定的目录 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/static-resources</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>${project.basedir}/src/main/resources/static</directory>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- 用于创建 tar.gz 包 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <!-- 创建 naii 目录的 tar.gz 包 -->
                                <tar destfile="${project.build.directory}/naii-static-resources.tar">
                                    <fileset dir="${project.build.directory}/static-resources/naii"/>
                                </tar>

                                <!-- 创建 news 目录的 tar.gz 包 -->
                                <tar destfile="${project.build.directory}/news-static-resources.tar">
                                    <fileset dir="${project.build.directory}/static-resources/news"/>
                                </tar>
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

        </plugins>

        <resources>
            <!-- 打包时将jsp文件拷贝到META-INF目录下-->
            <resource>
                <!-- 指定resources插件处理哪个目录下的资源文件 -->
                <directory>src/main/webapp</directory>
                <!--注意此次必须要放在此目录下才能被访问到-->
                <targetPath>META-INF/resources</targetPath>
                <includes>
                    <include>**/**</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/**</include>
                </includes>
                <filtering>false</filtering>
            </resource>

        </resources>
    </build>
</project>
