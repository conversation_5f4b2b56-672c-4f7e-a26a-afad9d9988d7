#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NAII Gateway 代码迁移脚本
自动化更新代码引用，从旧的类迁移到新的统一框架

使用方法:
python migration-script.py --dry-run  # 预览更改
python migration-script.py --execute  # 执行更改
"""

import os
import re
import sys
import argparse
from pathlib import Path
from typing import List, Tuple, Dict

class MigrationScript:
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.src_root = self.project_root / "src" / "main" / "java"
        self.changes_made = []
        
        # 定义迁移规则
        self.migration_rules = {
            # 全局配置类迁移
            'global_config': [
                # Import语句替换
                (r'import\s+cn\.edu\.sjtu\.gateway\.manager\.G;', 
                 'import cn.edu.sjtu.gateway.common.config.GlobalConfig;\nimport cn.edu.sjtu.gateway.common.manager.DomainManager;'),
                (r'import\s+cn\.edu\.sjtu\.gateway\.admin\.G;', 
                 'import cn.edu.sjtu.gateway.common.config.GlobalConfig;\nimport cn.edu.sjtu.gateway.common.manager.DomainManager;'),
                (r'import\s+cn\.edu\.sjtu\.gateway\.domain\.G;', 
                 'import cn.edu.sjtu.gateway.common.config.GlobalConfig;\nimport cn.edu.sjtu.gateway.common.manager.DomainManager;'),
                
                # 版本常量替换
                (r'\bG\.VERSION\b', 'GlobalConfig.Version.CURRENT_VERSION'),
                
                # 路径常量替换
                (r'\bG\.CACHE_FILE\b', 'GlobalConfig.Path.CACHE_FILE'),
                (r'\bG\.DEFAULT_SITE_COLUMN_ICON_URL\b', 'GlobalConfig.Path.DEFAULT_SITE_COLUMN_ICON_URL'),
                
                # 模板常量替换
                (r'\bG\.TEMPLATE_PC_DEFAULT\b', 'GlobalConfig.Template.PC_DEFAULT'),
                (r'\bG\.TEMPLATE_WAP_DEFAULT\b', 'GlobalConfig.Template.WAP_DEFAULT'),
                (r'\bG\.PAGE_WAP_NUM\b', 'GlobalConfig.Template.PAGE_WAP_NUM'),
                
                # 图片尺寸常量替换
                (r'\bG\.SITECOLUMN_ICON_MAXWIDTH\b', 'GlobalConfig.ImageSize.SITECOLUMN_ICON_MAXWIDTH'),
                (r'\bG\.CAROUSEL_MAXWIDTH\b', 'GlobalConfig.ImageSize.CAROUSEL_MAXWIDTH'),
                (r'\bG\.NEWS_TITLEPIC_MAXWIDTH\b', 'GlobalConfig.ImageSize.NEWS_TITLEPIC_MAXWIDTH'),
                
                # 业务常量替换
                (r'\bG\.agencyAddSubAgency_siteSize\b', 'GlobalConfig.Business.AGENCY_ADD_SUB_AGENCY_SITE_SIZE'),
                (r'\bG\.REG_GENERAL_OSS_HAVE\b', 'GlobalConfig.Business.REG_GENERAL_OSS_HAVE'),
                (r'\bG\.copyright\b', 'GlobalConfig.Business.copyright'),
                
                # 域名管理方法替换
                (r'\bG\.putDomain\(', 'DomainManager.putDomain('),
                (r'\bG\.putBindDomain\(', 'DomainManager.putBindDomain('),
                (r'\bG\.getDomain\(', 'DomainManager.getDomain('),
                (r'\bG\.getBindDomain\(', 'DomainManager.getBindDomain('),
                (r'\bG\.getDomainSize\(\)', 'DomainManager.getDomainSize()'),
                (r'\bG\.getBindDomainSize\(\)', 'DomainManager.getBindDomainSize()'),
                (r'\bG\.getAutoAssignDomain\(\)', 'GlobalConfig.getAutoAssignDomain()'),
                (r'\bG\.getFirstAutoAssignDomain\(\)', 'GlobalConfig.getFirstAutoAssignDomain()'),
                (r'\bG\.getCarouselPath\(', 'GlobalConfig.getCarouselPath('),
            ],
            
            # 工具类迁移
            'string_utils': [
                # Import语句替换
                (r'import\s+cn\.edu\.sjtu\.gateway\.tools\.StringUtil;', 
                 'import cn.edu.sjtu.gateway.common.util.StringUtils;'),
                (r'import\s+cn\.edu\.sjtu\.gateway\.admin\.util\.StringUtil;', 
                 'import cn.edu.sjtu.gateway.common.util.StringUtils;'),
                
                # 方法调用替换
                (r'\bStringUtil\.StringEqual\(', 'StringUtils.equals('),
                (r'\bStringUtil\.isEnglishAndNumber\(', 'StringUtils.isEnglishAndNumber('),
                (r'\bStringUtil\.getRandomAZ\(', 'StringUtils.getRandomAZ('),
                (r'\bStringUtil\.getRandom09AZ\(', 'StringUtils.getRandom09AZ('),
                (r'\bStringUtil\.filterXss\(', 'StringUtils.filterXss('),
                (r'\bStringUtil\.encrypt\(', 'StringUtils.encrypt('),
                (r'\bStringUtil\.stringToUrl\(', 'StringUtils.stringToUrl('),
                (r'\bStringUtil\.stringToInputStream\(', 'StringUtils.stringToInputStream('),
            ],
            
            'date_utils': [
                # Import语句替换
                (r'import\s+cn\.edu\.sjtu\.gateway\.tools\.DateUtil;', 
                 'import cn.edu.sjtu.gateway.common.util.DateUtils;'),
                (r'import\s+cn\.edu\.sjtu\.tools\.DateUtil;', 
                 'import cn.edu.sjtu.gateway.common.util.DateUtils;'),
                
                # 方法调用替换
                (r'\bDateUtil\.timeForUnix10\(\)', 'DateUtils.currentTimeSeconds()'),
                (r'\bDateUtil\.intToString\(', 'DateUtils.formatUnixTime('),
                (r'\bDateUtil\.dateFormat\(', 'DateUtils.formatTimestamp('),
                (r'\bDateUtil\.currentDateTime\(\)', 'DateUtils.currentDateTime()'),
            ],
            
            'file_utils': [
                # Import语句替换
                (r'import\s+cn\.edu\.sjtu\.gateway\.tools\.file\.FileUtil;', 
                 'import cn.edu.sjtu.gateway.common.util.FileUtils;'),
                
                # 方法调用替换
                (r'\bFileUtil\.read\(', 'FileUtils.readFile('),
                (r'\bFileUtil\.write\(', 'FileUtils.writeFile('),
                (r'\bFileUtil\.deleteFile\(', 'FileUtils.delete('),
                (r'\bFileUtil\.exists\(', 'FileUtils.exists('),
            ],
            
            # 异常处理迁移
            'exceptions': [
                # 常见的RuntimeException替换
                (r'throw\s+new\s+RuntimeException\("用户不存在"\)', 
                 'throw BusinessException.userNotFound()'),
                (r'throw\s+new\s+RuntimeException\("参数错误[^"]*"\)', 
                 'throw BusinessException.paramError("参数错误")'),
                (r'throw\s+new\s+RuntimeException\("数据库[^"]*"\)', 
                 'throw SystemException.databaseOperationError("数据库操作失败")'),
                (r'throw\s+new\s+RuntimeException\("文件[^"]*"\)', 
                 'throw SystemException.ioError("文件操作失败")'),
                
                # 需要添加import的情况
                (r'BusinessException\.', 'BusinessException.'),  # 检查是否需要添加import
                (r'SystemException\.', 'SystemException.'),      # 检查是否需要添加import
            ]
        }
    
    def find_java_files(self) -> List[Path]:
        """查找所有Java文件"""
        java_files = []
        for root, dirs, files in os.walk(self.src_root):
            for file in files:
                if file.endswith('.java'):
                    java_files.append(Path(root) / file)
        return java_files
    
    def read_file(self, file_path: Path) -> str:
        """读取文件内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            # 尝试其他编码
            with open(file_path, 'r', encoding='gbk') as f:
                return f.read()
    
    def write_file(self, file_path: Path, content: str):
        """写入文件内容"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def apply_migration_rules(self, content: str, file_path: Path) -> Tuple[str, List[str]]:
        """应用迁移规则"""
        modified_content = content
        changes = []
        
        for category, rules in self.migration_rules.items():
            for pattern, replacement in rules:
                matches = re.findall(pattern, modified_content)
                if matches:
                    modified_content = re.sub(pattern, replacement, modified_content)
                    changes.append(f"{category}: {pattern} -> {replacement}")
        
        # 检查是否需要添加import语句
        modified_content = self.add_missing_imports(modified_content, file_path)
        
        return modified_content, changes
    
    def add_missing_imports(self, content: str, file_path: Path) -> str:
        """添加缺失的import语句"""
        lines = content.split('\n')
        imports_to_add = []
        
        # 检查是否使用了新的类但没有import
        if 'GlobalConfig.' in content and 'import cn.edu.sjtu.gateway.common.config.GlobalConfig;' not in content:
            imports_to_add.append('import cn.edu.sjtu.gateway.common.config.GlobalConfig;')
        
        if 'DomainManager.' in content and 'import cn.edu.sjtu.gateway.common.manager.DomainManager;' not in content:
            imports_to_add.append('import cn.edu.sjtu.gateway.common.manager.DomainManager;')
        
        if 'StringUtils.' in content and 'import cn.edu.sjtu.gateway.common.util.StringUtils;' not in content:
            imports_to_add.append('import cn.edu.sjtu.gateway.common.util.StringUtils;')
        
        if 'DateUtils.' in content and 'import cn.edu.sjtu.gateway.common.util.DateUtils;' not in content:
            imports_to_add.append('import cn.edu.sjtu.gateway.common.util.DateUtils;')
        
        if 'FileUtils.' in content and 'import cn.edu.sjtu.gateway.common.util.FileUtils;' not in content:
            imports_to_add.append('import cn.edu.sjtu.gateway.common.util.FileUtils;')
        
        if 'BusinessException.' in content and 'import cn.edu.sjtu.gateway.common.exception.BusinessException;' not in content:
            imports_to_add.append('import cn.edu.sjtu.gateway.common.exception.BusinessException;')
        
        if 'SystemException.' in content and 'import cn.edu.sjtu.gateway.common.exception.SystemException;' not in content:
            imports_to_add.append('import cn.edu.sjtu.gateway.common.exception.SystemException;')
        
        # 添加import语句
        if imports_to_add:
            # 找到package语句后的位置
            package_line = -1
            last_import_line = -1
            
            for i, line in enumerate(lines):
                if line.strip().startswith('package '):
                    package_line = i
                elif line.strip().startswith('import '):
                    last_import_line = i
            
            # 在最后一个import后添加新的import
            insert_position = last_import_line + 1 if last_import_line > -1 else package_line + 2
            
            for import_stmt in reversed(imports_to_add):
                lines.insert(insert_position, import_stmt)
        
        return '\n'.join(lines)
    
    def process_file(self, file_path: Path, dry_run: bool = True) -> bool:
        """处理单个文件"""
        try:
            original_content = self.read_file(file_path)
            modified_content, changes = self.apply_migration_rules(original_content, file_path)
            
            if original_content != modified_content:
                relative_path = file_path.relative_to(self.project_root)
                
                if dry_run:
                    print(f"\n📝 {relative_path}")
                    for change in changes:
                        print(f"   - {change}")
                else:
                    self.write_file(file_path, modified_content)
                    print(f"✅ 已更新: {relative_path}")
                    self.changes_made.append(str(relative_path))
                
                return True
            
            return False
            
        except Exception as e:
            print(f"❌ 处理文件失败 {file_path}: {e}")
            return False
    
    def run_migration(self, dry_run: bool = True):
        """运行迁移"""
        print("🚀 开始代码迁移...")
        print(f"📁 项目根目录: {self.project_root}")
        print(f"📂 源码目录: {self.src_root}")
        print(f"🔍 模式: {'预览模式' if dry_run else '执行模式'}")
        print("-" * 60)
        
        java_files = self.find_java_files()
        print(f"📋 找到 {len(java_files)} 个Java文件")
        
        modified_files = 0
        
        for file_path in java_files:
            # 跳过备份文件和新创建的统一框架文件
            if (file_path.name.endswith('.backup') or 
                'common' in str(file_path) or
                file_path.name.endswith('.new')):
                continue
            
            if self.process_file(file_path, dry_run):
                modified_files += 1
        
        print("-" * 60)
        print(f"📊 迁移统计:")
        print(f"   - 总文件数: {len(java_files)}")
        print(f"   - 需要修改: {modified_files}")
        print(f"   - 无需修改: {len(java_files) - modified_files}")
        
        if not dry_run and self.changes_made:
            print(f"\n✅ 成功更新了 {len(self.changes_made)} 个文件:")
            for file_path in self.changes_made:
                print(f"   - {file_path}")
        
        if dry_run:
            print(f"\n💡 这是预览模式，没有实际修改文件")
            print(f"   要执行迁移，请运行: python {sys.argv[0]} --execute")
        else:
            print(f"\n🎉 迁移完成！")
            print(f"   建议运行测试验证功能是否正常")

def main():
    parser = argparse.ArgumentParser(description='NAII Gateway 代码迁移脚本')
    parser.add_argument('--dry-run', action='store_true', 
                       help='预览模式，不实际修改文件')
    parser.add_argument('--execute', action='store_true', 
                       help='执行模式，实际修改文件')
    parser.add_argument('--project-root', default='.', 
                       help='项目根目录路径')
    
    args = parser.parse_args()
    
    if not args.dry_run and not args.execute:
        print("❌ 请指定运行模式: --dry-run 或 --execute")
        sys.exit(1)
    
    if args.dry_run and args.execute:
        print("❌ 不能同时指定 --dry-run 和 --execute")
        sys.exit(1)
    
    project_root = os.path.abspath(args.project_root)
    if not os.path.exists(project_root):
        print(f"❌ 项目根目录不存在: {project_root}")
        sys.exit(1)
    
    migration = MigrationScript(project_root)
    migration.run_migration(dry_run=args.dry_run)

if __name__ == '__main__':
    main()
