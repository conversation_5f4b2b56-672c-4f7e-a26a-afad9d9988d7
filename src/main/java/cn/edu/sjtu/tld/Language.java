package cn.edu.sjtu.tld;


import cn.edu.sjtu.gateway.vm.util.LanguageUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.jsp.JspException;
import javax.servlet.jsp.JspWriter;
import javax.servlet.jsp.tagext.TagSupport;
import java.io.IOException;

/**
 * 调用语言包显示
 * <AUTHOR>
 */
@Setter
@Getter
@Slf4j
public class Language extends TagSupport {
	private String key;		//要调用的语言文字的key值
	private String remark;	//备注，程序中无任何作用，只是给开发人员自己看

    @Override
	public int doEndTag() throws JspException {
		JspWriter writer = pageContext.getOut();
		try {
			writer.print(LanguageUtil.show(key));
		} catch (IOException e) {
			log.error("错误信息：------>"+e);
		}
		return super.doEndTag();
	}
	
}
