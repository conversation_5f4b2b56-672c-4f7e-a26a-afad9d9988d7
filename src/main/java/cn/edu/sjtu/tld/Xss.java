package cn.edu.sjtu.tld;

import cn.edu.sjtu.gateway.tools.StringUtil;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.jsp.JspException;
import javax.servlet.jsp.JspWriter;
import javax.servlet.jsp.tagext.TagSupport;
import java.io.IOException;

/**
 * Xss 处理
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class Xss extends TagSupport {
    private static final Logger log = LoggerFactory.getLogger(Xss.class);
    //显示的字符串
    private String text;

    @Override
    public int doEndTag() throws JspException {
        if (this.text != null) {
            this.text = StringUtil.filterXss(text);
        }
        JspWriter writer = pageContext.getOut();
        try {
            writer.print(this.text);
        } catch (IOException e) {
            log.error("错误信息：------>", e);
        }
        return super.doEndTag();
    }
}
