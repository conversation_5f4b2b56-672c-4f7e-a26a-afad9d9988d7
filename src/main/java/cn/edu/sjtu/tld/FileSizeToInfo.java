package cn.edu.sjtu.tld;


import cn.edu.sjtu.gateway.tools.Lang;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.jsp.JspException;
import javax.servlet.jsp.JspWriter;
import javax.servlet.jsp.tagext.TagSupport;
import java.io.IOException;

/**
 * 存储空间，B、KB、MB、GB的显示，传入基本的B，然后自动计算是KB、MB、GB显示
 * <br/>传入字节数剧，返回大小的描述信息，小数点后保留两位。
 * <br/>如传入：1234435742，返回：1.15 GB
 * <AUTHOR>
 */
@Setter
@Getter
@Slf4j
public class FileSizeToInfo extends TagSupport {
	//传入的以B为单位的长度
	private long size;

    @Override
	public int doEndTag() throws JspException {
		JspWriter writer = pageContext.getOut();
		try {
			writer.print(Lang.fileSizeToInfo(size));
		} catch (IOException e) {
			log.error("错误信息：------>"+e);
		}
		return super.doEndTag();
	}
	
}
