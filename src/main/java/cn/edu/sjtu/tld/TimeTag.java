package cn.edu.sjtu.tld;


import cn.edu.sjtu.gateway.tools.DateUtil;
import cn.edu.sjtu.gateway.tools.exception.NotReturnValueException;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.jsp.JspException;
import javax.servlet.jsp.JspWriter;
import javax.servlet.jsp.tagext.TagSupport;
import java.io.IOException;

/**
 * 时间转换标签，将linux转换为文字描述的时间
 * <br/><b>需xnx3.jar （ https://github.com/xnx3/xnx3 ）</b>
 * <AUTHOR>
 *
 */
@Setter
@Slf4j
@Getter
public class TimeTag extends TagSupport {
	private static final String FORMAT_DEFAULT="yyyy-MM-dd HH:mm:ss";	//如果format没有传递过来，会使用这个默认的时间戳
	
    private long linuxTime;										//Linux时间戳，10位或者13位
    /**
     * -- SETTER --
     *  yyyy-MM-dd HH:mm:ss
     *
     * @param format
     */
    private String format;										//转换格式 ,若不填，默认为yyyy-MM-dd HH:mm:ss
	
	public String getFormat() {
		if(this.format == null || this.format.isEmpty()) {
			this.format = FORMAT_DEFAULT;
		}
		return format;
	}

    @Override
	public int doEndTag() throws JspException {
		// TODO Auto-generated method stub
		String date="";
		try {
			date = DateUtil.dateFormat(getLinuxTime(), getFormat());
		} catch (NotReturnValueException e1) {
			date = e1.getMessage();
		}
		
		JspWriter writer = pageContext.getOut();
		try {
			writer.print(date);
		} catch (IOException e) {
			log.error("错误信息：------>"+e);
		}
		
		return super.doEndTag();
	}
	
	
}
