package cn.edu.sjtu.tld;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.jsp.JspException;
import javax.servlet.jsp.JspWriter;
import javax.servlet.jsp.tagext.TagSupport;
import java.io.IOException;

/**
 * 输出图片时，图片路径的判断，判断其是相对路径还是绝对路径。如果是相对路径，则加入网址前缀，拼接为绝对路径
 * <AUTHOR>
 */
@Setter
@Getter
@Slf4j
public class ImgUrl extends TagSupport {
	private String prefixUrl;	//如果图片路径img是一个相对路径，则加入此作为网址前缀，拼接为绝对路径
	private String img;			//图片路径

    @Override
	public int doEndTag() throws JspException {
		JspWriter writer = pageContext.getOut();
		String url = img;
		if(!url.contains("://")){
			url = this.prefixUrl+url;
		}
		
		try {
			writer.print(url);
		} catch (IOException e) {
			log.error("错误信息：------>"+e);
		}
		return super.doEndTag();
	}
	
}
