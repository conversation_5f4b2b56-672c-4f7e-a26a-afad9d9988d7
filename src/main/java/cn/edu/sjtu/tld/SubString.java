package cn.edu.sjtu.tld;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.jsp.JspException;
import javax.servlet.jsp.JspWriter;
import javax.servlet.jsp.tagext.TagSupport;
import java.io.IOException;

/**
 * 字符串截取
 * <AUTHOR>
 */
@Setter
@Getter
@Slf4j
public class SubString extends TagSupport {
	private final static String DEFAULT_MORE="...";	//如果末尾更多字符没有传递过来，会使用这个
	
	private String text;							//原本的字符串
	private int maxLength;							//显示的最大长度
	private String more;							//若超过最大长度，裁减的字符串会加上此输出。若不填默认使用 ...

    @Override
	public int doEndTag() throws JspException {
		String content = "";
		
		if(more == null){
			more = DEFAULT_MORE;
		}
		
		if(this.text != null){
			if(this.text.length()>maxLength){
				content = text.substring(0, maxLength)+more;
			}else{
				content = text;
			}
		}
		JspWriter writer = pageContext.getOut();
		try {
			writer.print(content);
		} catch (IOException e) {
			log.error("错误信息：------>"+e);
		}
		return super.doEndTag();
	}
}
