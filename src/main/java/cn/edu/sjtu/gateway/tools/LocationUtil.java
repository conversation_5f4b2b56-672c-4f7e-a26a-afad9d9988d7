package cn.edu.sjtu.gateway.tools;

/**
 * 位置、定位相关
 * <AUTHOR>
 */
public class LocationUtil {
	private static final double EARTH_RADIUS = 6378.137;
    
    private static double rad(double d) {    
        return d * Math.PI / 180.0;    
    }    
    
    /**   
     * 通过经纬度获取距离(单位：米)   
     * @param lat1 坐标点1的纬度latitude
     * @param lng1 坐标点1的经度longitude
     * @param lat2 坐标点2的纬度latitude
     * @param lng2 坐标点2的经度longitude
     * @return 两者相差多少米
     */
    public static double distance(double lat1, double lng1, double lat2,double lng2) {    
        double radLat1 = rad(lat1);    
        double radLat2 = rad(lat2);    
        double a = radLat1 - radLat2;    
        double b = rad(lng1) - rad(lng2);    
        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2)    
                + Math.cos(radLat1) * Math.cos(radLat2)    
                * Math.pow(Math.sin(b / 2), 2)));    
        s = s * EARTH_RADIUS;    
        s = Math.round(s * 10000d) / 10000d;    
        s = s*1000;    
        return s;    
    }    
}
