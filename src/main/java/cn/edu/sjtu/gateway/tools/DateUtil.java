package cn.edu.sjtu.gateway.tools;

import cn.edu.sjtu.gateway.tools.exception.NotReturnValueException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

/**
 * <AUTHOR>
 */
public class DateUtil {
    public final static String FORMAT_DEFAULT = "yyyy-MM-dd HH:mm:ss";
    private static final ZoneId DEFAULT_ZONE_ID = ZoneId.systemDefault();
    private static final Logger log = LoggerFactory.getLogger(DateUtil.class);

    /**
     * 返回当前13位的Unix时间戳
     *
     * @return 13位Unix时间戳
     */
    public static long timeForUnix13() {
        return System.currentTimeMillis();
    }

    /**
     * 将10位Unix时间戳转换为格式化日期字符串
     *
     * @param unixTime 10位Unix时间戳
     * @param format   转换格式，默认为 yyyy-MM-dd HH:mm:ss
     * @return 转换后的日期字符串；若转换失败，返回原时间戳字符串
     */
    public static String intToString(int unixTime, String format) {
        try {
            // 转为毫秒级时间戳后调用 dateFormat
            return dateFormat(unixTime * 1000L, format);
        } catch (NotReturnValueException e) {
            log.error("错误信息：------>" + e);
            return String.valueOf(unixTime);
        }
    }

    /**
     * 将 Date 转为10位Unix时间戳
     *
     * @param date Date 对象
     * @return 10位Unix时间戳；若 Date 为 null，返回 0
     */
    public static int dateToInt10(Date date) {
        if (date == null) {
            return 0;
        }
        // 转为秒级时间戳
        return (int) (date.getTime() / 1000);
    }

    /**
     * 返回当前10位数的Unix时间戳
     *
     * @return 当前时间的10位Unix时间戳
     */
    public static int timeForUnix10() {
        return (int) (timeForUnix13() / 1000);
    }

    /**
     * 将Unix时间戳转为文字描述的时间
     *
     * @param unixTime 时间戳（10位或13位）
     * @param format   转换格式，默认为 {@link #FORMAT_DEFAULT}
     * @return 转换后的日期字符串
     */
    public static String dateFormat(long unixTime, String format) throws NotReturnValueException {
        if (String.valueOf(unixTime).length() < 10 || String.valueOf(unixTime).length() > 13) {
            throw new NotReturnValueException("传入的时间戳格式错误：" + unixTime);
        }
        format = (format == null || format.isEmpty()) ? FORMAT_DEFAULT : format;

        // 转换为13位时间戳
        if (String.valueOf(unixTime).length() == 10) {
            unixTime *= 1000;
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format).withZone(DEFAULT_ZONE_ID);
        return formatter.format(Instant.ofEpochMilli(unixTime));
    }

    /**
     * 将时间字符串转换为10位的Unix时间戳
     *
     * @param time   时间字符串，如"2016-02-18 00:00:11"
     * @param format 时间格式，如"yyyy-MM-dd HH:mm:ss"
     * @return 10位Unix时间戳；若解析失败，返回0
     */
    public static int stringToInt(String time, String format) {
        Date date = stringToDate(time, format);
        if (date == null) {
            return 0;
        }
        // 转为秒级时间戳
        return (int) (date.getTime() / 1000);
    }

    /**
     * 将时间字符串转换为Date对象
     *
     * @param time   时间字符串
     * @param format 时间格式
     * @return 转换后的Date对象
     */
    public static Date stringToDate(String time, String format) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            sdf.setTimeZone(TimeZone.getDefault());
            return sdf.parse(time);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取当前时间，格式化为指定的格式
     *
     * @param format 时间格式，默认为 {@link #FORMAT_DEFAULT}
     * @return 当前时间字符串
     */
    public static String currentDate(String format) {
        format = (format == null || format.isEmpty()) ? FORMAT_DEFAULT : format;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format).withZone(DEFAULT_ZONE_ID);
        return formatter.format(Instant.now());
    }

    /**
     * 将Date对象转为10位Unix时间戳
     *
     * @param date Date对象
     * @return 10位Unix时间戳
     */
    public static int dateToUnix10(Date date) {
        return (int) (date.getTime() / 1000);
    }

    /**
     * 获取当前是星期几
     *
     * @return 星期几，1为周日，7为周六
     */
    public static int currentWeek() {
        return LocalDate.now(DEFAULT_ZONE_ID).getDayOfWeek().getValue() % 7 + 1;
    }

    /**
     * 获取指定日期的凌晨时间
     *
     * @param date 日期对象
     * @return 当天凌晨时间
     */
    public static Date weeHours(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 获取指定日期的午夜时间
     *
     * @param date 日期对象
     * @return 当天午夜时间
     */
    public static Date midnight(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 999);
        return cal.getTime();
    }

    /**
     * 判断指定日期是星期几
     *
     * @param dateStr 日期字符串，格式为yyyy-MM-dd
     * @return 星期几，1为周日，7为周六
     */
    public static int getWeekForTime(String dateStr) {
        try {
            LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            return date.getDayOfWeek().getValue() % 7 + 1;
        } catch (Exception e) {
            log.error(e.getMessage());
            return 0;
        }
    }

}
