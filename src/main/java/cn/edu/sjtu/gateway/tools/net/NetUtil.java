package cn.edu.sjtu.gateway.tools.net;


import cn.edu.sjtu.gateway.tools.BaseVO;

import java.io.IOException;
import java.net.Socket;

/**
 * 网络通信
 *
 * <AUTHOR>
 */
public class NetUtil {

    /**
     * 判断某个host、端口是否能连通，也就是端口是否打开了。通常用于启动前的端口是否被占用的检测
     *
     * @param host 可传入如 localhost、***************
     * @param port 端口号，传入如  80、 8080
     */
    public static BaseVO ping(String host, int port) {
        BaseVO vo = new BaseVO();

        try (Socket Skt = new Socket(host, port)) {
        } catch (IOException e) {
            return BaseVO.failure(e.getMessage());
        }

        return BaseVO.success("success");
    }
}
