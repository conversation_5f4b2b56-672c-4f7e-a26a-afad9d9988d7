package cn.edu.sjtu.gateway.tools;

import java.io.File;
import java.nio.file.Paths;

/**
 * 类 相关的操作工具
 * <AUTHOR>
 *
 */
public class ClassUtil{
	
	/**
	 * 判断某个class是否存在
	 * @param packageName class的包名，传入如 cn.edu.sjtu.gateway.tools.wangmarket.plugin.learnExample.Plugin
	 * @return true:class存在，  false:class不存在
	 */
	public static boolean classExist(String packageName){
		try{
			Class.forName(packageName);
			return true;
		}catch(ClassNotFoundException e){
			return false;
		}
	}


	/**
	 * 根据包名生成对应的项目路径。
	 *
	 * @param packageName 包名，如 cn.edu.sjtu.gateway.tools.j2ee.entity
	 * @return 返回包对应的绝对路径，例如 /Users/<USER>/git/autowritecode/src/main/java/cn/edu/sjtu/gateway/tools/j2ee/entity/
	 */
	public static String packageToFilePath(String packageName) {
		if (packageName == null || packageName.trim().isEmpty()) {
			throw new IllegalArgumentException("包名不能为空");
		}

		// 替换包名中的"."为文件分隔符
		String packagePath = packageName.replace(".", File.separator);

		// 构造完整路径
		String projectPath = Paths.get(getProjectBasePath(), "src", "main", "java", packagePath).toString();

		return projectPath.endsWith(File.separator) ? projectPath : projectPath + File.separator;
	}

	/**
	 * 获取当前项目的根目录。
	 *
	 * @return 项目根目录的绝对路径
	 */
	private static String getProjectBasePath() {
		return System.getProperty("user.dir");
	}
	
	public static void main(String[] args) {
		System.out.println(packageToFilePath("cn.edu.sjtu.gateway.tools.Languages"));
	}
}