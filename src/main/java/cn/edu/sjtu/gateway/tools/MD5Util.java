package cn.edu.sjtu.gateway.tools;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 
 * MD5工具类. 
 * <AUTHOR>
 */
public class MD5Util {

	protected static char[] hexDigits = { '0', '1', '2', '3', '4', '5', '6',
			'7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f' };


	/**
	 * 将字符串MD5加密
	 * @param str 要进行加密的字符串
	 * @return 加密后的字符串
	 */
	public static String MD5(String str) {
		return bytesToMD5(str.getBytes()).getInfo();
	}
	
	/**
	 * 将字符串MD5加密
	 * @param str 要进行加密的字符串
	 * @return 加密后的字符串 如果 result==BaseVO.Failure ，则未失败，info返回失败原因
	 */
	public static BaseVO stringToMD5(String str) {
		return bytesToMD5(str.getBytes());
	}
	
	
	/**
	 * 失败则返回null
	 * @param bytes
	 * @return
	 */
	private static BaseVO bytesToMD5(byte[] bytes) {
		MessageDigest messagedigest = null;
		try {
			messagedigest = MessageDigest.getInstance("MD5");
		} catch (NoSuchAlgorithmException nsaex) {
			System.err.println(MD5Util.class.getName() + "初始化失败，MessageDigest不支持MD5Util。");
			nsaex.printStackTrace();
			return BaseVO.failure(MD5Util.class.getName() + "初始化失败，MessageDigest不支持MD5Util。");
		}
		
		messagedigest.update(bytes);
		return BaseVO.success(bufferToHex(messagedigest.digest()));
	}

	private static String bufferToHex(byte[] bytes) {
		return bufferToHex(bytes, 0, bytes.length);
	}

	private static String bufferToHex(byte bytes[], int m, int n) {
		StringBuffer stringbuffer = new StringBuffer(2 * n);
		int k = m + n;
		for (int l = m; l < k; l++) {
			appendHexPair(bytes[l], stringbuffer);
		}
		return stringbuffer.toString();
	}

	private static void appendHexPair(byte bt, StringBuffer stringbuffer) {
		char c0 = hexDigits[(bt & 0xf0) >> 4];
		char c1 = hexDigits[bt & 0xf];
		stringbuffer.append(c0);
		stringbuffer.append(c1);
	}
}