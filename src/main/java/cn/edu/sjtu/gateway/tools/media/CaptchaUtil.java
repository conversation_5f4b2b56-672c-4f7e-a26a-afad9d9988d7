package cn.edu.sjtu.gateway.tools.media;

import cn.edu.sjtu.gateway.vm.vo.BaseVO;
import lombok.Getter;
import lombok.Setter;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.Random;

/**
 * 验证码工具类，用于生成和验证验证码图片
 * 验证码内容为数字和字母，排除容易混淆的字符（如0、O、1、I等）
 * 支持验证码宽高、字体、背景颜色等多种属性的自定义
 *
 * <AUTHOR>
 */
public class CaptchaUtil {
    // 默认配置
    private static final int DEFAULT_WIDTH = 90;
    private static final int DEFAULT_HEIGHT = 20;
    private static final int DEFAULT_CODE_COUNT = 6;
    private static final int DEFAULT_FONT_HEIGHT = 21;
    private static final char[] CODE_SEQUENCE = {
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'M', 'N', 'P', 'Q',
            'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '2', '3', '4', '5', '6',
            '7', '8', '9'
    };
    private static final Color DEFAULT_BACKGROUND_COLOR = Color.WHITE;
    private static final Font DEFAULT_FONT = new Font("Fixedsys", Font.BOLD, DEFAULT_FONT_HEIGHT);

    // 可配置属性
    @Getter
    @Setter
    private int width = DEFAULT_WIDTH;
    @Getter
    @Setter
    private int height = DEFAULT_HEIGHT;
    @Getter
    @Setter
    private int codeCount = DEFAULT_CODE_COUNT;
    @Getter
    @Setter
    private int fontHeight = DEFAULT_FONT_HEIGHT;
    @Getter
    @Setter
    private Font font = DEFAULT_FONT;
    @Getter
    @Setter
    private Color backgroundColor = DEFAULT_BACKGROUND_COLOR;

    private String[] code;
    private final Random random = new Random();

    /**
     * 初始化验证码工具类，使用默认配置生成验证码
     */
    public CaptchaUtil() {
        generateCode();
    }

    /**
     * 生成新的验证码内容
     */
    private void generateCode() {
        code = new String[codeCount];
        for (int i = 0; i < codeCount; i++) {
            code[i] = String.valueOf(CODE_SEQUENCE[random.nextInt(CODE_SEQUENCE.length)]);
        }
    }

    /**
     * 获取当前生成的验证码内容
     *
     * @return 验证码字符串
     */
    public String getCode() {
        return String.join("", code);
    }

    /**
     * 创建验证码图片
     *
     * @return 生成的验证码图片
     */
    public BufferedImage createImage() {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = image.createGraphics();

        // 绘制背景颜色
        g.setColor(backgroundColor);
        g.fillRect(0, 0, width, height);

        // 设置字体
        g.setFont(font);

        // 添加干扰线
        g.setColor(Color.BLACK);
        for (int i = 0; i < 15; i++) {
            int x1 = random.nextInt(width);
            int y1 = random.nextInt(height);
            int x2 = x1 + random.nextInt(12);
            int y2 = y1 + random.nextInt(12);
            g.drawLine(x1, y1, x2, y2);
        }

        // 绘制验证码字符
        int charX = 15;
        for (int i = 0; i < code.length; i++) {
            g.setColor(new Color(random.nextInt(255), random.nextInt(255), random.nextInt(255)));
            g.drawString(code[i], charX * (i + 1), fontHeight - 5);
        }

        g.dispose();
        return image;
    }

    /**
     * 显示验证码图片，并将验证码内容存储到Session中
     *
     * @param captchaUtil 验证码工具类实例
     * @param request     HttpServletRequest对象
     * @param response    HttpServletResponse对象
     * @throws IOException IO异常
     */
    public static void showImage(CaptchaUtil captchaUtil, HttpServletRequest request, HttpServletResponse response) throws IOException {
        //将验证码保存到Session
        HttpSession session = request.getSession();
        session.setAttribute("code", captchaUtil.getCode());
        session.setAttribute("codeIsUsed", "0");
		// 设置响应头
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);
        response.setContentType("image/jpeg");
		// 输出验证码图片
        try (ServletOutputStream sos = response.getOutputStream()) {
            ImageIO.write(captchaUtil.createImage(), "jpeg", sos);
        }
    }

	/**
	 * 验证用户输入的验证码是否正确
	 *
	 * @param inputCode 用户输入的验证码
	 * @param request          HttpServletRequest对象，用于获取Session
	 * @return 验证结果，包含成功或失败的消息
	 */
    public static BaseVO compare(String inputCode, HttpServletRequest request) {
        BaseVO result = new BaseVO();

        if (inputCode == null || inputCode.isEmpty()) {
            result.setBaseVO(BaseVO.FAILURE, "请输入验证码");
            return result;
        }

        HttpSession session = request.getSession();
        String sessionCode = (String) session.getAttribute("code");

        if (sessionCode == null) {
            result.setBaseVO(BaseVO.FAILURE, "系统未生成验证码，请刷新后重试");
        } else if ("1".equals(session.getAttribute("codeIsUsed"))) {
            result.setBaseVO(BaseVO.FAILURE, "验证码已被使用，请刷新获取新的验证码");
        } else if (inputCode.equalsIgnoreCase(sessionCode)) {
            session.setAttribute("codeIsUsed", "1");
            result.setBaseVO(BaseVO.SUCCESS, "验证成功");
        } else {
            result.setBaseVO(BaseVO.FAILURE, "验证码错误");
        }

        return result;
    }
}
