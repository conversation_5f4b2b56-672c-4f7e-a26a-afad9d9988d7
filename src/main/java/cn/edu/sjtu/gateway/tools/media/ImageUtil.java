package cn.edu.sjtu.gateway.tools.media;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Base64;
import java.util.Iterator;



/**
 * 图片相关工具类
 * <AUTHOR>
 *
 */
public class ImageUtil {

	private static final Logger log = LoggerFactory.getLogger(ImageUtil.class);

	/**
	 * 替换 {@link BufferedImage}中，制定颜色进行替换，比如将图片中所有红色的像素点替换为黑色
	 * @param bufferedImage 要替换的图像
	 * @param oldHex 要替换的像素点十六进制颜色，如FFFFFF
	 * @param newHex 替换成的新颜色，像素点十六进制颜色，如FFFFFF
	 * @return 新的图像{@link BufferedImage}
	 */
	public static BufferedImage replaceColor(BufferedImage bufferedImage, String oldHex,String newHex){
		int newC = ColorUtil.hexToInt(newHex);
		int width = bufferedImage.getWidth();
		int height = bufferedImage.getHeight();
		
		BufferedImage newImage = new BufferedImage(width,height,bufferedImage.getType());
        Graphics g = newImage.getGraphics();
        g.drawImage(bufferedImage, 0,0,width,height,null);
        g.dispose();
		
		for (int x = 0; x < width; x++) {
			for (int y = 0; y < height; y++) {
				if(ColorUtil.intToHex(newImage.getRGB(x, y)).equals(oldHex)){
					newImage.setRGB(x, y, newC);
				}
			}
		}
		
		return newImage;
	}
	
	 /**
     * 对图片进行放大
     * @param originalImage 原始图片
     * @param times 放大倍数
     * @return 放大后的图片
     */
    public static BufferedImage bigImage(BufferedImage  originalImage, Integer times){
        int width = originalImage.getWidth()*times;
        int height = originalImage.getHeight()*times;
        BufferedImage newImage = new BufferedImage(width,height,originalImage.getType());
        Graphics g = newImage.getGraphics();
        g.drawImage(originalImage, 0,0,width,height,null);
        g.dispose();
        return newImage;
    }
    
    /**
     * 等比例缩放
     * <p>判断图像的宽度，若是宽度大于传入的值，则进行等比例压缩到指定宽高。若是图片小于指定的值，则不处理</p>
     * @param inputStream 原图
     * @param maxWidth 缩放后的宽度。若大于这个宽度才会进行等比例缩放。否则不进行处理。传入0则不处理，忽略
     * @param suffix 图片的后缀名，如png、jpg
     * @return 处理好的
     */
    public static InputStream proportionZoom(InputStream inputStream,int maxWidth,String suffix){
    	if(inputStream == null){
    		return null;
    	}
    	if(maxWidth == 0 || maxWidth < 0){
    		return inputStream;
    	}
    	
    	try {
			BufferedImage bi = ImageIO.read(inputStream);
			BufferedImage b = proportionZoom(bi, maxWidth);
			ByteArrayOutputStream os = new ByteArrayOutputStream();  
			ImageIO.write(b, suffix, os);  
			InputStream is = new ByteArrayInputStream(os.toByteArray()); 
			return is;
		} catch (IOException e) {
			e.printStackTrace();
		}
    	return null;
    }
    
    /**
     * 等比例缩放
     * <p>判断图像的宽度，若是宽度大于传入的值，则进行等比例压缩到指定宽高。若是图片小于指定的值，则不处理</p>
     * @param bufferedImage 原图
     * @param maxWidth 缩放后的宽度。若大于这个宽度才会进行等比例缩放。否则不进行处理
     * @return 处理好的
     */
    public static BufferedImage proportionZoom(BufferedImage bufferedImage,int maxWidth){
    	if(maxWidth == 0 || maxWidth < 0){
    		return bufferedImage;
    	}
    	if(bufferedImage == null){
    		return null;
    	}
    	
    	//原始图像的宽度
    	int originalWidth = bufferedImage.getWidth();	
    	if(maxWidth < originalWidth){
    		//原始图像的高度
    		int originalHeight = bufferedImage.getHeight();	
			//计算出等比例缩放后，新图片的高度
			int height = (int)((originalHeight*maxWidth)/originalWidth);
			
			BufferedImage newImage = new BufferedImage(maxWidth,height,bufferedImage.getType());
			Graphics g = newImage.getGraphics();
			g.drawImage(bufferedImage, 0,0,maxWidth,height,null);
			g.dispose();
			return newImage;
    	}else{
		   return bufferedImage;
	   }
    }
    
   
    /**
     * 通过一个图片所在的URL超链接，读取图片的 BufferedImage
     * @param imageUrl 图片的URL超链接地址，如 http://www.leimingyun.com/a.jpg
     * @return 将imageUrl的图片转化为 {@link BufferedImage}
     */
    public static BufferedImage getBufferedImageByUrl(String imageUrl){
		URL url = null;
		try {
			url = new URL(imageUrl);
		} catch (MalformedURLException e) {
			e.printStackTrace();
		}  
        BufferedImage bufferedImage = null;
		try {
			bufferedImage = ImageIO.read(url);
		} catch (IOException e) {
			e.printStackTrace();
		}  
        return bufferedImage;
    }
    
    /**
     * 将 {@link InputStream} 转化为 {@link BufferedImage}
     * @param is 目标图片的{@link InputStream}
     * @return 转换为的{@link BufferedImage}
     */
    public static BufferedImage inputStreamToBufferedImage(InputStream is){
    	if(is == null){
    		return null;
    	}
    	try {
			return ImageIO.read(is);
		} catch (IOException e) {
			e.printStackTrace();
		}
    	return null;
    }
    
    /**
     * 将 {@link InputStream} 转换为 {@link Byte}[]
     * @param is  目标图片的{@link InputStream}
     * @return 转换好的{@link Byte}[]
     */
    public static byte[] inputStreamToByte(InputStream is)  {  
    	if(is == null){
    		return null;
    	}
    	
        ByteArrayOutputStream swapStream = new ByteArrayOutputStream();  
        byte[] buff = new byte[100];  
        int rc = 0;  
        try {
			while ((rc = is.read(buff, 0, 100)) > 0) {  
			    swapStream.write(buff, 0, rc);  
			}
		} catch (IOException e) {
			e.printStackTrace();
		}  
        byte[] in2b = swapStream.toByteArray();  
        return in2b;  
    }  
    
    /** 
     * 获取图片的类型。如果是 gif、jpg、png、bmp 以外的类型则返回null。 
     * @param imageBytes 图片字节数组。 
     * @return 图片类型。 
     */  
    public static String getImageType(byte[] imageBytes) {  
    	if(imageBytes == null){
    		return null;
    	}
        ByteArrayInputStream input = new ByteArrayInputStream(imageBytes);  
        ImageInputStream imageInput = null;
        String type = null;  
		try {
			imageInput = ImageIO.createImageInputStream(input);
			Iterator<ImageReader> iterator = ImageIO.getImageReaders(imageInput);  
	        if (iterator.hasNext()) {  
	            ImageReader reader = iterator.next();  
	            type = reader.getFormatName().toUpperCase();  
	        }  
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (imageInput != null) {  
	            try {
					imageInput.close();
				} catch (IOException e) {
					e.printStackTrace();
				}  
	        }
		}
        
        return type;  
    }  
    
    /**
     * 将 {@link BufferedImage} 转换为 {@link InputStream}
     * @param bufferedImage 要转换的 {@link BufferedImage}
     * @param imageFileSuffix 图片的后缀名，如 gif 、png 、 jpg 、 bmp
     * @return 转换为的。如果本身输入为null，则这里返回的也是null
     */
    public static InputStream bufferedImageToInputStream(BufferedImage bufferedImage, String imageFileSuffix){
    	if(bufferedImage == null){
    		return null;
    	}
    	ByteArrayOutputStream os = new ByteArrayOutputStream();  
    	try {
			ImageIO.write(bufferedImage, imageFileSuffix, os);
		} catch (IOException e) {
			e.printStackTrace();
		}  
    	InputStream is = new ByteArrayInputStream(os.toByteArray());  
    	return is;
    }
    
    /**
     * 将 {@link BufferedImage} 转换为 {@link Byte}[]
     * @param bufferedImage 要转换的 {@link BufferedImage}
     * @param imageSuffix 图片的后缀名，如 gif 、png 、 jpg 、 bmp
     * @return {@link Byte}[] ，若是出错或没有，返回null
     */
    public static byte[] bufferedImageToByte(BufferedImage bufferedImage, String imageSuffix){
    	if(bufferedImage == null){
    		return null;
    	}
        ByteArrayOutputStream out = null;
		try {
			out = new ByteArrayOutputStream();
			ImageIO.write(bufferedImage, imageSuffix, out);
		} catch (IOException e) {
			e.printStackTrace();
		}
		if(out == null){
			return null;
		}else{
			return out.toByteArray();
		}
    }
    
    /**
     * 加载本地图像，变为 {@link BufferedImage}
     * @param path 图像所在路径，如 ： /images/ceshi.png
     * @return {@link BufferedImage} 若为空或图像不存在，返回null
     */
    public static BufferedImage loadLocalhostImage(String path){
    	if(path == null){
    		return null;
    	}
    	try {
			return ImageIO.read(new FileInputStream(path));
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
    	return null;
    }
    
    /**
     * 将 {@link BufferedImage} 保存到本地
     * @param bufferedImage 要保存的内存图像
     * @param formatName 如 jpg、 png、gif 
     * @param path 本地路径，如 /images/xnx3/ceshiaaaa.png
     */
    public static void saveToLocalhost(BufferedImage bufferedImage, String formatName,String path){
    	File f=new File(path);
    	try {
			ImageIO.write(bufferedImage,formatName,f);
		} catch (IOException e) {
			log.error("图片保存失败！", e);
		}
    }
    
    /**
     * 后缀格式转换，读入 png、jpg、gif格式图片的 {@link BufferedImage} ，返回新生成的内存图像，可另行保存为png、jpg、gif等格式
     * @param bufferedImage 要输入的图片的 {@link BufferedImage}
     * @return 新创建的 {@link BufferedImage}
     */
    public static BufferedImage formatConversion(BufferedImage bufferedImage){
    	 Image image = bufferedImage.getScaledInstance(bufferedImage.getWidth(),bufferedImage.getHeight(), Image.SCALE_DEFAULT);
         BufferedImage tag = new BufferedImage(bufferedImage.getWidth(), bufferedImage.getHeight(), BufferedImage.TYPE_INT_RGB);
         Graphics g = tag.getGraphics();
         g.drawImage(image, 0, 0, null); // 绘制新图
         g.dispose();
         return tag;
    }
    
    /**
     * 图片水印
     * @param originalImage 原图，大图。比如，可用 {@link cn.edu.sjtu.gateway.tools.media.ImageUtil#getBufferedImageByUrl(String)} 获取
     * @param shuiyinImage 水印图，小图，要水印到大图的水印图。
     * @param startX 水印开始地点，原图的左上角，X坐标
     * @param startY 水印开始地点，原图的左上角，Y坐标
     * @param width	宽度的像素点，会将整个图片压缩或放大整个图水印上。
     * @param height 高度的像素点
     * @return 已经水印好的图片的 {@link BufferedImage} 对象
     */
    public static BufferedImage watermark(BufferedImage originalImage, BufferedImage shuiyinImage, int startX, int startY ,int width, int height){
    	Graphics2D g = originalImage.createGraphics();
    	g.drawImage(shuiyinImage, startX, startY, width, height, null);
    	
    	return originalImage;
    }
	
    /**
     * 将 {@link BufferedImage} 转为 Base64 字符串形式返回
     * @param bufferedImage {@link BufferedImage}图像
     * @param suffix 图像的后缀，类型，传入如 jpg、png、gif 等
     * @return Base64字符串,会自动进行\r\n这种空行替换，去除换行符，变为一行返回。 返回如：
     * 		<pre>
     * 			/9j/4AAQSkZJRgABAgAAAQABAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQNDAsLDBkSEw8UHRofHh0aHBwgJC4nICIsIxwcKDcpLDAxNDQ0Hyc5PTgyPC4zNDL/2wBDAQkJCQwLDBgNDRgyIRwhMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjL/wAARCAABAAIDASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD29/8AWN9TRRRWhif/2Q==
     * 		<pre>
     */
    public static String bufferedImageToBase64(BufferedImage bufferedImage ,String suffix){
    	byte[] data = ImageUtil.bufferedImageToByte(bufferedImage, suffix);

        //对字节数组Base64编码
		Base64.Encoder encoder = Base64.getEncoder();

        return encoder.encodeToString(data).replaceAll("[\n" +
                "]", "");
    }
    
    /**
     * 将 Base64 字符串 转为 {@link BufferedImage} 返回
     * @param base64Str Base64字符串形式，传入如：
     * 		<pre>
     * 			/9j/4AAQSkZJRgABAgAAAQABAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQNDAsLDBkSEw8UHRofHh0aHBwgJC4nICIsIxwcKDcpLDAxNDQ0Hyc5PTgyPC4zNDL/2wBDAQkJCQwLDBgNDRgyIRwhMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjL/wAARCAABAAIDASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD29/8AWN9TRRRWhif/2Q==
     * 		<pre>
     * @return {@link BufferedImage}图像。注意，如果失败，比如传入的字符串为null，则会返回 null
     */
    public static BufferedImage base64TobufferedImage(String base64Str){
    	if(base64Str == null){
    		return null;
    	}
		Base64.Decoder decoder = Base64.getDecoder();
		try {
			// 解密
			byte[] b = decoder.decode(base64Str);
			ByteArrayInputStream bais = new ByteArrayInputStream(b);
            return ImageIO.read(bais);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
    }
    
    
}
