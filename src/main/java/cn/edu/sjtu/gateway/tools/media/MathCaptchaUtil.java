package cn.edu.sjtu.gateway.tools.media;

import lombok.Getter;
import lombok.Setter;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.Random;

import cn.edu.sjtu.gateway.vm.vo.BaseVO;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/12/4
 */
public class MathCaptchaUtil {
    // 默认配置
    private static final int DEFAULT_WIDTH = 120;
    private static final int DEFAULT_HEIGHT = 40;
    private static final int DEFAULT_FONT_HEIGHT = 24;
    private static final Font DEFAULT_FONT = new Font("Fixedsys", Font.BOLD, DEFAULT_FONT_HEIGHT);
    private static final Color DEFAULT_BACKGROUND_COLOR = Color.WHITE;

    @Getter
    @Setter
    private int width = DEFAULT_WIDTH;
    @Getter
    @Setter
    private int height = DEFAULT_HEIGHT;
    @Getter
    @Setter
    private int fontHeight = DEFAULT_FONT_HEIGHT;
    @Getter
    @Setter
    private Font font = DEFAULT_FONT;
    @Getter
    @Setter
    private Color backgroundColor = DEFAULT_BACKGROUND_COLOR;

    /**
     * -- GETTER --
     * 获取当前验证码问题
     *
     * @return 数学运算问题
     */
    @Getter
    private String question;
    private int answer;
    private final Random random = new Random();

    /**
     * 初始化验证码工具类，生成数学运算验证码
     */
    public MathCaptchaUtil() {
        generateMathCaptcha();
    }

    /**
     * 生成数学运算验证码
     */
    private void generateMathCaptcha() {
        // 1-10
        int num1 = random.nextInt(10) + 1;
        // 1-10
        int num2 = random.nextInt(10) + 1;
        // 0:+, 1:-, 2:*, 3:/
        int operatorIndex = random.nextInt(4);
        switch (operatorIndex) {
            case 0 -> {
                question = String.format("%-2d + %-2d= ?", num1, num2);
                answer = num1 + num2;
            }
            case 1 -> {
                question = String.format("%-2d - %-2d= ?", num1, num2);
                answer = num1 - num2;
            }
            case 2 -> {
                question = String.format("%-2d × %-2d= ?", num1, num2);
                answer = num1 * num2;
            }
            case 3 -> {
                answer = num1;
                num1 = num1 * num2;
                question = String.format("%-2d ÷ %-2d= ?", num1, num2);
            }
        }
    }

    /**
     * 创建验证码图片
     *
     * @return 生成的验证码图片
     */
    public BufferedImage createImage() {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = image.createGraphics();

        // 绘制背景颜色
        g.setColor(backgroundColor);
        g.fillRect(0, 0, width, height);

        // 设置字体
        g.setFont(font);
        g.setColor(Color.BLACK);
        for (int i = 0; i < 15; i++) {
            int x1 = random.nextInt(width);
            int y1 = random.nextInt(height);
            int x2 = random.nextInt(width);
            int y2 = random.nextInt(height);
            g.drawLine(x1, y1, x2, y2);
        }

        // 绘制验证码问题
        g.setColor(Color.BLUE);
        g.drawString(question, 10, height / 2 + fontHeight / 3);

        g.dispose();
        return image;
    }

    /**
     * 显示验证码图片，并将验证码内容存储到Session中
     *
     * @param captchaUtil 验证码工具类实例
     * @param request     HttpServletRequest对象
     * @param response    HttpServletResponse对象
     * @throws java.io.IOException IO异常
     */
    public static void showImage(MathCaptchaUtil captchaUtil, HttpServletRequest request, HttpServletResponse response) throws IOException {
        // 将答案保存到Session
        HttpSession session = request.getSession();
        session.setAttribute("captchaAnswer", captchaUtil.answer);
        session.setAttribute("captchaIsUsed", "0");

        // 设置响应头
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);
        response.setContentType("image/jpeg");

        // 输出验证码图片
        try (ServletOutputStream sos = response.getOutputStream()) {
            ImageIO.write(captchaUtil.createImage(), "jpeg", sos);
        }
    }

    /**
     * 验证用户输入的验证码是否正确
     *
     * @param inputAnswer 用户输入的答案
     * @param request     HttpServletRequest对象，用于获取Session
     * @return 验证结果，包含成功或失败的消息
     */
    public static BaseVO compare(String inputAnswer, HttpServletRequest request) {
        BaseVO result = new BaseVO();

        if (inputAnswer == null || inputAnswer.isEmpty()) {
            result.setBaseVO(BaseVO.FAILURE, "请输入验证码");
            return result;
        }

        HttpSession session = request.getSession();
        Integer sessionAnswer = (Integer) session.getAttribute("captchaAnswer");

        if (sessionAnswer == null) {
            result.setBaseVO(BaseVO.FAILURE, "系统未生成验证码，请刷新后重试");
        } else if ("1".equals(session.getAttribute("captchaIsUsed"))) {
            result.setBaseVO(BaseVO.FAILURE, "验证码已被使用，请刷新获取新的验证码");
        } else if (Integer.parseInt(inputAnswer) == sessionAnswer) {
            session.setAttribute("captchaIsUsed", "1");
            result.setBaseVO(BaseVO.SUCCESS, "验证成功");
        } else {
            result.setBaseVO(BaseVO.FAILURE, "验证失败");
        }

        return result;
    }
}

