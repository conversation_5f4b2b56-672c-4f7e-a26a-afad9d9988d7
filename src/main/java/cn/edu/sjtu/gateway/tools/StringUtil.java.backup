package cn.edu.sjtu.gateway.tools;

import cn.edu.sjtu.gateway.common.util.StringUtils;

import java.io.InputStream;
import java.io.UnsupportedEncodingException;

/**
 * 字符串工具类 - 迁移适配器
 * 
 * @deprecated 请使用 {@link StringUtils}
 * <AUTHOR>
 */
@Deprecated
public class StringUtil {
    
    /**
     * 两个String字符串比较是否相等
     * @deprecated 请使用 {@link StringUtils#equals(String, String)}
     */
    @Deprecated
    public static boolean StringEqual(String s1, String s2) {
        return StringUtils.equals(s1, s2);
    }
    
    /**
     * 两个String字符串比较是否相等
     * @deprecated 请使用 {@link StringUtils#equals(String, String, boolean)}
     */
    @Deprecated
    public static boolean StringEqual(String s1, String s2, boolean removeBlank) {
        return StringUtils.equals(s1, s2, removeBlank);
    }
    
    /**
     * 两个String字符串比较是否相等
     * @deprecated 请使用 {@link StringUtils#equals(String, String, boolean, boolean)}
     */
    @Deprecated
    public static boolean StringEqual(String s1, String s2, boolean removeBlank, boolean ignoreNull) {
        return StringUtils.equals(s1, s2, removeBlank, ignoreNull);
    }
    
    /**
     * 生成随机长度的英文
     * @deprecated 请使用 {@link StringUtils#getRandomAZ(int)}
     */
    @Deprecated
    public static String getRandomAZ(int length) {
        return StringUtils.getRandomAZ(length);
    }
    
    /**
     * 生成随机长度的英文+数字
     * @deprecated 请使用 {@link StringUtils#getRandom09AZ(int)}
     */
    @Deprecated
    public static String getRandom09AZ(int length) {
        return StringUtils.getRandom09AZ(length);
    }
    
    /**
     * 过滤XSS攻击有关的字符
     * @deprecated 请使用 {@link StringUtils#filterXss(String)}
     */
    @Deprecated
    public static String filterXss(String text) {
        return StringUtils.filterXss(text);
    }
    
    /**
     * 对某个字符串进行位移操作
     * @deprecated 请使用 {@link StringUtils#encrypt(String, int)}
     */
    @Deprecated
    public static String encrypt(String text, int shiftNum) {
        return StringUtils.encrypt(text, shiftNum);
    }
    
    /**
     * UTF-8格式汉字转换为%E4%BD%A0形式
     * @deprecated 请使用 {@link StringUtils#stringToUrl(String)}
     */
    @Deprecated
    public static String stringToUrl(String content) {
        return StringUtils.stringToUrl(content);
    }
    
    /**
     * 将字符串转换为InputStream
     * @deprecated 请使用 {@link StringUtils#stringToInputStream(String, String)}
     */
    @Deprecated
    public static InputStream stringToInputStream(String text, String encode) throws UnsupportedEncodingException {
        return StringUtils.stringToInputStream(text, encode);
    }
    
    /**
     * 将字符串转换为InputStream，使用UTF-8编码
     * @deprecated 请使用 {@link StringUtils#stringToInputStream(String)}
     */
    @Deprecated
    public static InputStream stringToInputStream(String text) {
        return StringUtils.stringToInputStream(text);
    }
    
    // 保留原有的其他方法，但标记为废弃
    // 这里只列出主要的迁移方法，其他方法可以根据需要逐步迁移
}
