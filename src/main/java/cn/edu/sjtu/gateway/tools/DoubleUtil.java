package cn.edu.sjtu.gateway.tools;

import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * Double工具类
 * <AUTHOR>
 *
 */
public class DoubleUtil {

	/**
	 * 将 String 格式转化为 double 格式。
	 *
	 * @param value        要转化的字符串
	 * @param defaultValue 转换失败时返回的默认值
	 * @return 转换后的 double 值
	 */
	public static double stringToDouble(String value, double defaultValue) {
		if (value == null || "null".equalsIgnoreCase(value.trim())) {
			return defaultValue;
		}
		try {
			return Double.parseDouble(value);
		} catch (NumberFormatException e) {
			return defaultValue;
		}
	}

	/**
	 * double 保留 1 位小数，四舍五入。
	 *
	 * @param d 要四舍五入的原始值
	 * @return 保留 1 位小数的值
	 */
	public static double doubleSplit(double d) {
		return doubleSplit(d, 1);
	}

	/**
	 * double 保留指定位数小数，四舍五入。
	 *
	 * @param d              要四舍五入的原始值
	 * @param decimalPlaces 保留的小数位数
	 * @return 保留小数位数的值
	 */
	public static double doubleSplit(double d, int decimalPlaces) {
		if (decimalPlaces < 0) {
			throw new IllegalArgumentException("保留的小数位数不能为负数");
		}
		BigDecimal bd = BigDecimal.valueOf(d);
		return bd.setScale(decimalPlaces, BigDecimal.ROUND_HALF_UP).doubleValue();
	}

	/**
	 * 将 double 转为 String 显示，去掉科学计数法表示。
	 *
	 * @param value double 值
	 * @return 转换后的字符串
	 */
	public static String doubleToString(double value) {
		DecimalFormat decimalFormat = new DecimalFormat("###################.################");
		return decimalFormat.format(value);
	}

	/**
	 * 两个 double 数相加，精确相加。
	 *
	 * @param d1 第一个 double 数
	 * @param d2 第二个 double 数
	 * @return 和
	 */
	public static double add(double d1, double d2) {
		return BigDecimal.valueOf(d1).add(BigDecimal.valueOf(d2)).doubleValue();
	}

	/**
	 * 两个 double 数相减，精确相减。
	 *
	 * @param d1 第一个 double 数
	 * @param d2 第二个 double 数
	 * @return 差
	 */
	public static double subtract(double d1, double d2) {
		return BigDecimal.valueOf(d1).subtract(BigDecimal.valueOf(d2)).doubleValue();
	}

	/**
	 * 给 double 值的最后一位小数加 1。
	 *
	 * @param value 原始值
	 * @return 加 1 后的结果
	 */
	public static double lastAddOne(double value) {
		String str = doubleToString(value);
		int decimalPointIndex = str.indexOf(".");

		if (decimalPointIndex == -1) {
			// 如果没有小数点，直接加 1
			return add(value, 1.0);
		}

		// 计算要加的最小单位
		int decimalPlaces = str.length() - decimalPointIndex - 1;
		double increment = Math.pow(10, -decimalPlaces);

		// 返回加运算后的结果
		return add(value, increment);
	}

	public static void main(String[] args) {
		System.out.println(doubleToString(subtract(0.000023, 0.000022))); // 输出：0.000001
		System.out.println(doubleSplit(123.45678, 2));    // 输出：123.46
		System.out.println(doubleToString(1.23E5));       // 输出：123000.0
		System.out.println(lastAddOne(1.234));            // 输出：1.235
	}
}

