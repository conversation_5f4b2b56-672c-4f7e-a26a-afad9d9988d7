package cn.edu.sjtu.gateway.manager.generateCache;

import cn.edu.sjtu.gateway.tools.file.FileUtil;
import cn.edu.sjtu.gateway.vm.Global;

import cn.edu.sjtu.gateway.vm.util.SystemUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * WeUI的选择使用，创建js，方便一件调用
 *
 * <AUTHOR>
 */
@Slf4j
public class WeUI {
    /**
     * 生成的js存储数据的对象名，保存的文件名也是使用此有关联
     */
    @Setter
    @Getter
    private String objName;

    private final List<Map<String, String>> dataList;

    public WeUI() {
        this.dataList = new ArrayList<>();
    }

    /**
     * 向数据列表中追加数据
     * select :  map.get("title")  map.get("value")
     */
    public void appendDataList(Map<String, String> dataMap) {
        this.dataList.add(dataMap);
    }
    public void appendDataList(String title, String value) {
        Map<String, String> map = new HashMap<String, String>();
        map.put("title", title);
        map.put("value", value);
        this.dataList.add(map);

    }

    /**
     * 生成js缓存文件保存
     */
    public void generateCacheFile() {
        String fileName = getClass().getSimpleName() + "_" + objName;
        String filePath = SystemUtil.getProjectPath() + Global.CACHE_FILE + fileName + ".js";
        StringBuilder data = new StringBuilder();
        for (Map<String, String> map : dataList) {
            if (!data.isEmpty()) {
                data.append(",");
            }
            data.append("{title: \"").append(map.get("title")).append("\",value: \"").append(map.get("value")).append("\"}");
        }
        String content = "var " + fileName + " = [" + data + "];";
        try {
            log.info("create cache js file success ! file path : {}" , filePath);
            FileUtil.write(filePath, content, "UTF-8");
        } catch (IOException e) {
            log.error("错误信息：------>", e);
        }
    }

}
