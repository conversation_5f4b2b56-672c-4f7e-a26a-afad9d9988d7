package cn.edu.sjtu.gateway.manager.vo;

import java.util.List;

import cn.edu.sjtu.gateway.vm.vo.BaseVO;

/**
 * 模版变量列表，不包含模版变量内容
 * <AUTHOR>
 */
public class TemplateVarListVO extends BaseVO {
//	private List<TemplateVar> list;
//
//	public List<TemplateVar> getList() {
//		return list;
//	}
//
//	public void setList(List<TemplateVar> list) {
//		this.list = list;
//	}
	
	private List<TemplateVarVO> list;

	public List<TemplateVarVO> getList() {
		return list;
	}

	public void setList(List<TemplateVarVO> list) {
		this.list = list;
	}
	
}
