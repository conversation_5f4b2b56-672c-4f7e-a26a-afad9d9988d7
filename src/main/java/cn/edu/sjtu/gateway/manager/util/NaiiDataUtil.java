package cn.edu.sjtu.gateway.manager.util;

import cn.edu.sjtu.gateway.tools.file.FileUtil;
import cn.edu.sjtu.gateway.vm.util.SystemUtil;

import java.io.File;

/**
 * naii 数据相关的data文件相关
 * <AUTHOR>
 *
 */
public class NaiiDataUtil {
	private static String naiiDataRootPath = null;
	
	/**
	 * 获取当前 naii_data 文件夹所在的路径
	 * @return 返回如：  /mnt/tomcat/naii_data/
	 */
	public static String getnaiiDataRootPath() {
		if(naiiDataRootPath != null) {
			return naiiDataRootPath;
		}
		
		
		String path = SystemUtil.getProjectPath();
		path = path.replace("target/classes/", "naii_data/");
		path = path.replace("webapps/ROOT/", "naii_data/");
		
		//如果第一次用，是没有 naii_data 这个目录的，自动创建这个目录，这个目录位于 tomcat下，跟webapps平级。  实际开发中，这个目录跟 target、src 是平级
		if(!FileUtil.exists(path)){
			File file = new File(path);
			file.mkdir();
		}
		
		return path;
	}
	
}
