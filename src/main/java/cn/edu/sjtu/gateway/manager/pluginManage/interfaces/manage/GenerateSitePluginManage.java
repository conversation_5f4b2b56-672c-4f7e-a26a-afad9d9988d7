package cn.edu.sjtu.gateway.manager.pluginManage.interfaces.manage;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import cn.edu.sjtu.gateway.tools.ScanClassUtil;

import cn.edu.sjtu.gateway.vm.util.SpringUtil;
import cn.edu.sjtu.gateway.vm.vo.BaseVO;
import cn.edu.sjtu.gateway.manager.bean.NewsDataBean;
import cn.edu.sjtu.gateway.manager.cache.TemplateCMS;
import cn.edu.sjtu.gateway.manager.entity.News;
import cn.edu.sjtu.gateway.manager.entity.Site;
import cn.edu.sjtu.gateway.manager.entity.SiteColumn;

/**
 * 文章保存时，针对news、news_date 的预处理插件
 * <AUTHOR>
 *
 */
@Component(value="PluginManageForGenerateSite")
@Slf4j
public class GenerateSitePluginManage {
	//自动回复的插件，这里开启项目时，便将有关此的插件加入此处
	public static List<Class<?>> classList;
	static{
		classList = new ArrayList<Class<?>>();
		
		new Thread(new Runnable() {
			public void run() {
				while(SpringUtil.getApplicationContext() == null){
					try {
						Thread.sleep(1000);
					} catch (InterruptedException e) {
						log.error("错误信息：------>"+e);
					}
				}
				
				//当 SpringUtil 被Spring 加载后才会执行
				List<Class<?>> cl = ScanClassUtil.getClasses("cn.edu.sjtu.gateway.tools.naii");
				classList = ScanClassUtil.searchByInterfaceName(cl, "cn.edu.sjtu.gateway.manager.pluginManage.interfaces.GenerateSiteInterface");
				for (int i = 0; i < classList.size(); i++) {
					log.info("装载 GenerateSite 插件："+classList.get(i).getName());
				}
			}
		}).start();
	}
	
	/**
	 * 点击一键部署按钮后，并且尚未一键部署之前，触发此方法
	 * @param site 一键部署的网站 {@link cn.edu.sjtu.gateway.manager.entity.Site}
	 * @throws InstantiationException
	 * @throws IllegalAccessException
	 * @throws NoSuchMethodException
	 * @throws SecurityException
	 * @throws IllegalArgumentException
	 * @throws java.lang.reflect.InvocationTargetException
	 */
	public static BaseVO generateSiteBefore(HttpServletRequest request, Site site) throws InstantiationException, IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException{
		for (int i = 0; i < classList.size(); i++) {
			Class<?> c = classList.get(i);
			Object invokeReply = null;
			invokeReply = c.newInstance();
			//运用newInstance()来生成这个新获取方法的实例
			Method m = c.getMethod("generateSiteBefore",new Class[]{HttpServletRequest.class,Site.class});	//获取要调用的init方法
			//动态构造的Method对象invoke委托动态构造的InvokeTest对象，执行对应形参的add方法
			Object o = m.invoke(invokeReply, new Object[]{request, site});
			if(o != null){
				BaseVO vo = (BaseVO) o;
				//如果某一个插件返回了失败，那就以失败来算
				if(vo.getResult() - BaseVO.FAILURE == 0){
					return vo;
				}
			}
		}
		return new BaseVO();
	}
	
	/**
	 * 点击一键部署按钮后，已经生成完成整站，触发的接口
	 * @param site 一键部署的网站 {@link cn.edu.sjtu.gateway.manager.entity.Site}
	 * @throws InstantiationException
	 * @throws IllegalAccessException
	 * @throws NoSuchMethodException
	 * @throws SecurityException
	 * @throws IllegalArgumentException
	 * @throws java.lang.reflect.InvocationTargetException
	 */
	public static void generateSiteFinish(HttpServletRequest request, Site site, Map<String, SiteColumn> siteColumnMap, Map<String, List<News>> newsMap, Map<Integer, NewsDataBean> newsDataMap, TemplateCMS template) throws InstantiationException, IllegalAccessException, NoSuchMethodException, SecurityException, IllegalArgumentException, InvocationTargetException{
		for (int i = 0; i < classList.size(); i++) {
			Class<?> c = classList.get(i);
			Object invokeReply = null;
			invokeReply = c.newInstance();
			//运用newInstance()来生成这个新获取方法的实例
			Method m = c.getMethod("generateSiteFinish",new Class[]{HttpServletRequest.class, Site.class, Map.class, Map.class, Map.class, TemplateCMS.class});	//获取要调用的init方法
			//动态构造的Method对象invoke委托动态构造的InvokeTest对象，执行对应形参的add方法
			m.invoke(invokeReply, new Object[]{request, site, siteColumnMap, newsMap, newsDataMap, template});
		}
	}
	
}
