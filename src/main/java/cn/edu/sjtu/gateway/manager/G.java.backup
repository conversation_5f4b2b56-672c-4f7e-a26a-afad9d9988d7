package cn.edu.sjtu.gateway.manager;

import cn.edu.sjtu.gateway.common.config.GlobalConfig;
import cn.edu.sjtu.gateway.common.manager.DomainManager;
import cn.edu.sjtu.gateway.domain.bean.SimpleSite;

/**
 * 全局配置类 - 迁移适配器
 * 
 * @deprecated 请使用 {@link GlobalConfig} 和 {@link DomainManager}
 * <AUTHOR>
 */
@Deprecated
public class G {
    
    // 版本信息 - 迁移到 GlobalConfig.Version
    @Deprecated
    public static String VERSION = GlobalConfig.Version.CURRENT_VERSION;
    
    // 业务配置 - 迁移到 GlobalConfig.Business
    @Deprecated
    public static int agencyAddSubAgency_siteSize = GlobalConfig.Business.AGENCY_ADD_SUB_AGENCY_SITE_SIZE;
    
    // 路径配置 - 迁移到 GlobalConfig.Path
    @Deprecated
    public static final String CACHE_FILE = GlobalConfig.Path.CACHE_FILE;
    @Deprecated
    public static final String DEFAULT_SITE_COLUMN_ICON_URL = GlobalConfig.Path.DEFAULT_SITE_COLUMN_ICON_URL;
    
    // 模板配置 - 迁移到 GlobalConfig.Template
    @Deprecated
    public static final int TEMPLATE_PC_DEFAULT = GlobalConfig.Template.PC_DEFAULT;
    @Deprecated
    public static final int TEMPLATE_WAP_DEFAULT = GlobalConfig.Template.WAP_DEFAULT;
    @Deprecated
    public static final int PAGE_WAP_NUM = GlobalConfig.Template.PAGE_WAP_NUM;
    
    // 图片尺寸配置 - 迁移到 GlobalConfig.ImageSize
    @Deprecated
    public static final int SITECOLUMN_ICON_MAXWIDTH = GlobalConfig.ImageSize.SITECOLUMN_ICON_MAXWIDTH;
    @Deprecated
    public static final int CAROUSEL_MAXWIDTH = GlobalConfig.ImageSize.CAROUSEL_MAXWIDTH;
    @Deprecated
    public static final int NEWS_TITLEPIC_MAXWIDTH = GlobalConfig.ImageSize.NEWS_TITLEPIC_MAXWIDTH;
    @Deprecated
    public static final int SITECOLUMN_ICON_MAXWIDTH_PC = GlobalConfig.ImageSize.SITECOLUMN_ICON_MAXWIDTH_PC;
    @Deprecated
    public static final int CAROUSEL_MAXWIDTH_PC = GlobalConfig.ImageSize.CAROUSEL_MAXWIDTH_PC;
    
    // 业务配置
    @Deprecated
    public static final int REG_GENERAL_OSS_HAVE = GlobalConfig.Business.REG_GENERAL_OSS_HAVE;
    @Deprecated
    public static boolean copyright = GlobalConfig.Business.copyright;
    
    // 域名管理方法 - 迁移到 DomainManager
    @Deprecated
    public static void putDomain(String domain, SimpleSite ss) {
        DomainManager.putDomain(domain, ss);
    }
    
    @Deprecated
    public static void putBindDomain(String bindDomain, SimpleSite ss) {
        DomainManager.putBindDomain(bindDomain, ss);
    }
    
    @Deprecated
    public static SimpleSite getDomain(String domain) {
        return DomainManager.getDomain(domain);
    }
    
    @Deprecated
    public static SimpleSite getBindDomain(String bindDomain) {
        return DomainManager.getBindDomain(bindDomain);
    }
    
    @Deprecated
    public static int getDomainSize() {
        return DomainManager.getDomainSize();
    }
    
    @Deprecated
    public static int getBindDomainSize() {
        return DomainManager.getBindDomainSize();
    }
    
    @Deprecated
    public static String[] getAutoAssignDomain() {
        return GlobalConfig.getAutoAssignDomain();
    }
    
    @Deprecated
    public static String getFirstAutoAssignDomain() {
        return GlobalConfig.getFirstAutoAssignDomain();
    }
    
    @Deprecated
    public static String getCarouselPath(Integer siteId) {
        return GlobalConfig.getCarouselPath(siteId);
    }
}
