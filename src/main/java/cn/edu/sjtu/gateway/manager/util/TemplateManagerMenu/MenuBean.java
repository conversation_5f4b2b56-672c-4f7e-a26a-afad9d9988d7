package cn.edu.sjtu.gateway.manager.util.TemplateManagerMenu;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 网站管理后台，右侧菜单相关
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class MenuBean {

    public String id;        //id，如 jibenxinxi 一级menu是直接就是id，但是二级不是直接用，加tag前缀，如 dd_jibenxinxi 、 a_jibenxinxi
    //菜单所显示的文字，如 基本信息
    public String name;
    //a标签的href的值
    public String href;
    //一级菜单才有，也就是顶级菜单，前面会有个图标。这个就是。值如：&#xe620;
    public String icon;
    //父菜单，上级菜单的id，如果已经是顶级菜单，这里没有值，为“”空字符串
    public String parentId;
    //子菜单
    List<MenuBean> subList;
    //是否已使用，若是已经使用，则是1，没有使用，则是0
    private int isUse;

    public MenuBean(TemplateMenuEnum menuEnum) {
        this.id = menuEnum.id;
        this.name = menuEnum.name;
        this.href = menuEnum.href;
        this.icon = menuEnum.icon;
        this.parentId = menuEnum.parentId;

        //默认是0
        isUse = 0;
        this.subList = new ArrayList<MenuBean>();
    }

}
