package cn.edu.sjtu.gateway.manager.vo.bean.template;
/**
 * 模版导入，导入模版变量的内容，转换为json，对象
 * <AUTHOR>
 */
public class TemplateVar {
	private cn.edu.sjtu.gateway.manager.entity.TemplateVar templateVar;
	private String text;
	
	public cn.edu.sjtu.gateway.manager.entity.TemplateVar getTemplateVar() {
		return templateVar;
	}
	public void setTemplateVar(cn.edu.sjtu.gateway.manager.entity.TemplateVar templateVar) {
		this.templateVar = templateVar;
	}
	public String getText() {
		return text;
	}
	public void setText(String text) {
		this.text = text;
	}

}
