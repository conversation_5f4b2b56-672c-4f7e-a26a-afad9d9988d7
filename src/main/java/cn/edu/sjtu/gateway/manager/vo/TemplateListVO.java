package cn.edu.sjtu.gateway.manager.vo;

import java.util.List;

import cn.edu.sjtu.gateway.vm.vo.BaseVO;
import cn.edu.sjtu.gateway.manager.entity.Template;

/**
 * 模版页面，模版导入，将导入的字符串转化为json，然后将json转化为此对象
 * <AUTHOR>
 */
public class TemplateListVO extends BaseVO {
	private List<Template> list;	//返回的模版列表

	public List<Template> getList() {
		return list;
	}

	public void setList(List<Template> list) {
		this.list = list;
	}
	
}
