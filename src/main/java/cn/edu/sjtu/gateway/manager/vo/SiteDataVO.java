package cn.edu.sjtu.gateway.manager.vo;

import cn.edu.sjtu.gateway.vm.vo.BaseVO;
import cn.edu.sjtu.gateway.manager.entity.Site;
import cn.edu.sjtu.gateway.manager.entity.SiteData;

/**
 * 站点 其他信息
 * <AUTHOR>
 */
public class SiteDataVO extends BaseVO {
	private Site site;
	private SiteData siteData;

	public Site getSite() {
		return site;
	}

	public void setSite(Site site) {
		this.site = site;
	}

	public SiteData getSiteData() {
		return siteData;
	}

	public void setSiteData(SiteData siteData) {
		this.siteData = siteData;
	}
	
}
