package cn.edu.sjtu.gateway.manager.generateCache;

import cn.edu.sjtu.gateway.manager.entity.Carousel;
import org.springframework.stereotype.Component;
import cn.edu.sjtu.gateway.vm.generateCache.BaseGenerate;

/**
 * 轮播图
 * <AUTHOR>
 */
@Component
public class CarouselGenerate extends BaseGenerate {
	public CarouselGenerate() {
		isshow();
		type();
	}
	
	/**
	 * 是否显示
	 */
	public void isshow(){
		createCacheObject("isshow");
		cacheAdd(Carousel.ISSHOW_SHOW , "显示");
		cacheAdd(Carousel.ISSHOW_HIDDEN , "隐藏");
		generateCacheFile();
	}
	
	/**
	 * 类型
	 */
	public void type(){
		createCacheObject("type");
		cacheAdd(Carousel.TYPE_DEFAULT_PAGEBANNER , "内页Banner");
		cacheAdd(Carousel.TYPE_INDEXBANNER , "首页Banner");
		generateCacheFile();
	}
}
