package cn.edu.sjtu.gateway.manager.entity;

import lombok.Setter;

import static javax.persistence.GenerationType.IDENTITY;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * News entity. <AUTHOR> Persistence Tools
 */
@Setter
@Entity
@Table(name = "exchange")
public class Exchange implements java.io.Serializable {
	// Fields
	private Integer id;
	private Integer userid;
	private Integer addtime;
	private String type;
	private Short status;
	private Integer siteid;
	private Integer goodsid;
	private String userRemark;
	private String kefuRemark;
	
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Integer getId() {
		return id;
	}

    @Column(name = "userid", columnDefinition="int(11) COMMENT '' default '0'")
	public Integer getUserid() {
		return this.userid;
	}

    @Column(name = "addtime", columnDefinition="int(11) COMMENT ''")
	public Integer getAddtime() {
		return this.addtime;
	}

    @Column(name = "type", columnDefinition="varchar(255) COMMENT '' default ''")
	public String getType() {
		return type;
	}

    @Column(name = "status", columnDefinition="smallint(6) COMMENT '' default '0'")
	public Short getStatus() {
		return status;
	}

    @Column(name = "siteid", columnDefinition="int(11) COMMENT '' default '0'")
	public Integer getSiteid() {
		return siteid;
	}

    @Column(name = "user_remark", columnDefinition="varchar(255) COMMENT '' default ''")
	public String getUserRemark() {
		return userRemark;
	}

    @Column(name = "kefu_remark", columnDefinition="varchar(255) COMMENT '' default ''")
	public String getKefuRemark() {
		return kefuRemark;
	}

    @Column(name = "goodsid", columnDefinition="int(11) COMMENT '' default '0'")
	public Integer getGoodsid() {
		return goodsid;
	}

    @Override
	public String toString() {
		return "Exchange [id=" + id + ", userid=" + userid + ", addtime=" + addtime + ", type=" + type + ", status="
				+ status + ", siteid=" + siteid + ", goodsid=" + goodsid + ", userRemark=" + userRemark
				+ ", kefuRemark=" + kefuRemark + "]";
	}
	
}