package cn.edu.sjtu.gateway.manager.pluginManage.interfaces;

import javax.servlet.http.HttpServletRequest;

import cn.edu.sjtu.gateway.manager.cache.generateSite.GenerateHtmlInterface;
import cn.edu.sjtu.gateway.manager.entity.Site;

/**
 * 点击一键部署后，自定义将网站生成的html存放到哪里。如存放到obs、ftp等
 * <AUTHOR>
 */
public interface GenerateHtmlStorateInterface {
	
	/**
	 * 获取 {@link GenerateHtmlInterface} 接口的实现。如果没有插件实现此接口，那么返回null
	 */
	public GenerateHtmlInterface getGenerateHtmlInterfaceImpl(HttpServletRequest request, Site site);
	
}