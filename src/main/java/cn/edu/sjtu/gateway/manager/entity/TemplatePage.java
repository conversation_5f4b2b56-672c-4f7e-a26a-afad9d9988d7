package cn.edu.sjtu.gateway.manager.entity;

import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import static javax.persistence.GenerationType.IDENTITY;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 模版页面
 * <AUTHOR>
 */
@Entity
@Table(name = "template_page")
public class TemplatePage implements java.io.Serializable {
	
	/**
	 * 模版页面类型，0:其他
	 */
	public final static Short TYPE_ELSE = 0;
	/**
	 * 模版页面类型，1:首页
	 */
	public final static Short TYPE_INDEX = 1;
	/**
	 * 模版页面类型，2:文章列表,文字列表
	 */
	public final static Short TYPE_NEWS_LIST = 2;
	/**
	 * 模版页面类型，3:文章详情
	 */
	public final static Short TYPE_NEWS_VIEW = 3;
	/**
	 * 模版页面类型，6:单页面如关于我们，废弃，并入详情页模版
	 */
	public final static Short TYPE_ALONEPAGE = 6;
	
	/**
	 * 当前模版页面的编辑模式，1:智能模式， 这里，判断只要不是2，那都是智能模式，以兼容以前的版本
	 */
	public final static Short EDIT_MODE_VISUAL = 1;
	/**
	 * 当前模版页面的编辑模式，2:代码模式
	 */
	public final static Short EDIT_MODE_CODE = 2;

	@Setter
    private Integer id;			//自动编号
	private String name;		//当前模版页面的名字，（还原模板时会使用到）
	@Setter
    private Short editMode;		//当前模版页面的编辑模式，1:智能模式； 2:代码模式。  这里，判断只要不是2，那都是智能模式，以兼容以前的版本
	@Setter
    private Integer userid;		//所属用户的id
	@Setter
    private Short type;			//当前模版页的类型，类型；0其他；1首页；2新闻列表,文字列表；3新闻详情；6单页面如关于我们
	@Setter
    private String templateName;//所属的模版名字
	@Setter
    private int siteid;			//当前模版页面，是那个站点在使用。一个站点拥有一个自己的模版，克隆而来
	@Setter
    private String remark;		//备注
	
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Integer getId() {
		return id;
	}

    @Column(name = "name", columnDefinition = "char(20) COMMENT '当前模版页面的名字，（还原模板时会使用到）' default ''")
	public String getName() {
		return name;
	}
	public void setName(String name) {
		if(name.length() > 20){
			name = name.substring(0, 20);
		}
		this.name = name;
	}
	@Column(name = "userid", columnDefinition = "int(11) COMMENT '所属用户的id' default '0'")
	public Integer getUserid() {
		return userid;
	}

    @Column(name = "type", columnDefinition = "tinyint(2) COMMENT '当前模版页的类型，类型；0其他；1首页；2新闻列表,文字列表；3新闻详情；6单页面如关于我们' default '0'")
	public Short getType() {
		return type;
	}

    @Column(name = "template_name", columnDefinition = "char(20) COMMENT '所属的模版名字' default ''")
	public String getTemplateName() {
		return templateName;
	}

    @Column(name = "siteid", columnDefinition = "int(11) COMMENT '当前模版页面，是那个站点在使用。一个站点拥有一个自己的模版，克隆而来' default '0'")
	public int getSiteid() {
		return siteid;
	}

    @Column(name = "remark", columnDefinition = "char(30) COMMENT '备注' default ''")
	public String getRemark() {
		return remark;
	}

    @Column(name = "edit_mode", columnDefinition = "tinyint(2) COMMENT '当前模版页面的编辑模式，1:智能模式； 2:代码模式。  这里，判断只要不是2，那都是智能模式，以兼容以前的版本' default '0'")
	public Short getEditMode() {
		return editMode;
	}

    @Override
	public String toString() {
		return "TemplatePage [id=" + id + ", name=" + name + ", editMode=" + editMode + ", userid=" + userid + ", type="
				+ type + ", templateName=" + templateName + ", siteid=" + siteid + ", remark=" + remark + "]";
	}
	
}