package cn.edu.sjtu.gateway.manager.vo;

import java.util.List;

import cn.edu.sjtu.gateway.vm.vo.BaseVO;
import cn.edu.sjtu.gateway.manager.entity.Site;
import cn.edu.sjtu.gateway.manager.vo.bean.OSSFile;


/**
 * 站点文件列表
 * <AUTHOR>
 */
public class SiteFileListVO extends BaseVO {
	private List<OSSFile> list;
	private Site site;

	public List<OSSFile> getList() {
		return list;
	}

	public void setList(List<OSSFile> list) {
		this.list = list;
	}

	public Site getSite() {
		return site;
	}

	public void setSite(Site site) {
		this.site = site;
	}
	
}
