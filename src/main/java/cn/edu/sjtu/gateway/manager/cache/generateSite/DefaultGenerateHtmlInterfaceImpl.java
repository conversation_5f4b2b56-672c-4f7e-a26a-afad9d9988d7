package cn.edu.sjtu.gateway.manager.cache.generateSite;

import cn.edu.sjtu.gateway.fileupload.NaiiFileUpload;
import cn.edu.sjtu.gateway.vm.util.file.FileUploadUtil;
import org.springframework.beans.BeanUtils;
import cn.edu.sjtu.gateway.vm.util.AttachmentUtil;
import cn.edu.sjtu.gateway.vm.vo.BaseVO;
import cn.edu.sjtu.gateway.manager.entity.Site;

/**
 * 默认的 GenerateHtmlInterface 接口的实现
 * <AUTHOR>
 *
 */
public class DefaultGenerateHtmlInterfaceImpl implements GenerateHtmlInterface{
	private final Site site;
	private final NaiiFileUpload fileUpload;
	
	public DefaultGenerateHtmlInterfaceImpl(Site site) {
		this.site = site;
		this.fileUpload = new NaiiFileUpload();
		BeanUtils.copyProperties(FileUploadUtil.fileupload, this.fileUpload);
		//自动一键部署允许上传的文件后缀
		this.fileUpload.setAllowUploadSuffix("html|txt|xml");
	}
	
	@Override
	public BaseVO putStringFile(String text, String path) {
		AttachmentUtil.uploadStringFile("site/"+site.getId()+"/"+path, text);
		this.fileUpload.uploadString("site/"+site.getId()+"/"+path, text);
		return BaseVO.success();
	}
}
