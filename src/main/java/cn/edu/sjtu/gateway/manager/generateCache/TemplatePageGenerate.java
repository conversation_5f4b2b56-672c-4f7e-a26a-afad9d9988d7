package cn.edu.sjtu.gateway.manager.generateCache;

import cn.edu.sjtu.gateway.manager.entity.TemplatePage;
import org.springframework.stereotype.Component;
import cn.edu.sjtu.gateway.vm.generateCache.BaseGenerate;

/**
 * 模版页面
 * <AUTHOR>
 */
@Component
public class TemplatePageGenerate extends BaseGenerate{
	
	public TemplatePageGenerate() {
		type();
		editMode();
	}
	
	public void type(){
		createCacheObject("type");
		cacheAdd(TemplatePage.TYPE_INDEX, "首页模版");
		cacheAdd(TemplatePage.TYPE_NEWS_LIST, "列表页模版");
		cacheAdd(TemplatePage.TYPE_NEWS_VIEW, "详情页模版");
		generateCacheFile();
	}
	
	public void editMode(){
		createCacheObject("editMode");
		cacheAdd(TemplatePage.EDIT_MODE_VISUAL, "可视化(即将废弃，不建议用)");
		cacheAdd(TemplatePage.EDIT_MODE_CODE, "纯代码编辑");
		generateCacheFile();
	}
	
}
