package cn.edu.sjtu.gateway.manager.entity;

import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * site entitiy
 * <AUTHOR>
 */
@Setter
@Entity
@Table(name = "site_data")
public class SiteData implements java.io.Serializable {
	
	private Integer id;
	private String indexDescription;

	@Id
	@Column(name = "id", unique = true, nullable = false)
	public Integer getId() {
		if(this.id == null){
			return 0;
		}
		return this.id;
	}

    @Column(name = "index_description", columnDefinition = "varchar(400) COMMENT '首页的描述' default ''")
	public String getIndexDescription() {
		return indexDescription;
	}


}