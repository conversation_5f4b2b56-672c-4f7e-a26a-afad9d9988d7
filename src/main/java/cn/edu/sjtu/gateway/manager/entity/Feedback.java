package cn.edu.sjtu.gateway.manager.entity;

import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import static javax.persistence.GenerationType.IDENTITY;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 问题反馈
 * <AUTHOR>
 */
@Setter
@Entity
@Table(name = "feedback")
public class Feedback implements java.io.Serializable {

	private Integer id;
	private Integer userid;
	private Integer addtime;
	private String text;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Integer getId() {
		return this.id;
	}

    @Column(name = "userid", columnDefinition="int(11) COMMENT '' default '0'")
	public Integer getUserid() {
		return userid;
	}

    @Column(name = "addtime", columnDefinition="int(11) COMMENT ''")
	public Integer getAddtime() {
		return addtime;
	}

    @Column(name = "text", columnDefinition="varchar(255) COMMENT '' default ''")
	public String getText() {
		return text;
	}

    @Override
	public String toString() {
		return "Feedback [id=" + id + ", userid=" + userid + ", addtime="
				+ addtime + ", text=" + text + "]";
	}
	
}