package cn.edu.sjtu.gateway.manager.entity;

import lombok.Setter;

import static javax.persistence.GenerationType.IDENTITY;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 输入模型，用户添加新闻、产品等
 * <AUTHOR>
 */
@Setter
@Entity
@Table(name = "input_model")
public class InputModel implements java.io.Serializable {
	
	private Integer id;
	//对应site.id
	private Integer siteid;
	//备注说明
	private String remark;
	//输入模型的内容
	private String text;
	//模型代码，每个网站的模型代码是唯一的
	private String codeName;
	
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Integer getId() {
		return id;
	}

    @Column(name = "siteid", columnDefinition="int(11) COMMENT '对应site.id' default '0'")
	public Integer getSiteid() {
		return siteid;
	}

    @Column(name = "text", columnDefinition="mediumtext COLLATE utf8mb4_unicode_ci COMMENT '输入模型的内容'")
	public String getText() {
		return text;
	}

    @Column(name = "remark", columnDefinition="varchar(30) COMMENT '备注说明' default ''")
	public String getRemark() {
		return remark;
	}

    @Column(name = "code_name", columnDefinition="char(30) COMMENT '模型代码，每个网站的模型代码是唯一的' default ''")
	public String getCodeName() {
		return codeName;
	}

    @Override
	public String toString() {
		return "InputModel [id=" + id + ", siteid=" + siteid + ", remark=" + remark + ", text=" + text + ", codeName="
				+ codeName + "]";
	}
	
}