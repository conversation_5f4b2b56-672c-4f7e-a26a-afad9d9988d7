package cn.edu.sjtu.gateway.manager;

import cn.edu.sjtu.gateway.manager.entity.Site;
import cn.edu.sjtu.gateway.manager.util.DomainUtil;
import cn.edu.sjtu.gateway.vm.util.SessionUtil;
import cn.edu.sjtu.gateway.vm.bean.ActiveUser;
import cn.edu.sjtu.gateway.vm.entity.User;
import cn.edu.sjtu.gateway.vm.shiro.ShiroFunc;

import cn.edu.sjtu.gateway.vm.util.SystemUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * 常用的一些函数
 * <AUTHOR>
 */
@Component
@Slf4j
public class Func extends DomainUtil{
	
	/**
	 * 判断是wap模式还是pc模式。若没有传递 client=pc，其余的统一认为是wap模式
	 * @return true:WAP； false:PC
	 */
	public static boolean getPcOrWap(HttpServletRequest request){
		String client = request.getParameter("client");
		if(client == null || client.isEmpty()){
			return true;
		}else{
            return !"pc".equals(client);
		}
	}
	
	
	/**
	 * 判断是否是CMS类型的建站
	 * @return
	 */
	public static boolean isCMS(Site site){
		//类型便是CMS类型。以后这个为判断标准
		if(site.getClient() - Site.CLIENT_CMS == 0){
			return true;
		}
		//通用模版编号为0
		if(site.getTemplateId() == null || site.getTemplateId() == 0){
			return true;
		}
		//有使用模版
        return site.getTemplateName() != null && !site.getTemplateName().isEmpty();
    }
	
	
	/**
	 * 重定向跳转至当前登录用户的网站所属类型(wap\pc\cms)的控制台，或者代理后台、超级管理员后台，又或创建网站的页面
	 * @return 重定向跳转网址。返回包含：
	 * 		<ul>
	 * 			<li>template/index.naii</li>
	 * 			<li>sites/editPcIndex.naii</li>
	 * 			<li>sites/editWapIndex.naii</li>
	 * 			<li>manager/index/index.naii 代理后台，总管理后台都是用这个</li>
	 * 		</ul>
	 */
	public static String getConsoleRedirectUrl(){
//		UserBean userBean = SessionUtil.getUserBeanForSession();
		if(!SessionUtil.isLogin()){
			return "";	//未登录
		}
		
		//先判断此用户是超级管理员或者代理商
		if(cn.edu.sjtu.gateway.vm.util.SessionUtil.getAgency() != null){
			//有代理信息，跳转到代理后台
			return "agency/index.naii";
		}else if (cn.edu.sjtu.gateway.vm.Func.isAuthorityBySpecific(Objects.requireNonNull(ShiroFunc.getUser()).getAuthority(), SystemUtil.get("ROLE_SUPERADMIN_ID"))) {
			//超级管理员
			return "manager/index/index.naii";
		}
		
		//如果网站为空，那么可能是此用户还没有网站
		//跳转到创建网站界面
		Site site = SessionUtil.getSite();
		if(site == null){
			//既不是代理，也不是超级管理员，那肯定就是用户权限了。用户权限没有网站，那就跳转到网站创建页面
			//v3.9以后，这种情况是不存在的。账号跟网站是一块创建的
			//临时这个链接
			return "template/index.naii";
		}
		
		if(site.getClient() - Site.CLIENT_CMS == 0){
			return "template/index.naii";
		}else if(site.getClient() - Site.CLIENT_PC == 0){
			return "sitePc/index.naii";
		}else if(site.getClient() - Site.CLIENT_WAP == 0){
			return "siteWap/index.naii";
		}else{
			log.info("--------Func.getConsoleRedirectUrl 未发现是神马的。siteid:{}", site.getId());
		}
		return "";
	}
	
	/**
	 * 判断当前用户是否是超级管理员，有总管理后台权限
	 * @return true:有总管理后台的权限；  false：没有
	 */
	public static boolean haveSuperManagerAuth(){
		User user = ShiroFunc.getUser();
		if(user == null){
			//未登录，那就直接是false
			return false;
		}
		
		if(cn.edu.sjtu.gateway.vm.Func.isAuthorityBySpecific(user.getAuthority(), SystemUtil.get("ROLE_SUPERADMIN_ID"))){
			return true;
		}
		return false;
	}
	
	public Func() {
	}
	
	/**
	 * 判断当前用户是否是代理商，有代理后台权限
	 * @return true:有代理后台的权限；  false：没有
	 */
	public static boolean haveAgencyAuth(){
		if(cn.edu.sjtu.gateway.vm.Func.isAuthorityBySpecific(ShiroFunc.getUser().getAuthority(), SystemUtil.get("ROLE_SUPERADMIN_ID"))){
			return true;
		}
		return false;
	}
	
	/**
	 * 获取当前登录用户的session缓存信息
	 * @return
     */
	public static cn.edu.sjtu.gateway.manager.bean.UserBean getUserBeanForShiroSession(){
		ActiveUser activeUser = SessionUtil.getActiveUser();
		cn.edu.sjtu.gateway.manager.bean.UserBean oldub = new cn.edu.sjtu.gateway.manager.bean.UserBean();
		oldub.setInputModelMap(SessionUtil.getInputModel());
		oldub.setMyAgency(cn.edu.sjtu.gateway.agencymanager.Func.getMyAgency());
		oldub.setMyAgencyData(cn.edu.sjtu.gateway.agencymanager.Func.getMyAgencyData());
		oldub.setParentAgency(SessionUtil.getParentAgency());
		oldub.setParentAgencyData(SessionUtil.getParentAgencyData());
		oldub.setSite(SessionUtil.getSite());
		oldub.setSiteColumnMap(SessionUtil.getSiteColumnMap());
		oldub.setSiteMenuRole(SessionUtil.getSiteMenuRole());
		oldub.setTemplateVarCompileDataMap(SessionUtil.getTemplateVarCompileDataMap());
		oldub.setTemplateVarMapForOriginal(SessionUtil.getTemplateVarMapForOriginal());
		if(activeUser != null){
			oldub.setPluginDataMap(activeUser.getPluginMap());
		}
		
		return oldub;
	}
}
