package cn.edu.sjtu.gateway.manager.service.impl;

import cn.edu.sjtu.gateway.manager.entity.SiteVar;
import cn.edu.sjtu.gateway.manager.service.SiteVarService;
import cn.edu.sjtu.gateway.manager.util.CacheUtil;
import cn.edu.sjtu.gateway.vm.service.SqlService;

import com.alibaba.fastjson2.JSONObject;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service("siteVarService")
public class SiteVarServiceImpl implements SiteVarService {

	private final SqlService sqlService;

	public SiteVarServiceImpl(SqlService sqlService) {
		this.sqlService = sqlService;
	}

	@Override
	public JSONObject getVar(int siteid) {
		//判断缓存中是否有
		String var = CacheUtil.getSiteVar(siteid);
		if(var == null){
			//缓存中没有，那么从数据库中取
			SiteVar siteVar = sqlService.findById(SiteVar.class, siteid);
			if(siteVar == null){
				//没有，返回一个不包含任何数据的JSON对象
				return new JSONObject();
			}
			//将查询出来的数据进行缓存
			CacheUtil.setSiteVar(siteVar);
			var = siteVar.getText();
		}
		
		return JSONObject.parseObject(var);
	}

	@Override
	public JSONObject getVar(int siteid, String key) {
		Object obj = getVar(siteid).get(key);
		if(obj == null){
			return new JSONObject();
		}
		return (JSONObject)obj;
	}

	@Override
	public void setVar(int siteid, SiteVar siteVar) {
		CacheUtil.setSiteVar(siteVar);
	}

}
