package cn.edu.sjtu.gateway.manager.generateCache;

import cn.edu.sjtu.gateway.manager.entity.Site;
import org.springframework.stereotype.Component;
import cn.edu.sjtu.gateway.vm.generateCache.BaseGenerate;

/**
 * 站点
 * <AUTHOR>
 */
@Component
public class SiteGenerate extends BaseGenerate {
	public SiteGenerate() {
		mShowBanner();
		client();
		state();
	}
	
	public void mShowBanner(){
		createCacheObject("mShowBanner");
		cacheAdd(Site.MSHOWBANNER_SHOW, "显示");
		cacheAdd(Site.MSHOWBANNER_HIDDEN, "隐藏");
		generateCacheFile();
		
		//weui
		WeUI we = new WeUI();
		we.setObjName("mShowBanner");
		we.appendDataList("显示", Site.MSHOWBANNER_SHOW+"");
		we.appendDataList("隐藏", Site.MSHOWBANNER_HIDDEN+"");
		we.generateCacheFile();
	}
	
	public void client(){
		createCacheObject("client");
		cacheAdd(Site.CLIENT_PC, "电脑端(旧版，已不推荐)");
		cacheAdd(Site.CLIENT_WAP, "手机端(旧版，已不推荐)");
		cacheAdd(Site.CLIENT_CMS, "CMS(新版，推荐)");
		generateCacheFile();
	}
	
	public void state(){
		createCacheObject("state");
		cacheAdd(Site.STATE_NORMAL, "正常");
		cacheAdd(Site.STATE_FREEZE, "冻结");
		generateCacheFile();
	}
}
