package cn.edu.sjtu.gateway.manager.bean;

import cn.edu.sjtu.gateway.manager.entity.InputModel;
import cn.edu.sjtu.gateway.manager.entity.Site;
import cn.edu.sjtu.gateway.manager.entity.SiteColumn;
import cn.edu.sjtu.gateway.vm.util.SessionUtil;
import cn.edu.sjtu.gateway.manager.vo.TemplateVarVO;
import cn.edu.sjtu.gateway.agencymanager.entity.Agency;
import cn.edu.sjtu.gateway.agencymanager.entity.AgencyData;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户登录后，即跟随用户Session一块的缓存，其在Shiro中
 * <AUTHOR>
 * 已废弃。可以直接使用 {@link SessionUtil} 来获取响应信息。此保留着只是为了适配之前的一些插件
 */
@Setter
public class UserBean {
	@Getter
    private Agency myAgency;										//我的代理信息，如果我是代理的话，才有内容
	@Getter
    private AgencyData myAgencyData;								//我的代理信息-变长表的信息
	@Getter
    private Site site;												//当前用户的站点信息，当前用户所能管理的网站信息。如果当前用户是网站用户的话。
	@Getter
    private Agency parentAgency;									//我的上级代理信息，当前用户的上级代理信息
	@Getter
    private AgencyData parentAgencyData;							//我的上级代理信息-变长表的信息
	@Getter
    private Map<String, String> templateVarCompileDataMap;			//我当前高级模式使用的模版变量，可能是已被编译(替换)过标签的内容了。key:template.name	value:模版变量的内容
	@Getter
    private Map<String, TemplateVarVO> templateVarMapForOriginal;	//原始的模版变量，其内包含模版变量的数据库中的原始内容. key:templateVar.name
	@Getter
    private Map<Integer, SiteColumn> siteColumnMap;					//缓存的当前用户的栏目信息 key:siteColumn.id（CMS模式才会使用此缓存）登录时不会缓存此处，在使用时才会缓存
	@Getter
    private Map<Integer, InputModel> inputModelMap;					//当前CMS网站的输入模型，由 inputModelService 初始化赋予值。在用户进入内容管理，编辑时才会判断，如果此为null，才会从数据库加载数据
	private Map<String, String> siteMenuRole;						//网站管理后台的左侧菜单使用权限，只限网站用户有效，v4.9版本增加。 key: id，也就是左侧菜单的唯一id标示，比如模版管理是template，一键部署是 shengchengzhengzhan， 至于value，无意义，1即可 
	private Map<String, Object> pluginDataMap;						//缓存 功能插件 的一些信息。比如关键词插件，用户登录成功后，当编辑过一篇文章时，将关键词缓存到这里，在编辑其他文章时直接从这里取数据即可 。 key: 插件的id,如 keyword   value：插件存储的数据

    public Map<String, String> getSiteMenuRole() {
		if(siteMenuRole == null){
			siteMenuRole = new HashMap<String, String>();
		}
		return siteMenuRole;
	}

    public Map<String, Object> getPluginDataMap() {
		if(pluginDataMap == null){
			pluginDataMap = new HashMap<String, Object>();
		}
		return pluginDataMap;
	}

}
