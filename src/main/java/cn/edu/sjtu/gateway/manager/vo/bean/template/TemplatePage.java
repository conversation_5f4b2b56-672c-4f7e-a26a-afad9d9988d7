package cn.edu.sjtu.gateway.manager.vo.bean.template;

/**
 * 模版导入，导入模版页面的内容，转换为json，对象
 * <AUTHOR>
 */
public class TemplatePage {
	private cn.edu.sjtu.gateway.manager.entity.TemplatePage templatePage;
	private String text;	//模版页面的内容
	public cn.edu.sjtu.gateway.manager.entity.TemplatePage getTemplatePage() {
		return templatePage;
	}
	public void setTemplatePage(cn.edu.sjtu.gateway.manager.entity.TemplatePage templatePage) {
		this.templatePage = templatePage;
	}
	public String getText() {
		return text;
	}
	public void setText(String text) {
		this.text = text;
	}
	
	
	
}
