package cn.edu.sjtu.gateway.manager.bean;


import lombok.Data;

/**
 * 信息相关，相当于 {@link cn.edu.sjtu.gateway.manager.entity.News}，只不过加了排序功能
 * <AUTHOR>
 */
@Data
public class News implements Comparable {
	private cn.edu.sjtu.gateway.manager.entity.News news;
	//相当于news.id，排序使
	private Integer rank;
	
	@Override
    public int compareTo(Object o) {
		News obj = (News)o;
		return this.rank.compareTo(obj.getRank());
	}

}
