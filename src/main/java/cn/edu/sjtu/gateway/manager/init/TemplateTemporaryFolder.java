package cn.edu.sjtu.gateway.manager.init;


import cn.edu.sjtu.gateway.vm.util.SystemUtil;
import cn.edu.sjtu.gateway.tools.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;

/**
 * 初始化模版文件夹，自动创建模版包上传的临时文件
 * <AUTHOR>
 *
 */
@Component
@Slf4j
public class TemplateTemporaryFolder {
	//存放上传模版zip包的临时文件夹
	public static String folderPath;
	//过期时间120秒
	public static final int TIMEOUT = 120;
	
	static{
		new TemplateTemporaryFolder();
		
		//定时巡检，删除过时文件
		new Thread(new Runnable() {
			@Override
			public void run() {
				log.info("start template temporary folder timing check thread.");
				while (true) {
					try {
						Thread.sleep(TIMEOUT*1000);
					} catch (InterruptedException e) {
						log.error("错误信息：------>", e);
					}
					
					File file = new File(folderPath);
                    File subFiles[] = file.listFiles();
					if(subFiles != null){
                        for (File subFile : subFiles) {
                            if ((subFile.lastModified() + (TIMEOUT * 1000)) < DateUtil.timeForUnix13()) {
                                //如果文件创建时间超过2分钟，那么就删除掉这个临时的模版文件夹
                                subFile.delete();
                                deleteFile(subFile);
                            }
                        }
					}
					
				}
			}
		}).start();
		
	}
	
	public TemplateTemporaryFolder() {
		if(SystemUtil.getProjectPath().indexOf("/target/classes") > 0){
			//包含这个路径，那么认为是在开发环境中
			folderPath = SystemUtil.getProjectPath()+"templateTemporaryFile/";
		}else{
			//正式运行环境
			folderPath = SystemUtil.getProjectPath()+"WEB-INF/classes/templateTemporaryFile/";
		}
		
		File templateTemporaryFile = new File(folderPath);
		if(!templateTemporaryFile.exists()){
			//如果文件夹不存在，那么自动创建
			templateTemporaryFile.mkdir();
			log.info("auto create template temporary floder : "+folderPath);
		}
	}
	
	/**
	 * 删除文件
	 * @param file
	 */
	public static void deleteFile(File file){
		if(file.isDirectory()){
			//如果是目录，则便利其下的文件，将其删除掉
			File[] subFiles = file.listFiles();
            if (subFiles != null) {
                for (File subFile : subFiles) {
                    deleteFile(subFile);
                }
            }
        }
		//将当前文件或者文件夹删除掉
		file.delete();
	}
	
}
