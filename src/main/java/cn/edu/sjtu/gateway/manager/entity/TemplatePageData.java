package cn.edu.sjtu.gateway.manager.entity;

import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 某个模版页的具体内容
 */
@Setter
@Entity
@Table(name = "template_page_data")
public class TemplatePageData implements java.io.Serializable {
	
	private Integer id;
	private String text;

	@Id
	@Column(name = "id", unique = true, nullable = false)
	public Integer getId() {
		return this.id;
	}

    @Column(name = "text", columnDefinition="mediumtext COLLATE utf8mb4_unicode_ci")
	public String getText() {
		return this.text;
	}

    @Override
	public String toString() {
		return "TemplatePageData [id=" + id + ", text=" + text + "]";
	}

}