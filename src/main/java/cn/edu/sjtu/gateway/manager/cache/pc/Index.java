package cn.edu.sjtu.gateway.manager.cache.pc;

import cn.edu.sjtu.gateway.manager.cache.GenerateHTML;
import cn.edu.sjtu.gateway.manager.entity.Site;

/**
 * PC首页的一些处理
 * <AUTHOR>
 */
public class Index {
	
	/**
	 * 更新首页的 keywords
	 * @param site {@link cn.edu.sjtu.gateway.manager.entity.Site}
	 * @param keywords 要更新为的 keywords，如   寻仙,寻仙网
	 */
	public static void updateKeywords(Site site,String keywords){
		GenerateHTML gh = new GenerateHTML(site);
		String html = gh.getGeneratePcIndexHtml();
		html = html.replaceAll("<meta name=\"keywords\" content=\".*\" />", "<meta name=\"keywords\" content=\""+keywords+"\" />");
		gh.generateIndexHtml(html);
	}
	
	/**
	 * 更新首页的 description
	 * @param site {@link cn.edu.sjtu.gateway.manager.entity.Site}
	 * @param description 要更新为的 description
	 */
	public static void updateDescription(Site site,String description){
		GenerateHTML gh = new GenerateHTML(site);
		String html = gh.getGeneratePcIndexHtml();
		html = html.replaceAll("<meta name=\"description\" content=\".*\" />", "<meta name=\"description\" content=\""+description+"\" />");
		gh.generateIndexHtml(html);
	}
	
	/**
	 * 更新首页的 title 标题
	 * @param site {@link cn.edu.sjtu.gateway.manager.entity.Site}
	 * @param name 要更改的站点的名字
	 */
	public static void updateSiteName(Site site,String name){
		GenerateHTML gh = new GenerateHTML(site);
		String html = gh.getGeneratePcIndexHtml();
		html = html.replaceAll("<title>.*</title>", "<title>"+name+"</title>");
		gh.generateIndexHtml(html);
	}
	
}
