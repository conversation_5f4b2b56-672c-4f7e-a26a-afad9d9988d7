package cn.edu.sjtu.gateway.manager.service.impl;

import cn.edu.sjtu.gateway.manager.G;
import cn.edu.sjtu.gateway.manager.bean.NewsDataBean;
import cn.edu.sjtu.gateway.manager.cache.GenerateHTML;
import cn.edu.sjtu.gateway.manager.cache.SiteCache;
import cn.edu.sjtu.gateway.manager.cache.Template;
import cn.edu.sjtu.gateway.manager.cache.pc.IndexAboutUs;
import cn.edu.sjtu.gateway.manager.cache.pc.IndexNews;
import cn.edu.sjtu.gateway.manager.entity.Carousel;
import cn.edu.sjtu.gateway.manager.entity.News;
import cn.edu.sjtu.gateway.manager.entity.NewsData;
import cn.edu.sjtu.gateway.manager.entity.Site;
import cn.edu.sjtu.gateway.manager.entity.SiteColumn;
import cn.edu.sjtu.gateway.manager.entity.SiteData;
import cn.edu.sjtu.gateway.manager.service.NewsService;
import cn.edu.sjtu.gateway.manager.service.SiteColumnService;
import cn.edu.sjtu.gateway.manager.service.SiteService;
import cn.edu.sjtu.gateway.vm.util.SessionUtil;
import cn.edu.sjtu.gateway.manager.vo.IndexVO;
import cn.edu.sjtu.gateway.manager.vo.SiteRemainHintVO;
import cn.edu.sjtu.gateway.manager.vo.SiteVO;
import cn.edu.sjtu.gateway.manager.vo.bean.TemplateCommon;
import cn.edu.sjtu.gateway.agencymanager.entity.Agency;
import cn.edu.sjtu.gateway.vm.dao.SqlDAO;
import cn.edu.sjtu.gateway.vm.util.AttachmentUtil;
import cn.edu.sjtu.gateway.vm.util.SystemUtil;
import cn.edu.sjtu.gateway.vm.vo.BaseVO;
import cn.edu.sjtu.gateway.tools.DateUtil;
import cn.edu.sjtu.gateway.tools.StringUtil;
import cn.edu.sjtu.gateway.tools.file.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("siteService")
@Slf4j
public class SiteServiceImpl implements SiteService {
    private final SqlDAO sqlDAO;
    private final NewsService newsService;
    private final SiteColumnService siteColumnService;

    public SiteServiceImpl(SqlDAO sqlDAO, NewsService newsService, SiteColumnService siteColumnService) {
        this.sqlDAO = sqlDAO;
        this.newsService = newsService;
        this.siteColumnService = siteColumnService;
    }

    @Override
    public void generateSiteIndex(Site site) {
        GenerateHTML gh = new GenerateHTML(site);

        String html = null;
        if (site.getClient() - Site.CLIENT_PC == 0) {
            //刷新pc首页的时候，需要刷新pc首页中间的那些数据，还需要到数据库里将其查询出来

        } else if (site.getClient() - Site.CLIENT_WAP == 0) {
            html = gh.wapIndex();
        } else {
            //估计是CMS了，但是cms不会用到此处。待整理
        }

        if (html != null) {
            AttachmentUtil.uploadStringFile("site/" + site.getId() + "/index.html", html);
        }
    }

    @Override
    public BaseVO refreshSiteGenerateHtml(HttpServletRequest request) {
        Site site = SessionUtil.getSite();

        //生成新闻/图文的栏目列表
        List<SiteColumn> siteColumnList = sqlDAO.findBySqlQuery("SELECT * FROM site_column WHERE siteid = " + site.getId() + " AND used = " + SiteColumn.USED_ENABLE, SiteColumn.class);
        Map<Integer, SiteColumn> siteColumnMap = new HashMap<Integer, SiteColumn>();    //可以根据siteColumn.id取出siteColumn数据对象
        for (SiteColumn siteColumn : siteColumnList) {
            siteColumnMap.put(siteColumn.getId(), siteColumn);
            //如果是新闻列表或者图文列表，则生成列表页面
            if (siteColumn.getType() - SiteColumn.TYPE_NEWS == 0 || siteColumn.getType() - SiteColumn.TYPE_IMAGENEWS == 0) {
                newsService.generateListHtml(site, siteColumn);
            }
        }

        //生成内容页面
        List<News> listNews = sqlDAO.findBySqlQuery("SELECT news.* FROM news WHERE news.siteid = " + site.getId() + " AND news.status = " + News.STATUS_NORMAL + " ORDER BY news.id DESC", News.class);
        List<NewsData> listNewsData = sqlDAO.findBySqlQuery("SELECT news_data.* FROM news,news_data WHERE news.id = news_data.id AND news.siteid = " + site.getId() + " AND news.status = " + News.STATUS_NORMAL, NewsData.class);
        for (int i = 0; i < listNews.size(); i++) {
            News news = listNews.get(i);
            NewsData newsData = listNewsData.get(i);
            newsService.generateViewHtml(site, news, siteColumnMap.get(news.getCid()), new NewsDataBean(newsData), request);
        }

        //首页生成
        if (site.getClient().equals(Site.CLIENT_WAP)) {
            generateSiteIndex(site);            //生成WAP首页
        } else {
            /*PC 首页生成*/
            //获取关于我们内容
            News aboutUsNews = sqlDAO.findAloneBySqlQuery("SELECT * FROM news WHERE cid = " + site.getAboutUsCid(), News.class);
            NewsData aboutUsNewsData = null;
            if (aboutUsNews != null) {
                aboutUsNewsData = sqlDAO.findAloneBySqlQuery("SELECT * FROM news_data WHERE id = " + aboutUsNews.getId(), NewsData.class);
            }

            //找出关于我们、新闻、图文列表的三个栏目
            SiteColumn aboutUsSiteColumn = sqlDAO.findAloneBySqlQuery("SELECT * FROM site_column WHERE id = " + site.getAboutUsCid(), SiteColumn.class);
            SiteColumn NewsSiteColumn = null;
            SiteColumn ImagesSiteColumn = null;
            for (SiteColumn sc : siteColumnList) {
                if (NewsSiteColumn == null && sc.getType() - SiteColumn.TYPE_NEWS == 0) {
                    NewsSiteColumn = sc;
                    continue;
                }
                if (ImagesSiteColumn == null && sc.getType() - SiteColumn.TYPE_IMAGENEWS == 0) {
                    ImagesSiteColumn = sc;
                    continue;
                }
            }

            //获取网站的信息，如首页描述等。若没有，则自动创建
            SiteData siteData = sqlDAO.findById(SiteData.class, site.getId());
            if (siteData == null) {
                siteData = new SiteData();
                siteData.setId(site.getId());
                siteData.setIndexDescription(site.getName());
                sqlDAO.save(siteData);
            }

            //生成PC首页
            generateSitePcIndex(site, aboutUsSiteColumn, NewsSiteColumn, ImagesSiteColumn, siteData);

            //刷新关于我们的内容模块
            NewsData ndAU = aboutUsNewsData;
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e1) {
                log.error("错误信息：------>" + e1);
            }
            IndexAboutUs.refreshIndexData(site, aboutUsSiteColumn, aboutUsNews, ndAU.getText());

            //拿到新闻、图文的前10条数据
            List<News> newsList = new ArrayList<News>();
            List<News> imagesList = new ArrayList<News>();
            for (News listNew : listNews) {
                if (newsList.size() == 10 && imagesList.size() == 10) {
                    break;
                }

                if (newsList.size() < 10 && NewsSiteColumn != null && listNew.getCid() - NewsSiteColumn.getId() == 0) {
                    newsList.add(listNew);
                    continue;
                }

                if (imagesList.size() < 10 && ImagesSiteColumn != null && listNew.getCid() - ImagesSiteColumn.getId() == 0) {
                    imagesList.add(listNew);
                    continue;
                }
            }

            //刷新首页的新闻模块
            SiteColumn scNews = NewsSiteColumn;
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e1) {
                log.error("错误信息：------>" + e1);
            }
            IndexNews.refreshIndexData(site, scNews, newsList);


            //刷新首页的图片模块
            SiteColumn scImages = ImagesSiteColumn;
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e1) {
                log.error("错误信息：------>" + e1);
            }
            IndexNews.refreshIndexData(site, scImages, imagesList);

        }

        return new BaseVO();
    }

    @Override
    /**
     * 刷新首页（PC）
     *
     * @return
     */
    public BaseVO refreshIndex(Site site) {
        BaseVO vo = new BaseVO();
        if (site == null) {
            vo.setBaseVO(BaseVO.FAILURE, "传入的站点不存在");
            return vo;
        }
        GenerateHTML gh = new GenerateHTML(site);
        String indexHtml = gh.pcIndex();        //获取到首页的模版，替换掉common的

        //获取关于我们内容
        News aboutUsNews = sqlDAO.findAloneBySqlQuery("SELECT * FROM news WHERE cid = " + site.getAboutUsCid(), News.class);
        NewsData aboutUsNewsData = null;
        if (aboutUsNews != null) {
            aboutUsNewsData = sqlDAO.findAloneBySqlQuery("SELECT * FROM news_data WHERE id = " + aboutUsNews.getId(), NewsData.class);
        }

        //找出新闻、图文列表的三个栏目
        SiteColumn aboutUsSiteColumn = sqlDAO.findAloneBySqlQuery("SELECT * FROM site_column WHERE id = " + site.getAboutUsCid(), SiteColumn.class);
        SiteColumn newsSiteColumn = null;
        SiteColumn imagesSiteColumn = null;

        List<SiteColumn> siteColumnList = sqlDAO.findBySqlQuery("SELECT * FROM site_column WHERE siteid = " + site.getId() + " AND used = " + SiteColumn.USED_ENABLE, SiteColumn.class);
        for (SiteColumn sc : siteColumnList) {
            if (newsSiteColumn == null && sc.getType() - SiteColumn.TYPE_NEWS == 0) {
                newsSiteColumn = sc;
                continue;
            }
            if (imagesSiteColumn == null && sc.getType() - SiteColumn.TYPE_IMAGENEWS == 0) {
                imagesSiteColumn = sc;
                continue;
            }
        }

        //关于我们栏目属性替换
        indexHtml = indexHtml.replaceAll("<!--Index_Content_AboutUs_Start--><!--id=0-->", "<!--Index_Content_AboutUs_Start--><!--id=" + (aboutUsSiteColumn == null ? "0" : "" + aboutUsSiteColumn.getId()) + "-->");
        //新闻栏目属性替换
        indexHtml = indexHtml.replaceAll("<!--Index_Content_NewsList_Start--><!--id=0-->", "<!--Index_Content_NewsList_Start--><!--id=" + (newsSiteColumn == null ? "0" : "" + newsSiteColumn.getId()) + "-->");
        //图文栏目属性替换
        indexHtml = indexHtml.replaceAll("<!--Index_Content_NewsImageList_Start--><!--id=0-->", "<!--Index_Content_NewsImageList_Start--><!--id=" + (imagesSiteColumn == null ? "0" : "" + imagesSiteColumn.getId()) + "-->");

        //获取网站的信息，如首页描述等。若没有，则自动创建
        SiteData siteData = sqlDAO.findById(SiteData.class, site.getId());
        if (siteData == null) {
            siteData = new SiteData();
            siteData.setId(site.getId());
            siteData.setIndexDescription(site.getName());
            sqlDAO.save(siteData);
        }

        //刷新关于我们的内容模块
        IndexVO indexVO_aboutus = null;
        if (aboutUsSiteColumn != null) {
            indexVO_aboutus = IndexAboutUs.refreshIndexData(site, aboutUsSiteColumn, aboutUsNews, aboutUsNewsData.getText(), indexHtml);
        }
        if (indexVO_aboutus != null && indexVO_aboutus.getResult() == IndexVO.SUCCESS) {
            indexHtml = indexVO_aboutus.getText();
        }

        //拿到新闻、图文的前10条数据
        //所有的News表的信息
        List<News> listNews = sqlDAO.findBySqlQuery("SELECT news.* FROM news WHERE news.siteid = " + site.getId() + " AND news.status = " + News.STATUS_NORMAL + " ORDER BY news.id DESC", News.class);
        List<News> newsList = new ArrayList<News>();
        List<News> imagesList = new ArrayList<News>();
        for (News listNew : listNews) {
            if (newsList.size() == 10 && imagesList.size() == 10) {
                break;
            }

            if (newsList.size() < 10 && newsSiteColumn != null && listNew.getCid() - newsSiteColumn.getId() == 0) {
                newsList.add(listNew);
                continue;
            }

            if (imagesList.size() < 10 && imagesSiteColumn != null && listNew.getCid() - imagesSiteColumn.getId() == 0) {
                imagesList.add(listNew);
                continue;
            }
        }

        //刷新首页的新闻模块
        IndexVO indexVO_news = IndexNews.refreshIndexData(site, newsSiteColumn, newsList, indexHtml);
        if (indexVO_news.getResult() == IndexVO.SUCCESS) {
            indexHtml = indexVO_news.getText();
        }

        //刷新首页的图片模块
        IndexVO indexVO_image = IndexNews.refreshIndexData(site, imagesSiteColumn, imagesList, indexHtml);
        if (indexVO_image.getResult() == IndexVO.SUCCESS) {
            indexHtml = indexVO_image.getText();
        }

        //生成，写出首页
        gh.generateIndexHtml(indexHtml, siteData);

        return new BaseVO();
    }


    @Override
    public TemplateCommon getTemplateCommonHtml(Site site, String title, Model model) {
        if (site == null) {
            site = new Site();
            site.setId(0);
            site.setName("");
            site.setTemplateId(1);
        }

        TemplateCommon tc = new TemplateCommon();

        GenerateHTML gh = new GenerateHTML(site);
        Template temp = new Template(site);
        gh.setEditMode(true);

        String headHtml = FileUtil.read(SystemUtil.getProjectPath() + "/static/template/" + gh.templateId + "/common/head.html");
        headHtml = gh.replacePublicTag(headHtml);
        headHtml = headHtml.replaceAll(GenerateHTML.regex("title"), title);
        headHtml = temp.replaceForEditModeTag(headHtml);

        String topHtml = FileUtil.read(SystemUtil.getProjectPath() + "/static/template/" + gh.templateId + "/common/top.html");
        topHtml = gh.replacePublicTag(topHtml);
        topHtml = topHtml.replaceAll(GenerateHTML.regex("title"), title);
        topHtml = temp.replaceForEditModeTag(topHtml);

        String footHtml = FileUtil.read(SystemUtil.getProjectPath() + "/static/template/" + gh.templateId + "/common/foot.html");
        footHtml = gh.replacePublicTag(footHtml);
        footHtml = temp.replaceForEditModeTag(footHtml);

        model.addAttribute("headHtml", headHtml);
        model.addAttribute("topHtml", topHtml);
        model.addAttribute("footHtml", footHtml);

        return tc;
    }

    @Override
    public SiteVO saveSite(Site s, int siteUserId, HttpServletRequest request) {
        SiteVO baseVO = new SiteVO();
        String name = "";
        if (s.getName() != null && !s.getName().isEmpty()) {
            name = StringUtil.filterXss(s.getName());
        }
        if (name.isEmpty()) {
            baseVO.setBaseVO(BaseVO.FAILURE, "您的站点叫什么名字呢?");
            return baseVO;
        }

        Site site;
        if (s.getId() != null && s.getId() > 0) {
            //编辑
            //取出当前登录的站点的信息
            Site currentLoginSite = SessionUtil.getSite();

            site = sqlDAO.findById(Site.class, s.getId());
            if (site == null) {
                baseVO.setBaseVO(BaseVO.FAILURE, "要编辑的站点不存在");
                return baseVO;
            }
            if (site.getId() - currentLoginSite.getId() != 0) {
                baseVO.setBaseVO(BaseVO.FAILURE, "站点不属于您，无法操作！");
                return baseVO;
            }
        } else {
            site = new Site();
            site.setAddtime(DateUtil.timeForUnix10());
            site.setUserid(siteUserId);
            site.setDomain(siteUserId + DateUtil.currentDate("mmss"));
        }

        site.setAddress(s.getAddress());
        site.setCompanyName(s.getCompanyName());
        site.setUsername(StringUtil.filterXss(s.getUsername()));
        site.setName(name);
        site.setPhone(s.getPhone());
        site.setQq(StringUtil.filterXss(s.getQq()));
        site.setBindDomain(s.getBindDomain() == null ? "" : StringUtil.filterXss(s.getBindDomain()));
        site.setExpiretime(s.getExpiretime());

        if (s.getClient() - Site.CLIENT_PC == 0) {
            //通用模版，电脑站
            site.setClient(s.getClient());
        } else if (s.getClient() - Site.CLIENT_CMS == 0) {
            //高级自定义模版CMS
            site.setTemplateName(StringUtil.filterXss(s.getTemplateName()));
            site.setClient(s.getClient());
        } else {
            //剩下的都是通用模版，手机站
            site.setClient(Site.CLIENT_WAP);
        }


        //非CMS类型，将会赋予其一个模版
        if (s.getClient() - Site.CLIENT_CMS != 0) {
            if (s.getTemplateId() != null && s.getTemplateId() > 0) {
                site.setTemplateId(s.getTemplateId());
            } else if (s.getId() == null || s.getId() == 0) {
                //如果是新增，默认添加一个模版
                if (site.getClient() - Site.CLIENT_PC == 0) {
                    site.setTemplateId(G.TEMPLATE_PC_DEFAULT);
                } else {
                    site.setTemplateId(G.TEMPLATE_WAP_DEFAULT);
                }
            }
            site.setTemplateName("");    //避免sqlite 数据库中为null
        } else {
            //CMS类型，填充一些数据
            site.setTemplateId(0);
        }

        //若没有设置关键词，自动创建关键词
        String keywords = s.getKeywords();
        if (keywords == null || keywords.length() == 0) {
            keywords = site.getName();
            if (site.getCompanyName() != null && !site.getName().equals(site.getCompanyName()) && site.getCompanyName().length() > 0) {
                keywords = keywords + "," + site.getCompanyName();
            }
            if (site.getUsername() != null && !site.getName().equals(site.getUsername()) && site.getUsername().length() > 0) {
                keywords = keywords + "," + site.getUsername();
            }
        }
        //v6.0增加，英文站会有问题，会导致吧img标签中的url破坏的情况
        //site.setKeywords(SafetyUtil.filter(keywords));

        //填充默认数据
        if (site.getLogo() == null) {
            site.setLogo("");
        }
        if (site.getColumnId() == null) {
            site.setColumnId("");
        }
        if (site.getAboutUsCid() == null) {
            site.setAboutUsCid(0);
        }

        sqlDAO.save(site);
        if (site.getId() > 0) {
            List<SiteColumn> columnList = new ArrayList<SiteColumn>();

            if (s.getId() > 0) {
                //看是否是新增加的站点，若只是修改

                if (s.getClient() - Site.CLIENT_CMS == 0) {
                    //CMS类型，无需变动
                } else {
                    //通用模版，要有刷新

                    //修改,只需要变化首页即可
                    generateSiteIndex(site);
                }
            } else {
                //新增站点

                if (s.getClient() - Site.CLIENT_CMS == 0) {
                    /*
                     * v2.7更改，创建CMS时，只是创建一个空白的CMS网站。模版的选择在登录某个CMS网站后进行操作
                     */
                } else {
                    //如果是通用网站模版

                    //新增

                    //站点创建完毕后，要给站点默认设置description首页描述
                    String description = site.getName();
                    if (site.getCompanyName() != null && !site.getName().equals(site.getCompanyName()) && site.getCompanyName().length() > 0) {
                        description = description + "，隶属于" + site.getCompanyName();
                    }
                    if (site.getAddress() != null && !site.getName().equals(site.getAddress()) && site.getAddress().length() > 0) {
                        description = description + "，地理位置位于" + site.getAddress();
                    }
                    if (site.getUsername() != null && !site.getName().equals(site.getUsername()) && site.getUsername().length() > 0) {
                        description = description + "，业务负责人：" + site.getUsername();
                    }
                    if (site.getPhone() != null && site.getPhone().length() > 0) {
                        description = description + "，联系电话：" + site.getPhone();
                    }
                    SiteData siteData = new SiteData();
                    siteData.setId(site.getId());
                    siteData.setIndexDescription(description);
                    sqlDAO.save(siteData);

                    //站点创建完毕后，默认给站点添加一个导航栏目：关于我们
                    SiteColumn sc = new SiteColumn();
                    sc.setIcon(SystemUtil.get("MASTER_SITE_URL") + "default/aboutUs.jpg");
                    sc.setName("关于我们");
                    sc.setParentid(0);
                    sc.setRank(1);
                    sc.setSiteid(site.getId());
                    sc.setType(SiteColumn.TYPE_PAGE);
                    sc.setUsed(SiteColumn.USED_ENABLE);
                    sc.setUserid(siteUserId);
                    sqlDAO.save(sc);

                    //默认给导航栏目：关于我们，创建一个独立页面
                    News news = new News();
                    news.setAddtime(DateUtil.timeForUnix10());
                    news.setCid(sc.getId());
                    news.setIntro(sc.getName());
                    news.setSiteid(site.getId());
                    news.setStatus(News.STATUS_NORMAL);
                    news.setTitle(sc.getName());
                    news.setTitlepic(SystemUtil.get("MASTER_SITE_URL") + "default/aboutUs.jpg");

                    news.setType(News.TYPE_PAGE);
                    news.setUserid(siteUserId);
                    news.setLegitimate(News.LEGITIMATE_OK);
                    sqlDAO.save(news);
                    NewsData newsData = new NewsData(news.getId(), news.getIntro());
                    sqlDAO.save(newsData);

                    //默认给站点添加一个Banner轮播图,创建轮播图数据js缓存
                    List<Carousel> carouselList = new ArrayList<Carousel>();
                    Carousel carousel = new Carousel();
                    carousel.setAddtime(DateUtil.timeForUnix10());
                    carousel.setImage(AttachmentUtil.netUrl() + "default/banner.jpg");
                    carousel.setIsshow(Carousel.ISSHOW_SHOW);
                    carousel.setSiteid(site.getId());
                    carousel.setUserid(siteUserId);
                    carousel.setUrl("");
                    carousel.setType(Carousel.TYPE_DEFAULT_PAGEBANNER);
                    sqlDAO.save(carousel);
                    carouselList.add(carousel);
                    new SiteCache().carousel(carouselList, site);    //创建轮播图缓存数据

                    //将新增加的关于我们的SiteColumn.id更新入Site中
                    site.setAboutUsCid(sc.getId());
                    sqlDAO.save(site);

                    if (site.getClient().equals(Site.CLIENT_PC)) {
                        /****        PC start        ***/
                        //生成PC首页
                        GenerateHTML gh = new GenerateHTML(site);
                        Template temp = new Template(site);
                        String index = gh.pcIndex();        //获取到首页的模版，替换掉common的

                        //生成新闻栏目
                        SiteColumn newsSiteColumn = new SiteColumn();
                        newsSiteColumn.setIcon(AttachmentUtil.netUrl() + "image/default_newsColumn.png");
                        newsSiteColumn.setName("新闻咨询");
                        newsSiteColumn.setParentid(0);
                        newsSiteColumn.setRank(2);
                        newsSiteColumn.setSiteid(site.getId());
                        newsSiteColumn.setType(SiteColumn.TYPE_NEWS);
                        newsSiteColumn.setUrl("");
                        newsSiteColumn.setUsed(SiteColumn.USED_ENABLE);
                        newsSiteColumn.setUserid(site.getUserid());
                        sqlDAO.save(newsSiteColumn);
                        //给新闻栏目默认添加一条欢庆信息
                        News xwNews = new News();
                        xwNews.setAddtime(DateUtil.timeForUnix10());
                        xwNews.setCid(newsSiteColumn.getId());
                        xwNews.setSiteid(site.getId());
                        xwNews.setStatus(News.STATUS_NORMAL);
                        xwNews.setTitle("热烈庆祝" + site.getName() + "网站成立");
                        xwNews.setIntro(xwNews.getTitle() + ",各族人民前来欢庆。");
                        xwNews.setTitlepic(AttachmentUtil.netUrl() + "image/default_news_titlepic.png");
                        xwNews.setType(News.TYPE_NEWS);
                        xwNews.setUserid(siteUserId);
                        xwNews.setLegitimate(News.LEGITIMATE_OK);
                        sqlDAO.save(xwNews);
                        NewsData xwNewsData = new NewsData(xwNews.getId(), xwNews.getIntro());
                        sqlDAO.save(xwNewsData);

                        //加入 Site.columnId
                        site.setColumnId(getSiteColumnId(newsSiteColumn, site));

                        //生成产品展示栏目
                        SiteColumn imageSiteColumn = new SiteColumn();
                        imageSiteColumn.setIcon(AttachmentUtil.netUrl() + "image/default_project_column.png");
                        imageSiteColumn.setName("产品展示");
                        imageSiteColumn.setParentid(0);
                        imageSiteColumn.setRank(3);
                        imageSiteColumn.setSiteid(site.getId());
                        imageSiteColumn.setType(SiteColumn.TYPE_IMAGENEWS);
                        imageSiteColumn.setUrl("");
                        imageSiteColumn.setUsed(SiteColumn.USED_ENABLE);
                        imageSiteColumn.setUserid(site.getUserid());
                        sqlDAO.save(imageSiteColumn);
                        //给产品展示栏目默认添加一条欢庆信息
                        News productNews = new News();
                        productNews.setAddtime(DateUtil.timeForUnix10());
                        productNews.setCid(imageSiteColumn.getId());
                        productNews.setSiteid(site.getId());
                        productNews.setStatus(News.STATUS_NORMAL);
                        productNews.setTitle("热烈庆祝" + site.getName() + "网站成立");
                        productNews.setIntro(xwNews.getTitle() + ",各族人民前来欢庆。");
                        productNews.setTitlepic(AttachmentUtil.netUrl() + "image/default_news_titlepic.png");
                        productNews.setType(News.TYPE_IMAGENEWS);
                        productNews.setUserid(siteUserId);
                        productNews.setLegitimate(News.LEGITIMATE_OK);
                        sqlDAO.save(productNews);
                        NewsData productNewsData = new NewsData(productNews.getId(), productNews.getIntro());
                        sqlDAO.save(productNewsData);

                        //加入 Site.columnId
                        site.setColumnId(getSiteColumnId(imageSiteColumn, site));
                        site.setDomain("s" + site.getId());
                        sqlDAO.save(site);

                        //生成联系我们
                        SiteColumn lxSC = new SiteColumn();
                        lxSC.setIcon(AttachmentUtil.netUrl() + "image/lianxi.jpeg");
                        lxSC.setName("联系我们");
                        lxSC.setParentid(0);
                        lxSC.setRank(4);
                        lxSC.setSiteid(site.getId());
                        lxSC.setType(SiteColumn.TYPE_PAGE);
                        lxSC.setUsed(SiteColumn.USED_ENABLE);
                        lxSC.setUserid(siteUserId);
//						lxSC.setClient(SiteColumn.CLIENT_PC);
                        sqlDAO.save(lxSC);
                        //默认给导航栏目：联系我们，创建一个独立页面
                        News lxNews = new News();
                        lxNews.setAddtime(DateUtil.timeForUnix10());
                        lxNews.setCid(lxSC.getId());
                        lxNews.setIntro(lxSC.getName());
                        lxNews.setSiteid(site.getId());
                        lxNews.setStatus(News.STATUS_NORMAL);
                        lxNews.setTitle(lxSC.getName());
                        lxNews.setTitlepic(lxSC.getIcon());
                        lxNews.setType(News.TYPE_PAGE);
                        lxNews.setUserid(siteUserId);
                        lxNews.setLegitimate(News.LEGITIMATE_OK);
                        sqlDAO.save(lxNews);
                        NewsData lxNewsData = new NewsData(lxNews.getId(), lxNews.getIntro());
                        sqlDAO.save(lxNewsData);

                        //刷新栏目js数据以及排序
                        siteColumnService.resetColumnRankAndJs(site);

                        /***        PC end        ****/
                    } else {
                        //WAP
                        columnList.add(sc);
                        new SiteCache().siteColumn(columnList, site); //siteColumn.js
                        new SiteCache().siteColumnRank(site, sc.getId() + "");    //siteColumnRank.js
                    }

                    //更新当前Session的缓存Site的信息
                    SessionUtil.setSite(site);

                    //新增需要刷新全站，生成所有页面
                    refreshSiteGenerateHtml(request);
                }

            }

            //更新当前Session缓存。如果是api接口开通网站，session是空的。所以要加null判断
            SessionUtil.setSite(site);

            //创建数据js缓存 ， pc、wap模式已废弃这种模式
//			new cn.edu.sjtu.gateway.manager.cache.Site().site(site, imService.getImByCache());				//site.js

            baseVO.setBaseVO(BaseVO.SUCCESS, s.getId() > 0 ? "保存网站成功！" : "创建网站成功！");
            baseVO.setSite(site);
            return baseVO;
        } else {
            baseVO.setBaseVO(BaseVO.FAILURE, "保存失败！");
            return baseVO;
        }
    }

    public String getSiteColumnId(SiteColumn sc, Site site) {
        //修改Site.column_id
        //若栏目为新闻或者图文列表，且Site表里没有记录该栏目，则将该栏目的id加入Site表的栏目id中
        if (sc.getType() - SiteColumn.TYPE_IMAGENEWS == 0 || sc.getType() - SiteColumn.TYPE_NEWS == 0) {
            //判断该栏目的状态是否是启用状态，若是启用状态，需要将该栏目的id加入Site表
            if (sc.getUsed() - SiteColumn.USED_ENABLE == 0) {
                if (site.getColumnId() == null || site.getColumnId().indexOf("," + sc.getId() + ",") == -1) {
                    if (site.getColumnId() == null) {
                        site.setColumnId("," + sc.getId() + ",");
                    } else {
                        site.setColumnId(site.getColumnId() + sc.getId() + ",");
                    }
                    if (site.getColumnId().length() < 80) {
                        return site.getColumnId();
                    }
                }
            } else {
                //若是状态不启用，需要检查Site表中是否有此栏目的存储，若有，需要将其去掉
                if (site.getColumnId() != null && site.getColumnId().indexOf("," + sc.getId() + ",") > -1) {
                    site.setColumnId(site.getColumnId().replaceAll("," + sc.getId() + ",", ","));
                    return site.getColumnId();
                }
            }
        }
        return null;
    }

//	public SiteVO updateWapTemplateRefreshHtml(Site site, HttpServletRequest request) {
//		SiteVO siteVO = saveSite(site,site.getUserid(), request);
//		if(siteVO.SUCCESS == siteVO.getResult()){
//			//因为以前注册的站点模版没变过来，暂时是刷新全站
//			refreshSiteGenerateHtml(request, site);
//		}
//		return siteVO;
//	}
    @Override
    public void generateSitePcIndex(Site site, SiteColumn aboutUsColumn, SiteColumn newsColumn, SiteColumn imagesColumn, SiteData siteData) {
        GenerateHTML gh = new GenerateHTML(site);

        String html = gh.pcIndex();
        //关于我们
        html = html.replaceAll("<!--Index_Content_AboutUs_Start--><!--id=0-->", "<!--Index_Content_AboutUs_Start--><!--id=" + (aboutUsColumn == null ? "0" : "" + aboutUsColumn.getId()) + "-->");
        //新闻
        html = html.replaceAll("<!--Index_Content_NewsList_Start--><!--id=0-->", "<!--Index_Content_NewsList_Start--><!--id=" + (newsColumn == null ? "0" : "" + newsColumn.getId()) + "-->");
        //图片
        html = html.replaceAll("<!--Index_Content_NewsImageList_Start--><!--id=0-->", "<!--Index_Content_NewsImageList_Start--><!--id=" + (imagesColumn == null ? "0" : "" + imagesColumn.getId()) + "-->");

        gh.generateIndexHtml(html, siteData);
    }
    @Override
    public boolean isPcClient(Site site) {
        if (site.getClient() == null) {
            return false;
        } else {
            if (site.getClient() == Site.CLIENT_PC) {
                return true;
            } else {
                return false;
            }
        }
    }

    @Override
    public void refreshSiteMap(Site site) {
        StringBuilder xml = new StringBuilder("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"
                + "<urlset\n"
                + "\txmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\"\n"
                + "\txmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\n"
                + "\txsi:schemaLocation=\"http://www.sitemaps.org/schemas/sitemap/0.9\n"
                + "\t\thttp://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd\">\n");

        //加入首页
        String indexUrl = "";
        if (site.getBindDomain() != null && site.getBindDomain().length() > 3) {
            indexUrl = "http://" + site.getBindDomain();
        } else {
            indexUrl = "http://" + site.getDomain() + ".wang.market";
        }
        xml.append(getSitemapUrl(indexUrl, "1.00"));


        //新闻/图文栏目列表
        List<SiteColumn> siteColumnList = sqlDAO.findBySqlQuery("SELECT * FROM site_column WHERE siteid = " + site.getId() + " AND used = " + SiteColumn.USED_ENABLE, SiteColumn.class);
//		Map<Integer, SiteColumn> siteColumnMap = new HashMap<Integer, SiteColumn>();	//可以根据siteColumn.id取出siteColumn数据对象
        for (SiteColumn siteColumn : siteColumnList) {
            //XML加入栏目页面
            xml.append(getSitemapUrl(indexUrl + "/lc" + siteColumn.getId() + "_1.html", "0.4"));
        }

        //生成内容页面
        List<News> listNews = sqlDAO.findBySqlQuery("SELECT news.* FROM news WHERE news.siteid = " + site.getId() + " AND news.status = " + News.STATUS_NORMAL + " ORDER BY news.id DESC", News.class);
        for (News news : listNews) {
            //XML加入内容页面
            if (news.getType() - News.TYPE_PAGE == 0) {
                //独立页面享有更大的权重，赋予其 0.8
                xml.append(getSitemapUrl(indexUrl + "/c" + news.getCid() + ".html", "0.8"));
            } else {
                //普通列表的内容页面，0.5
                xml.append(getSitemapUrl(indexUrl + "/" + news.getId() + ".html", "0.5"));
            }
        }

        xml.append("</urlset>");

        //生成 sitemap.xml
        AttachmentUtil.uploadStringFile("site/" + site.getId() + "/sitemap.xml", xml.toString());
    }

    /**
     * SiteMap生成的url项
     *
     * @param loc
     * @param priority 如 1.00 、 0.5
     * @return
     */
    private String getSitemapUrl(String loc, String priority) {
        if (!loc.contains("http:")) {
            loc = "http://" + loc;
        }
        return "<url>\n"
                + "\t<loc>" + loc + "</loc>\n"
                + "\t<priority>" + priority + "</priority>\n"
                + "</url>\n";
    }

//	public SiteVO findByIdForCurrentUser(int id) {
//		SiteVO vo = new SiteVO();
//		
//		if(id == 0){
//			vo.setBaseVO(SiteVO.FAILURE, "请传入要操作的站点编号");
//			return vo;
//		}
//		Site site = sqlDAO.findById(Site.class, id);
//		if(site == null){
//			vo.setBaseVO(SiteVO.FAILURE, "站点不存在");
//			return vo;
//		}
//		if(site.getUserid() - ShiroFunc.getUser().getId() != 0){
//			vo.setBaseVO(SiteVO.FAILURE, "站点不属于您，您无法操作");
//			return vo;
//		}
//		
//		vo.setSite(site);
//		return vo;
//	}
@Override
    public SiteRemainHintVO getSiteRemainHint(Site site, Agency agency) {
        SiteRemainHintVO vo = new SiteRemainHintVO();

        //v2.25 有时候会出现agency为null的情况,如果这样，默认讲result 设为正常模式未到期，不会出现什么提示
        if (agency == null) {
            vo.setResult(SiteRemainHintVO.SUCCESS);
            return vo;
        }

        vo.setCompanyName(agency.getName());
        vo.setPhone(agency.getPhone());
        vo.setQq(agency.getQq());

        //计算当前网站的过期时间，当网站过期时间低于两个月时，会出现即将到期的提示
        if (site.getExpiretime() != null && site.getExpiretime() > 0) {
            //获取当前网站还有多久过期，获取剩余时间的秒数
            int remain = site.getExpiretime() - DateUtil.timeForUnix10();

            //当前时间 + 两个月以后的时间，于过期时间比较	//1 * 60 * 60 * 24 * 30 * 2 两个月
            if (remain < 5184000) {
                //如果剩余时间不到连个月了，那么会有提示
                //计算还剩多久了
                if (remain < 1) {
                    vo.setResult(3);
                    vo.setRemainTimeString("已到期");
                } else {
                    vo.setResult(SiteRemainHintVO.FAILURE);
                    vo.setRemainTimeString("还剩<span style=\"font-size: 38px; font-style: oblique; padding: 5px; color:red; padding-right: 12px;\">" + (int) Math.ceil(remain / 86400) + "</span>天到期");
                }
            }
        }

        return vo;
    }
    @Override
    public String getSiteRemainHintForJavaScript(Site site, Agency agency) {
        /* SiteRemainHintVO vo = getSiteRemainHint(site, agency);
        if (vo.getResult() - SiteRemainHintVO.FAILURE == 0) {
            return "<script type=\"text/javascript\">"
                    + "layer.open({ type: 1 , title: '网站即将到期，续费提示', offset: 'rb' ,content: '<div style=\"padding: 10px 30px; padding-bottom: 60px;\">网站" + vo.getRemainTimeString() + "！"
                    + "<br/>请联系" + agency.getName() + "续费"
                    + "<br/>联系电话：" + agency.getPhone()
                    + "<br/>联系QQ：" + agency.getQq() + ""
                    + "<br/>所在地址：" + agency.getAddress() + "</div>' ,shade: 0});"
                    + "</script>";
        } */
        
        // 根据用户要求，禁用网站到期提醒功能
        return "";
    }

}
