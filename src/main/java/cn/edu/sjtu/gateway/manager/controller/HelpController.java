package cn.edu.sjtu.gateway.manager.controller;


import cn.edu.sjtu.gateway.vm.util.SystemUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;

/**
 * 帮助说明
 * <AUTHOR>
 */
@Controller
@RequestMapping("/help/")
@Slf4j
public class HelpController extends cn.edu.sjtu.gateway.manager.controller.BaseController {
	
	/**
	 * 模版开发
	 */
	@RequestMapping("/mobankaifa${url.suffix}")
	public String mobankaifa(HttpServletRequest request ,Model model){
		log.info("网站帮助说明-模版开发");
		return redirect(SystemUtil.get("SITE_TEMPLATE_DEVELOP_URL"));
	}
	
	
}
