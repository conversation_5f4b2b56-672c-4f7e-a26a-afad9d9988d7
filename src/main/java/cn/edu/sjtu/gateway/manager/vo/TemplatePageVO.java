package cn.edu.sjtu.gateway.manager.vo;

import java.io.Serializable;

import cn.edu.sjtu.gateway.vm.vo.BaseVO;
import cn.edu.sjtu.gateway.manager.entity.TemplatePage;
import cn.edu.sjtu.gateway.manager.entity.TemplatePageData;

/**
 * 模版页
 * <AUTHOR>
 */
public class TemplatePageVO extends BaseVO implements Serializable{
	private TemplatePage templatePage;
	private TemplatePageData templatePageData;
	
	public TemplatePage getTemplatePage() {
		return templatePage;
	}
	public void setTemplatePage(TemplatePage templatePage) {
		this.templatePage = templatePage;
	}
	public TemplatePageData getTemplatePageData() {
		return templatePageData;
	}
	public void setTemplatePageData(TemplatePageData templatePageData) {
		this.templatePageData = templatePageData;
	}
	
}
