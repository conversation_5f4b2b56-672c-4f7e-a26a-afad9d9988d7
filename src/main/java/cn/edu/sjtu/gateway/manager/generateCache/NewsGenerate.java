package cn.edu.sjtu.gateway.manager.generateCache;

import cn.edu.sjtu.gateway.manager.entity.News;
import cn.edu.sjtu.gateway.manager.entity.SiteColumn;
import org.springframework.stereotype.Component;
import cn.edu.sjtu.gateway.vm.generateCache.BaseGenerate;

/**
 * 站点栏目导航
 * <AUTHOR>
 */
@Component
public class NewsGenerate extends BaseGenerate {
	public NewsGenerate() {
		status();
		type();
		legitimate();
	}
	
	public void status(){
		createCacheObject("status");
		cacheAdd(News.STATUS_NORMAL, "显示");
		cacheAdd(News.STATUS_HIDDEN, "隐藏");
		generateCacheFile();
	}
	
	public void type(){
		createCacheObject("type");
		cacheAdd(SiteColumn.TYPE_NEWS, "新闻信息");
		cacheAdd(SiteColumn.TYPE_IMAGENEWS, "图文信息");
		cacheAdd(SiteColumn.TYPE_PAGE, "独立页面");
		cacheAdd(SiteColumn.TYPE_HREF, "超链接");
		cacheAdd(SiteColumn.TYPE_LIST, "信息列表");
		cacheAdd(SiteColumn.TYPE_ALONEPAGE, "独立页面");
		generateCacheFile();
	}
	
	/**
	 * 是否涉嫌内容违规
	 */
	public void legitimate(){
		createCacheObject("legitimate");
		cacheAdd(News.LEGITIMATE_OK, "合法");
		cacheAdd(News.LEGITIMATE_NO, "涉嫌");
		generateCacheFile();
	}
}
