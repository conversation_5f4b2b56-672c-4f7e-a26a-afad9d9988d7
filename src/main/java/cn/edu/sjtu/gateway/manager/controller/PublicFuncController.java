package cn.edu.sjtu.gateway.manager.controller;

import javax.servlet.http.HttpServletRequest;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import cn.edu.sjtu.gateway.vm.util.AttachmentUtil;


/**
 * 公共的一些
 * <AUTHOR>
 */
@Controller
@RequestMapping("/")
@Slf4j
public class PublicFuncController extends BaseController {
	
	/**
	 * 模版列表
	 */
	@RequestMapping("template${url.suffix}")
	public String templat(HttpServletRequest request,Model model){
		log.info("模版列表");
		model.addAttribute("AttachmentFileUrl", AttachmentUtil.netUrl());
		return "template";
	}
	
}
