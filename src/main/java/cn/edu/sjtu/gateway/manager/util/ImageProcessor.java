package cn.edu.sjtu.gateway.manager.util;

import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.FileImageOutputStream;
import javax.imageio.stream.ImageOutputStream;
import javax.imageio.stream.MemoryCacheImageOutputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Iterator;

/**
 * 图片处理工具
 * 只提供MultipartFile处理方法
 */
public class ImageProcessor {
    private static final Logger log = LoggerFactory.getLogger(ImageProcessor.class);
    private static final int DEFAULT_MAX_SIZE = 1080;
    private static final float DEFAULT_QUALITY = 0.9f;
    private static final int CHUNK_SIZE = 4 * 1024 * 1024; // 4MB 分块大小

    /**
     * 处理图片，使用默认参数
     *
     * @param file 输入的MultipartFile图片
     * @return 处理后的MultipartFile图片
     * @throws java.io.IOException 如果处理过程中出错
     */
    public static MultipartFile processImage(MultipartFile file) throws IOException {
        return process(file, DEFAULT_MAX_SIZE, DEFAULT_QUALITY);
    }

    /**
     * 处理图片，使用自定义参数
     *
     * @param file    输入的MultipartFile图片
     * @param maxSize 最大尺寸（像素）
     * @param quality 压缩质量 (0.0-1.0)
     * @return 处理后的MultipartFile图片
     * @throws java.io.IOException 如果处理过程中出错
     */
    public static MultipartFile process(MultipartFile file, int maxSize, float quality) throws IOException {
        // 参数验证
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("输入文件不能为空");
        }

        // 获取文件信息
        String filename = file.getOriginalFilename();
        String contentType = file.getContentType();
        log.info("处理图片: {}, 类型: {}, 大小: {} 字节", filename, contentType, file.getSize());

        // 获取文件格式
        String format = getFormat(filename, contentType);

        // 对于大文件，使用临时文件处理
        if (file.getSize() > CHUNK_SIZE) {
            return processLargeImage(file, maxSize, quality, format);
        } else {
            return processSmallImage(file, maxSize, quality, format);
        }
    }

    /**
     * 处理小图片（内存中处理）
     */
    private static MultipartFile processSmallImage(MultipartFile file, int maxSize, float quality, String format) throws IOException {
        // 读取图片
        BufferedImage src = null;
        try {
            src = ImageIO.read(file.getInputStream());
            if (src == null) {
                log.warn("无法读取图片: {}", file.getOriginalFilename());
                return file;
            }

            int width = src.getWidth();
            int height = src.getHeight();
            log.info("原始图片尺寸: {}x{}", width, height);

            // 检查是否需要调整大小
            if (width <= maxSize && height <= maxSize) {
                log.info("图片尺寸已经在限制范围内，无需处理");
                return file; // 不需要处理，直接返回原图
            }

            // 计算新尺寸
            int newWidth, newHeight;
            if (width > height) {
                newWidth = maxSize;
                newHeight = (int) (height * ((double) maxSize / width));
            } else {
                newHeight = maxSize;
                newWidth = (int) (width * ((double) maxSize / height));
            }
            log.info("调整图片尺寸为: {}x{}", newWidth, newHeight);

            // 调整大小
            BufferedImage dst = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
            Graphics2D g = dst.createGraphics();
            try {
                g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
                g.drawImage(src, 0, 0, newWidth, newHeight, null);
            } finally {
                g.dispose();
            }

            // 转换为字节数组
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            try {
                // 对JPEG使用压缩
                if ("jpg".equals(format) || "jpeg".equals(format)) {
                    Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName(format);
                    if (writers.hasNext()) {
                        ImageWriter writer = writers.next();
                        try {
                            ImageWriteParam param = writer.getDefaultWriteParam();
                            param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                            param.setCompressionQuality(quality);

                            MemoryCacheImageOutputStream imgOut = new MemoryCacheImageOutputStream(out);
                            try {
                                writer.setOutput(imgOut);
                                writer.write(null, new IIOImage(dst, null, null), param);
                            } finally {
                                imgOut.close();
                                writer.dispose();
                            }
                        } catch (Exception e) {
                            log.warn("JPEG压缩失败，使用默认方式: {}", e.getMessage());
                            ImageIO.write(dst, format, out);
                        }
                    } else {
                        ImageIO.write(dst, format, out);
                    }
                } else {
                    // 其他格式直接写入
                    ImageIO.write(dst, format, out);
                }

                // 创建新的MultipartFile
                byte[] bytes = out.toByteArray();
                log.info("处理后图片大小: {} 字节", bytes.length);
                return createMultipartFile(file.getName(), file.getOriginalFilename(), file.getContentType(), bytes);
            } finally {
                try {
                    out.close();
                } catch (IOException e) {
                    log.warn("关闭输出流失败: {}", e.getMessage());
                }
            }
        } catch (OutOfMemoryError e) {
            log.error("处理小图片时内存不足: {}", e.getMessage(), e);
            System.gc(); // 请求垃圾回收
            return file; // 返回原始文件
        } finally {
            if (src != null) {
                src.flush();
            }
        }
    }

    /**
     * 处理大图片（使用临时文件）
     */
    private static MultipartFile processLargeImage(MultipartFile file, int maxSize, float quality, String format) throws IOException {
        log.info("使用临时文件处理大图片: {}, 大小: {} 字节", file.getOriginalFilename(), file.getSize());

        // 创建临时文件
        Path tempInputFile = Files.createTempFile("input_", "." + format);
        Path tempOutputFile = Files.createTempFile("output_", "." + format);

        try {
            // 将上传的文件保存到临时文件
            try (InputStream in = file.getInputStream();
                 OutputStream out = Files.newOutputStream(tempInputFile)) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
            }

            // 读取图片信息（只读取元数据，不加载整个图片到内存）
            BufferedImage sourceImage = ImageIO.read(tempInputFile.toFile());
            if (sourceImage == null) {
                log.warn("无法读取大图片: {}", file.getOriginalFilename());
                return file;
            }

            int width = sourceImage.getWidth();
            int height = sourceImage.getHeight();
            log.info("大图片原始尺寸: {}x{}", width, height);

            // 检查是否需要调整大小
            if (width <= maxSize && height <= maxSize) {
                log.info("大图片尺寸已经在限制范围内，无需处理");
                return file;
            }

            // 计算新尺寸
            int newWidth, newHeight;
            if (width > height) {
                newWidth = maxSize;
                newHeight = (int) (height * ((double) maxSize / width));
            } else {
                newHeight = maxSize;
                newWidth = (int) (width * ((double) maxSize / height));
            }
            log.info("调整大图片尺寸为: {}x{}", newWidth, newHeight);

            // 使用Java2D API调整大小并写入临时输出文件
            try (ImageOutputStream output = new FileImageOutputStream(tempOutputFile.toFile())) {
                // 对JPEG使用压缩
                if ("jpg".equals(format) || "jpeg".equals(format)) {
                    resizeJpegWithCompression(tempInputFile.toFile(), output, newWidth, newHeight, quality);
                } else {
                    // 其他格式
                    resizeImage(tempInputFile.toFile(), tempOutputFile.toFile(), newWidth, newHeight, format);
                }
            }

            // 将处理后的图片转换为MultipartFile
            byte[] processedImageData = Files.readAllBytes(tempOutputFile);
            log.info("处理后大图片大小: {} 字节", processedImageData.length);

            return createMultipartFile(file.getName(), file.getOriginalFilename(), file.getContentType(), processedImageData);

        } catch (OutOfMemoryError e) {
            log.error("处理大图片时内存不足: {}", e.getMessage(), e);
            System.gc(); // 请求垃圾回收
            return file; // 返回原始文件
        } finally {
            // 清理临时文件
            try {
                Files.deleteIfExists(tempInputFile);
                Files.deleteIfExists(tempOutputFile);
            } catch (IOException e) {
                log.warn("清理临时文件失败: {}", e.getMessage());
            }
        }
    }

    /**
     * 调整JPEG图片大小并应用压缩
     */
    private static void resizeJpegWithCompression(File inputFile, ImageOutputStream output,
                                                 int targetWidth, int targetHeight, float quality) throws IOException {
        // 读取源图片
        BufferedImage sourceImage = ImageIO.read(inputFile);

        // 创建目标图片
        BufferedImage targetImage = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_RGB);

        // 绘制调整大小后的图片
        Graphics2D g = targetImage.createGraphics();
        try {
            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g.drawImage(sourceImage, 0, 0, targetWidth, targetHeight, null);
        } finally {
            g.dispose();
        }

        // 使用JPEG压缩写入
        Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName("jpeg");
        if (writers.hasNext()) {
            ImageWriter writer = writers.next();
            try {
                ImageWriteParam param = writer.getDefaultWriteParam();
                param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                param.setCompressionQuality(quality);

                writer.setOutput(output);
                writer.write(null, new IIOImage(targetImage, null, null), param);
            } finally {
                writer.dispose();
            }
        } else {
            // 回退到默认方法
            ImageIO.write(targetImage, "jpeg", output);
        }
    }

    /**
     * 调整图片大小（适用于非JPEG格式）
     */
    private static void resizeImage(File inputFile, File outputFile, int targetWidth, int targetHeight, String format) throws IOException {
        // 读取源图片
        BufferedImage sourceImage = ImageIO.read(inputFile);

        // 创建目标图片
        BufferedImage targetImage = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_RGB);

        // 绘制调整大小后的图片
        Graphics2D g = targetImage.createGraphics();
        try {
            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g.drawImage(sourceImage, 0, 0, targetWidth, targetHeight, null);
        } finally {
            g.dispose();
        }

        // 写入输出文件
        ImageIO.write(targetImage, format, outputFile);
    }

    // 使用Apache Commons FileUpload创建MultipartFile
    private static MultipartFile createMultipartFile(String name, String originalFilename, String contentType, byte[] content) {
        FileItem fileItem = new DiskFileItemFactory().createItem(
                name,
                contentType,
                false,
                originalFilename
        );

        try (InputStream inputStream = new ByteArrayInputStream(content)) {
            fileItem.getOutputStream().write(content);
            return new CommonsMultipartFile(fileItem);
        } catch (IOException e) {
            // 包装为运行时异常
            log.error("创建MultipartFile失败: {}", e.getMessage(), e);
            throw new IllegalStateException("创建MultipartFile失败", e);
        }
    }

    // 获取文件格式的辅助方法
    private static String getFormat(String filename, String contentType) {
        if (filename != null) {
            int dotIndex = filename.lastIndexOf('.');
            if (dotIndex > 0) {
                return filename.substring(dotIndex + 1).toLowerCase();
            }
        }

        if (contentType != null) {
            if (contentType.startsWith("image/")) {
                return contentType.substring("image/".length()).toLowerCase();
            }
        }

        return "jpg"; // 默认使用JPG格式
    }
}
