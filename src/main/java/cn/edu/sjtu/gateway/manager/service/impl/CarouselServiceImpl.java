package cn.edu.sjtu.gateway.manager.service.impl;

import cn.edu.sjtu.gateway.manager.entity.Carousel;
import cn.edu.sjtu.gateway.manager.service.CarouselService;
import cn.edu.sjtu.gateway.vm.dao.SqlDAO;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("carouselService")
public class CarouselServiceImpl implements CarouselService {

    private final SqlDAO sqlDAO;

    public CarouselServiceImpl(SqlDAO sqlDAO) {
        this.sqlDAO = sqlDAO;
    }

    @Override
    public List<Carousel> findBySiteid(int siteid) {
        // TODO Auto-generated method stub
        return sqlDAO.findBySqlQuery("SELECT * FROM carousel WHERE siteid = " + siteid + " ORDER BY `rank` ASC", Carousel.class);
    }

    @Override
    public Carousel findAloneBySiteid(int siteid, short type) {
        return sqlDAO.findAloneBySqlQuery("SELECT * FROM carousel WHERE siteid = " + siteid + " AND type = " + type + " ORDER BY `rank` ASC", Carousel.class);
    }


}
