package cn.edu.sjtu.gateway.manager.generateCache;

import cn.edu.sjtu.gateway.manager.entity.SiteColumn;
import org.springframework.stereotype.Component;
import cn.edu.sjtu.gateway.vm.generateCache.BaseGenerate;

/**
 * 站点栏目导航
 * <AUTHOR>
 */
@Component
public class SiteColumnGenerate extends BaseGenerate {
	public SiteColumnGenerate() {
		used();
		type();
		editMode();
		listRank();
		useGenerateView();
		
		//v4.10
		templateCodeColumnUsed();
		managerNewsUsed();
	}
	
	public void used(){
		createCacheObject("used");
		cacheAdd(SiteColumn.USED_ENABLE, "显示");
		cacheAdd(SiteColumn.USED_UNABLE, "隐藏");
		generateCacheFile();
	}
	
	public void type(){
		createCacheObject("type");
		cacheAdd(SiteColumn.TYPE_NEWS, "新闻信息");
		cacheAdd(SiteColumn.TYPE_IMAGENEWS, "图文信息");
		cacheAdd(SiteColumn.TYPE_PAGE, "独立页面");
		cacheAdd(SiteColumn.TYPE_HREF, "超链接");
		//v4.6，为CMS模式增加
		cacheAdd(SiteColumn.TYPE_LIST, "信息列表");
		cacheAdd(SiteColumn.TYPE_ALONEPAGE, "独立页面");
		
		generateCacheFile();
	}
	
	public void editMode(){
		createCacheObject("editMode");
		cacheAdd(SiteColumn.EDIT_MODE_INPUT_MODEL, "在内容管理中编辑");
		cacheAdd(SiteColumn.EDIT_MODE_TEMPLATE, "在模版页面中编辑");
		generateCacheFile();
	}
	
	/**
	 * 栏目内信息排序方式
	 */
	public void listRank(){
		createCacheObject("listRank");
		cacheAdd(SiteColumn.LIST_RANK_ADDTIME_ASC, "发布时间正序");
		cacheAdd(SiteColumn.LIST_RANK_ADDTIME_DESC, "发布时间倒序");
		generateCacheFile();
	}
	
	/**
	 * 是否生成内容详情的页面
	 */
	public void useGenerateView(){
		createCacheObject("useGenerateView");
		cacheAdd(SiteColumn.USED_ENABLE, "生成");
		cacheAdd(SiteColumn.USED_UNABLE, "不生成");
		generateCacheFile();
	}
	
	/**
	 * 是否在模版调用中显示（调取子栏目列表） v4.10 增加
	 */
	public void templateCodeColumnUsed(){
		createCacheObject("templateCodeColumnUsed");
		cacheAdd(SiteColumn.USED_ENABLE, "显示");
		cacheAdd(SiteColumn.USED_UNABLE, "隐藏");
		generateCacheFile();
	}
	

	/**
	 * 是否在内容管理中显示这个栏目 v4.10 增加
	 */
	public void managerNewsUsed(){
		createCacheObject("managerNewsUsed");
		cacheAdd(SiteColumn.USED_ENABLE, "显示");
		cacheAdd(SiteColumn.USED_UNABLE, "隐藏");
		generateCacheFile();
	}
	
	
}
