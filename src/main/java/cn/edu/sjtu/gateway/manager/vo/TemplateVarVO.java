package cn.edu.sjtu.gateway.manager.vo;

import java.io.Serializable;

import cn.edu.sjtu.gateway.vm.vo.BaseVO;
import cn.edu.sjtu.gateway.manager.entity.TemplateVar;
import cn.edu.sjtu.gateway.manager.entity.TemplateVarData;

/**
 * 模版变量
 * <AUTHOR>
 */
public class TemplateVarVO extends BaseVO implements Serializable{
	private TemplateVar templateVar;
	private TemplateVarData templateVarData;

	public TemplateVar getTemplateVar() {
		return templateVar;
	}

	public void setTemplateVar(TemplateVar templateVar) {
		this.templateVar = templateVar;
	}

	public TemplateVarData getTemplateVarData() {
		return templateVarData;
	}

	public void setTemplateVarData(TemplateVarData templateVarData) {
		this.templateVarData = templateVarData;
	}
	
}
