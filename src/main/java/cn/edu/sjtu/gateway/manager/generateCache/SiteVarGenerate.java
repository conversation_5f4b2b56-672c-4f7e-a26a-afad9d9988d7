package cn.edu.sjtu.gateway.manager.generateCache;

import cn.edu.sjtu.gateway.manager.entity.SiteVar;
import org.springframework.stereotype.Component;
import cn.edu.sjtu.gateway.vm.generateCache.BaseGenerate;

/**
 * 模板变量
 * <AUTHOR>
 */
@Component
public class SiteVarGenerate extends BaseGenerate {
	public SiteVarGenerate() {
		type();
	}
	
	public void type(){
		createCacheObject("type");
		cacheAdd(SiteVar.TYPE_TEXT, "文本输入");
		cacheAdd(SiteVar.TYPE_NUMBER, "整数输入");
		cacheAdd(SiteVar.TYPE_IMAGE, "单图片上传");
		cacheAdd(SiteVar.TYPE_IMAGE_GROUP, "多图片上传");
		cacheAdd(SiteVar.TYPE_SELECT, "下拉选择");
		generateCacheFile();
	}

}
