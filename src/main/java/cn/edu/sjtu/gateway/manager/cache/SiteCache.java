package cn.edu.sjtu.gateway.manager.cache;
import cn.edu.sjtu.gateway.manager.G;
import cn.edu.sjtu.gateway.manager.entity.Carousel;
import cn.edu.sjtu.gateway.manager.entity.Site;
import cn.edu.sjtu.gateway.manager.entity.SiteColumn;
import cn.edu.sjtu.gateway.http.Http;
import cn.edu.sjtu.gateway.http.Response;
import cn.edu.sjtu.gateway.tools.StringUtil;
import cn.edu.sjtu.gateway.vm.util.AttachmentUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Objects;

/**
 * 站点相关信息进行js缓存
 * <AUTHOR>
 */
@Slf4j
public class SiteCache extends BaseCache{
	
	/**
	 * 创建站点的栏目导航数据缓存
	 * @param siteColumnlist
	 */
	public void siteColumn(List<SiteColumn> siteColumnlist, Site site) {
		createCacheObject("siteColumn");
		int siteid = 0;
		StringBuilder content = new StringBuilder();
		for (int i = 0; i < siteColumnlist.size(); i++) {
			SiteColumn column = siteColumnlist.get(i);
			if(siteid == 0){
				siteid = column.getSiteid();
			}

			//根据type，来判断url的值
			if(Objects.equals(column.getType(), SiteColumn.TYPE_NEWS)){
				column.setUrl("/newsList.naii?cid="+column.getId());
			}else if (Objects.equals(column.getType(), SiteColumn.TYPE_IMAGENEWS)) {
				column.setUrl("/imageNewsList.naii?cid="+column.getId());
			}else if (Objects.equals(column.getType(), SiteColumn.TYPE_PAGE)) {
				column.setUrl(AttachmentUtil.netUrl()+"site/"+site.getId()+"/html/"+column.getId()+".html");
			}else if (Objects.equals(column.getType(), SiteColumn.TYPE_LEAVEWORD)) {
				column.setUrl("/leaveword.naii?siteid="+siteid);
			}else if (Objects.equals(column.getType(), SiteColumn.TYPE_HREF)) {
				//5是超链接，忽略过
			}else if (Objects.equals(column.getType(), SiteColumn.TYPE_HREF)) {
				column.setUrl("#");
			}
			
			if(column.getIcon() == null || column.getIcon().isEmpty()){
				column.setIcon(AttachmentUtil.netUrl()+G.DEFAULT_SITE_COLUMN_ICON_URL);
			}
			String icon = !column.getIcon().contains("://") ? AttachmentUtil.netUrl()+"site/"+site.getId()+"/column_icon/"+column.getIcon():column.getIcon();
			content.append(" siteColumn[").append(i).append("] = new Array();").append(" siteColumn[").append(i).append("]['id'] = '").append(column.getId()).append("'; ").append(" siteColumn[").append(i).append("]['name'] = '").append(StringUtil.StringToUtf8(column.getName())).append("'; ").append(" siteColumn[").append(i).append("]['url'] = '").append(column.getUrl()).append("'; ").append(" siteColumn[").append(i).append("]['type'] = '").append(column.getType()).append("'; ").append(" siteColumn[").append(i).append("]['icon'] = '").append(icon).append("'; ");
		}
		
		if(siteid > 0){
			appendContent(content.toString());
			generateCacheFile(site);
		}
	}
	
	/**
	 * 创建导航栏目的排序
	 * @param site 要排序的导航栏目所在的站点
	 * @param rank 排序，传入如：1,3,2,4,5  每个导航栏目id之间用,分割。这里在前面的数字排序越靠前
	 */
	public void siteColumnRank(cn.edu.sjtu.gateway.manager.entity.Site site, String rank){
		createCacheObject("siteColumnRank");
		if(rank != null && !rank.isEmpty()){
			String[] ranks = rank.split(",");
			StringBuilder content = new StringBuilder(" siteColumnRank = [");
			boolean haveData = false;
            for (String s : ranks) {
                if (s != null && !s.isEmpty()) {
                    if (haveData) {
                        content.append(",").append(s);
                    } else {
                        content.append(s);
                        haveData = true;
                    }
                }
            }
			content.append("]; ");
			appendContent(content.toString());
			generateCacheFile(site);
		}
	}
	
	/**
	 * 创建导航栏目的排序,新建一个SiteColumn后，忘排序规则里面追加
	 * @param site
	 * @param siteColumnId
	 * @throws java.io.IOException
	 */
	public void siteColumnRankAppend(Site site,int siteColumnId) throws IOException{
		String rankUrl = AttachmentUtil.netUrl()+"site/"+site.getId()+"/data/siteColumnRank.js";
		Response res = new Http().get(rankUrl);
		if(res.getCode() == 404){	//若没有，创建一个新的
			siteColumnRank(site, siteColumnId+"");
		}else{	//若有了，将新增加的放到最后一个
			String rankSource = new Http().get(rankUrl).getContent();
			rankSource = rankSource.replace("];", ","+siteColumnId+"];");
			try {
				AttachmentUtil.uploadFile("site/"+site.getId()+"/data/siteColumnRank.js", new ByteArrayInputStream(rankSource.getBytes("UTF-8")));
			} catch (UnsupportedEncodingException e) {
				log.error("错误信息：------>"+e);
			}
		}
		
	}
	
	/**
	 * banner轮播图缓存
	 * @param site
	 */
	public void carousel(List<Carousel> carouselList,cn.edu.sjtu.gateway.manager.entity.Site site){
		createCacheObject("carouselList");
		
		StringBuilder content = new StringBuilder();
		int siteid = 0;
		for (int i = 0; i < carouselList.size(); i++) {
			Carousel carousel = carouselList.get(i);
			if(siteid == 0){
				siteid = carousel.getSiteid();
			}
			if(carousel.getIsshow() == Carousel.ISSHOW_SHOW){
				String image = !carousel.getImage().contains("://") ? AttachmentUtil.netUrl()+"site/"+site.getId()+"/carousel/"+carousel.getImage():carousel.getImage();
				content.append(" carouselList[").append(i).append("] = new Array();").append(" carouselList[").append(i).append("]['id'] = '").append(carousel.getId()).append("'; ").append(" carouselList[").append(i).append("]['type'] = '").append(carousel.getType()).append("'; ").append(" carouselList[").append(i).append("]['url'] = '").append(carousel.getUrl()).append("'; ").append(" carouselList[").append(i).append("]['image'] = '").append(image).append("'; ");
			}
		}
		
		if(siteid > 0){
			appendContent(content.toString());
			generateCacheFile(site);
		}
	}
}
