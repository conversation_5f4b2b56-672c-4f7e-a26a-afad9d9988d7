package cn.edu.sjtu.gateway.manager.init;

import cn.edu.sjtu.gateway.manager.entity.Template;
import cn.edu.sjtu.gateway.manager.util.TemplateUtil;

import cn.edu.sjtu.gateway.vm.util.SpringUtil;
import cn.edu.sjtu.gateway.vm.util.SystemUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

import static java.lang.Thread.sleep;


/**
 * 从数据库中，加载本地模版库的模版，将之存入内存，以便随时调用。分布式部署时，本地私有模版库同步延迟：24小时。如A服务器增加了本地模版库，那么A服务器本地模版库会立马变成最新的，但是B服务器会有个延迟，最多会在24小时内同步为最新的
 * 系统启动时跟随启动
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class LoadTemplateByDataBase {

    public LoadTemplateByDataBase() {
        new Thread(() -> {
            log.info("start database template refresh thread.");
            while (true) {
                try {
                    //避免出问题中断
                    load();
                    //一天同步一次
                    sleep(1000 * 60 * 60 * 24);
                } catch (Exception e) {
                    log.error("错误信息：------>" + e);
                    //避免出现异常，走了cache，导致死循环，从而使  catalina.out 沾满整个磁盘
                    try {
                        sleep(1000 * 60);    //延迟1分钟
                    } catch (InterruptedException ignored) {
                        log.error("错误信息：------>" + ignored);
                    }
                }
            }
        }).start();
    }

    public void load() {
        //等待数据库加载完毕
        while (SystemUtil.get("ALLOW_USER_REG") == null) {
            try {
                //延迟1秒
                sleep(1000);
            } catch (InterruptedException e) {
                log.error("错误信息：------>" + e);
            }
        }

        //数据库基本配置参数加载完毕后，进行加载本地模版，将之从数据库加载入内存
        List<Template> list = SpringUtil.getSqlService().findByProperty(Template.class, "iscommon", Template.ISCOMMON_YES);
        if (list.isEmpty()) {
            //如果没有取出数据，那么就不用执行下面的了
            log.info("加载数据库模板完成，未找到本地模板");
            return;
        }

        //清理之前的数据库内存缓存
        TemplateUtil.databaseTemplateMapForName.clear();
        TemplateUtil.databaseTemplateMapForType.clear();
        //将之加入 内存缓存
        for (Template template : list) {
            TemplateUtil.databaseTemplateMapForName.put(template.getName(), template);

            TemplateUtil.databaseTemplateMapForType.computeIfAbsent(template.getType(), k -> new HashMap<String, Template>());
            log.info("加载本地模板: {}", template.getName());
            TemplateUtil.databaseTemplateMapForType.get(template.getType()).put(template.getName(), template);
        }
        log.info("--------------------------------------> 加载数据库模板完成！本地共有 " + list.size() + " 个模板 <--------------------------------------");

    }
}
