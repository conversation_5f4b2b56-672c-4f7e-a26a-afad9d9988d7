package cn.edu.sjtu.gateway.manager;

import cn.edu.sjtu.gateway.domain.bean.SimpleSite;
import cn.edu.sjtu.gateway.manager.entity.Site;
import cn.edu.sjtu.gateway.vm.util.SystemUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 全局
 *
 * <AUTHOR>
 */
public class G {
    //二级域名数组，中间存放着用于使用的二级域名的主域名。其中，第一个(twoDomainArray[0])会在程序中显示出来
    //CDN缓存的资源文件，包括框架的js、css文件、模版style.css文件等。
    /*
     * 使用系统赠送的二级域名访问，只要是建立过的网站，都会加入此,持久缓存
     * key: domain 二级域名的名字，不含 .wang.market
     */
    public static Map<String, SimpleSite> domainSiteMap = new HashMap<String, SimpleSite>();

    //使用绑定后的域名访问，这里只有绑定域名后才会加入此处,持久缓存
    public static Map<String, SimpleSite> bindDomainSiteMap = new HashMap<String, SimpleSite>();


    /**
     * 更新站点的二级域名缓存，二级域名是系统自己分配的
     *
     * @param domain 二级域名的名字，不含 .wang.market
     * @param ss     {@link SimpleSite}
     */
    public static void putDomain(String domain, SimpleSite ss) {
        domainSiteMap.put(domain, ss);
    }

    /**
     * 更新站点自己绑定的域名 缓存
     *
     * @param ss {@link SimpleSite}
     */
    public static void putBindDomain(String bindDomain, SimpleSite ss) {
        bindDomainSiteMap.put(bindDomain, ss);
    }

    /**
     * 通过站点自动分配的二级域名（不包含.wang.market ,仅仅只是二级域名的名字）获取站点信息
     *
     * @param domain 二级域名（不包含.wang.market ,仅仅只是二级域名的名字）
     * @return {@link SimpleSite}
     */
    public static SimpleSite getDomain(String domain) {
        return domainSiteMap.get(domain);
    }

    /**
     * 通过站点的绑定的域名获取站点信息
     *
     * @param bindDomain 绑定的域名
     * @return {@link SimpleSite}
     */
    public static SimpleSite getBindDomain(String bindDomain) {
        return bindDomainSiteMap.get(bindDomain);
    }

    /**
     * 获取自动分配的二级域名的个数
     *
     * @return
     */
    public static int getDomainSize() {
        return domainSiteMap.size();
    }

    /**
     * 获取绑定的域名的个数
     *
     * @return
     */
    public static int getBindDomainSize() {
        return bindDomainSiteMap.size();
    }

    /**
     * 获取泛解析的主域名列表，获取到的域名列表便是分配给用户的二级域名。
     * 注意的是，第一个域名会作为网站的官分配的二级域名，在程序里会体现第一个域名。其他的都是备用的，在程序中不会体现，只有用户使用二级域名访问时才会有效
     */
    public static String[] getAutoAssignDomain() {
        String d = SystemUtil.get("AUTO_ASSIGN_DOMAIN");
        return d.split(",");
    }

    /**
     * 当前的版本号。1.x为通用模版时代； 2.x为cms时代, 3.x 整体架构及云模块使用升级， 4.x整体架构升级，由深度依赖阿里云抽离， 5.x 插件时代，将更多的功能以插件的形式来做
     */
    public static String VERSION = "1.0.0";
    //云端域名。如，云端模版列表，则为 cloudDomain+"cloudTemplateList.naii"
    //代理开通下级代理，消耗20站币
    public static int agencyAddSubAgency_siteSize = 20;
    //站点信息缓存
    public static final String CACHE_FILE = "cache/data/";

    public static final String DEFAULT_SITE_COLUMN_ICON_URL = "res/glyph-icons/word.png";

    //PC端的默认模版编号是6
    public static final int TEMPLATE_PC_DEFAULT = 6;
    //手机端的默认模版编号是1
    public static final int TEMPLATE_WAP_DEFAULT = 1;
    //手机版本的新闻、图文列表，每页显示的条数
    public static final int PAGE_WAP_NUM = 12;
    //siteColumn的icon图标上传后缩放的最大宽度
    public static final int SITECOLUMN_ICON_MAXWIDTH = 100;
    //轮播图的最大宽度
    public static final int CAROUSEL_MAXWIDTH = 2600;
    //新闻图片的titlepic的最大宽度
    public static final int NEWS_TITLEPIC_MAXWIDTH = 1000;


    //普通注册成为会员后，拥有1000MB的存储空间
    public static final int REG_GENERAL_OSS_HAVE = 1000;
    //是否显示。
    public static boolean copyright = true;
    public static final String CONTENT_INPUTMODEL = """
            <div class="layui-form-item">
                <label class="layui-form-label" id="label_columnName">文章标题</label>
                <div class="layui-input-block">
                    <input type="text" name="title" required lay-verify="required" autocomplete="off" placeholder="限制30字以内"
                           class="layui-input" value="{news.title}">
                </div>
            </div>
            <div class="layui-form-item" id="sitecolumn_editUseTitlepic" style="display:none;">
                <label class="layui-form-label" id="label_columnName">标题图片</label>
                <div class="layui-input-block">
                    <input name="titlepic" id="titlePicInput" type="text" autocomplete="off" placeholder="点击右侧添加"
                           class="layui-input" value="{titlepicImage}" style="padding-right: 120px;">
                    <button type="button" class="layui-btn" id="uploadImagesButton" style="float: right;margin-top: -38px;">
                        <i class="layui-icon layui-icon-upload"></i>
                    </button>
                    <a href="{titlepicImage}" id="titlePicA" style="float: right;margin-top: -38px;margin-right: 60px;"
                       title="预览原始图片" target="_black">
                        <img id="titlePicImg" src="{titlepicImage}?x-oss-process=image/resize,h_38"
                             onerror="this.style.display='none';" style="height: 36px;max-width: 57px; padding-top: 1px;"
                             alt="预览原始图片">
                    </a><input class="layui-upload-file" type="file" name="fileName">
                </div>
            </div>
            <div class="layui-form-item" id="sitecolumn_editUseExtendPhotos" style="display:none;">
                <div id="photosDefaultValue" style="display:none;">{news.extend.photos}</div>
                <input type="hidden" value="0" id="photos_i" style="display:none;"/>
                <label class="layui-form-label" id="label_columnName">文章图集</label>
                <div class="layui-input-block" id="photoInputList" style="min-height: 0px;">
                    <div id="photos_input_item_{i}" style="padding-top:5px;">
                        <input name="extend.photos" id="titlePicInput{i}" type="text" autocomplete="off" placeholder="点击右侧添加"
                               class="layui-input" value="{value}" style="padding-right: 174px;">
                        <button type="button" name="{i}" class="layui-btn uploadImagesButton" id="uploadImagesButton{i}"
                                style="float: right;margin-top: -38px;">
                            <i class="layui-icon layui-icon-upload"></i>
                        </button>
                        <a href="{value}" id="titlePicA{i}" style="float: right;margin-top: -38px;margin-right: 116px;"
                           title="预览原始图片" target="_black">
                            <img id="titlePicImg{i}" src="{value}?x-oss-process=image/resize,h_38"
                                 onerror="this.style.display='none';" style="height: 36px;max-width: 57px; padding-top: 1px;"
                                 alt="预览原始图片">
                        </a><input class="layui-upload-file" type="file" name="fileName">
                        <a href="javascript:deletePhotosInput('{i}');" class="layui-btn"
                           style="float: right;margin-top: -38px;margin-right: 58px;" title="删除">
                            <i class="layui-icon layui-icon-delete"></i>
                        </a>
                    </div>
                    <!-- item模版结束 -->
                </div>
                <div style="padding-top:5px; padding-left:110px;">
                    <a href="javascript:appendPhotosInput('');" class="layui-btn layui-btn-sm layui-btn-primary layui-btn-radius"
                       style="float:left;">向图集添加一个图片输入框</a>
                </div>
            </div>
            <script type="text/javascript" src="/js/manager/cms/news_extend_photos.js"></script>
            <div class="layui-form-item" id="sitecolumn_editUseIntro" style="display:none;">
                <label class="layui-form-label" id="label_columnName">内容简介</label>
                <div class="layui-input-block">
                    <textarea class="layui-input" name="intro" style="height:70px;">{news.intro}</textarea>
                </div>
            </div>
            <!-- 配置文件 -->
            <script src="/js/tinymce/tinymce.min.js"></script>
            <!-- 编辑器源码文件 -->
            <div class="layui-form-item" id="sitecolumn_editUseText" style="display:none;">
                <label class="layui-form-label" id="sucai">内容正文</label>
                <div class="layui-input-block" id="ueditorUpperDiv" style="border: 0px;">
                    <label for="myEditor"></label><textarea class="layui-input" id="myEditor" name="text"
                                                            style="height: auto; padding-left: 0px; border: 0px;">{text}</textarea>
                </div>
            </div>
            <!-- 实例化编辑器 -->
            <script type="text/javascript">
                // 默认文件扩展类型配置
                const DEFAULT_EXTS = {
                    image: ".jpg,.jpeg,.png,.gif,.ico,.svg",
                    media: ".mp3,.mp4,.avi,.mov,.m4v",
                    file: ".pdf,.txt,.zip,.rar,.7z,.doc,.docx,.xls,.xlsx,.ppt,.pptx"
                };
                        
                // 初始化 TinyMCE
                tinymce.init({
                    selector: '#myEditor',
                    language: "zh_CN",
                    placeholder: "在这里输入文字",
                    min_width: 420,
                    height: 770,
                    branding: false,
                    font_formats:
                         "微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;宋体=simsun,serif;仿宋体=FangSong,serif;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;", //字体样式
                     plugins:
                         "print preview searchreplace autolink directionality visualblocks visualchars fullscreen image link media template code codesample table charmap hr pagebreak nonbreaking anchor insertdatetime advlist lists wordcount textpattern autosave emoticons indent2em",
                    toolbar: [
                         "fullscreen undo redo restoredraft | cut copy paste pastetext | forecolor backcolor bold italic underline strikethrough link anchor | alignleft aligncenter alignright alignjustify outdent indent | bullist numlist | blockquote subscript superscript removeformat powerpaste ",
                         "styleselect formatselect fontselect fontsizeselect |  table image axupimgs media emoticons charmap hr pagebreak insertdatetime  selectall visualblocks searchreplace | code print preview | indent2em lineheight formatpainter",
                     ],
                    fontsize_formats: '12px 14px 16px 18px 24px 36px 48px 56px 72px',
                   external_plugins: {
                       powerpaste: `/js/tinymce/plugins/powerpaste/plugin.min.js` // ${this.baseUrl}
                   },
                   powerpaste_word_import: 'propmt', // 参数:propmt, merge, clear
                   powerpaste_html_import: 'propmt', // 参数:propmt, merge, clear
                   powerpaste_allow_local_images: true, // 粘贴图片
                   paste_data_images: true,
                   //图片固定宽高自定义样式
                   content_style: 'body { background: #0d121c;color:#fff } p {margin: 5px 0;} img {max-width:100%;height:auto}',
                   // 粘贴内容处理
                   paste_postprocess: function (editor, fragment) {
                       // 处理粘贴图片的跨域请求
                       var imgs = fragment.node.getElementsByTagName('img');
                       for (let i = 0; i < imgs.length; ++i) {
                           imgs[i].crossOrigin = null;
                       }
                        // 处理粘贴视频
                       var videos = fragment.node.getElementsByTagName('video');
                       console.log("videos:", videos);
                       for (let i = 0; i < videos.length; ++i) {
                           videos[i].crossOrigin = null; // 防止跨域
                           // 如果你想处理视频源路径、大小等属性，可以在这里加逻辑
                       }
                       // 处理粘贴文件
                       var files = fragment.node.getElementsByTagName('a');
                       for (let i = 0; i < files.length; ++i) {
                           var href = files[i].getAttribute('href');
                           if (href && (href.endsWith('.pdf') || href.endsWith('.txt') || href.endsWith('.zip'))) {
                               // 如果粘贴的是文件，处理相应逻辑，比如设置样式或者添加提示
                               files[i].style.color = '#2196F3'; // 设置文件链接颜色为蓝色
                           }
                       }
                   },
                    images_upload_handler: async (blobInfo, success, failure) => {
                        try {
                            const response = await uploadFile('/sites/uploadImageTinymce.naii', blobInfo.blob(), blobInfo.filename(), {
                                size: "3MB",
                                exts: DEFAULT_EXTS.image
                            });
                            success(response.url);
                        } catch (err) {
                            failure(err.message || "文件上传失败");
                        }
                    },
                    file_picker_types: "file image media",
                    file_picker_callback: (callback, value, meta) => {
                        const acceptType = DEFAULT_EXTS[meta.filetype] || "*";
                        createFileInput(acceptType, async (file) => {
                            try {
                                const response = await uploadFile('/sites/uploadImageTinymceFile.naii', file, file.name, {
                                    accept: meta.filetype,
                                    size: "100MB",
                                    exts: acceptType
                                });
                                callback(response.url, { title: file.name });
                            } catch (err) {
                                alert(err.message || "文件上传失败");
                            }
                        });
                    }
                });
                        
                /**
                 * 创建文件输入框
                 * @param {string} accept 文件接受类型
                 * @param {function} callback 回调函数，返回用户选择的文件
                 */
                function createFileInput(accept, callback) {
                    const input = document.createElement("input");
                    input.type = "file";
                    input.accept = accept;
                    input.style.display = "none";
                    document.body.appendChild(input);
                        
                    input.addEventListener("change", () => {
                        if (input.files && input.files.length > 0) {
                            callback(input.files[0]);
                        }
                        document.body.removeChild(input);
                    });
                        
                    input.click();
                }
                        
                /**
                 * 文件上传通用方法
                 * @param {string} url 接口地址
                 * @param {File} file 文件对象
                 * @param {string} fileName 文件名
                 * @param {object} params 附加参数
                 * @returns {Promise<object>} 上传结果
                 */
                async function uploadFile(url, file, fileName, params) {
                    const formData = new FormData();
                    formData.append("file", file);
                    for (const key in params) {
                        formData.append(key, params[key]);
                    }
                    const response = await fetch(url, {
                        method: "POST",
                        body: formData
                    });
                    if (!response.ok) {
                        throw new Error(`HTTP Error: ${response.status}`);
                    }
                    const responseText = await response.text();
                    let result;
                         try {
                             result = JSON.parse(responseText);
                         } catch (e) {
                             throw new Error("Failed to parse JSON response: " + e.message);
                         }
                         if (!result || !result.url) {
                             throw new Error(result.info);
                         }
                    return result;
                }
                        
                /**
                 * 保存编辑内容
                 * @returns {string} 处理后的内容
                 */
                function save() {
                    const editorContent = tinymce.get('myEditor').getContent();
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = editorContent;
                        
                    // 设置图片样式
                    const images = tempDiv.getElementsByTagName('img');
                    for (const img of images) {
                        img.style.maxWidth = '100%';
                        img.style.height = 'auto';
                    }
                        
                    return tempDiv.innerHTML;
                }
            </script>""";

    /**
     * 轮播图再OSS上得存储路径
     */
    public static String getCarouselPath(Site site) {
        return "site/" + site.getId() + "/carousel/";
    }


    //private static String  firstAutoAssignDomain;	//下面方法的持久化缓存

    /**
     * 获取主域名，即 AUTO_ASSIGN_DOMAIN 配置的第一个域名
     * 例如，Global.get("AUTO_ASSIGN_DOMAIN") 为 ： wang.market,wscso.com
     *
     * @return 返回如 wang.market
     */
    public static String getFirstAutoAssignDomain() {
        if (SystemUtil.get("AUTO_ASSIGN_DOMAIN") != null) {
            if (SystemUtil.get("AUTO_ASSIGN_DOMAIN").indexOf(",") > 0) {
                //如果有多个，那么只取第一个
                String[] s = SystemUtil.get("AUTO_ASSIGN_DOMAIN").split(",");
                return s[0];
            } else {
                return SystemUtil.get("AUTO_ASSIGN_DOMAIN");
            }
        }
        return "请进入总管理后台，系统管理-系统变量下，修改变量名为 AUTO_ASSIGN_DOMAIN 的值";
    }
}