package cn.edu.sjtu.gateway.manager.cache;

import cn.edu.sjtu.gateway.manager.G;
import cn.edu.sjtu.gateway.vm.Global;

import cn.edu.sjtu.gateway.vm.util.SystemUtil;
import cn.edu.sjtu.gateway.tools.file.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;

/**
 * 自动在/cache/下创建data文件夹，用于缓存js数据
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class AutoCreateDataFolder {

    public AutoCreateDataFolder() {
        //初始化缓存文件夹，若根目录下没有缓存文件夹，自动创建
        if (!FileUtil.exists(SystemUtil.getProjectPath() + G.CACHE_FILE)) {
            String path = SystemUtil.getProjectPath();
            log.info("create -- data --{}", new File(path + G.CACHE_FILE).mkdir());
        }
        initCacheInputModelFolder();
        try {
            String fileInputPath = SystemUtil.getProjectPath() + Global.CACHE_INPUT_MODEL_FILE + "default.html";

            // 从模板文件读取内容
            String templatePath = SystemUtil.getProjectPath() + Global.INPUT_MODEL_TEMPLATE_FILE;
            String templateContent;

            // 检查模板文件是否存在
            if (FileUtil.exists(templatePath)) {
                // 如果模板文件存在，读取模板内容
                templateContent = FileUtil.read(templatePath);
                log.info("Using template file: {}", templatePath);
            } else {
                // 如果模板文件不存在，使用默认的硬编码内容
                templateContent = G.CONTENT_INPUTMODEL;
                log.info("Template file not found, using default content");

                // 创建模板目录
                String templateDir = SystemUtil.getProjectPath() + "templates" + File.separator + "inputmodel";
                if (!FileUtil.exists(templateDir)) {
                    new File(templateDir).mkdirs();
                    log.info("Created template directory: {}", templateDir);
                }

                // 将默认内容写入模板文件，以便将来可以直接编辑
                FileUtil.write(templatePath, G.CONTENT_INPUTMODEL, "UTF-8");
                log.info("Created default template file: {}", templatePath);
            }

            // 将内容写入缓存文件
            FileUtil.write(fileInputPath, templateContent, "UTF-8");
            log.info("Created inputModel.html from template");
        } catch (IOException e) {
            log.error("错误信息：------>" + e);
        }
    }
    private void initCacheInputModelFolder() {
        createPath(Global.CACHE_INPUT_MODEL_FILE);
        createPath(Global.CACHE_INPUT_MODEL_IMAGE);
    }
    public static void createPath(String path) {
        String projectPath = SystemUtil.getProjectPath();
        String fullPath = projectPath + path;

        // 如果路径不存在，尝试创建
        if (!FileUtil.exists(fullPath)) {
            log.info("Creating resource folder: " + fullPath);

            String[] folders = path.split("/");
            StringBuilder imagePath = new StringBuilder(projectPath);

            for (String folder : folders) {
                if (!folder.isEmpty()) {
                    // 拼接当前文件夹路径
                    imagePath.append(folder).append(File.separator);
                    File file = new File(imagePath.toString());

                    // 如果文件夹不存在，则创建
                    if (!file.exists()) {
                        // mkdirs() 会创建多层目录
                        boolean mkdir = file.mkdirs();
                        if (!mkdir) {
                            log.error("Create folder error: " + file.getAbsolutePath());
                        } else {
                            log.info("Created folder: " + file.getAbsolutePath());
                        }
                    }
                }
            }
            log.info("All folders created: " + imagePath);
        }
    }

}
