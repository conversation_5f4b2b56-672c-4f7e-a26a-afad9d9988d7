package cn.edu.sjtu.gateway.manager.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import static javax.persistence.GenerationType.IDENTITY;
import javax.persistence.Id;
import javax.persistence.Table;
import cn.edu.sjtu.gateway.vm.util.Sql;
import cn.edu.sjtu.gateway.manager.G;
import lombok.Setter;

/**
 * 网站站点信息表
 * <AUTHOR>
 */
@Entity
@Table(name = "site")
public class Site implements java.io.Serializable {
	
	/**
	 * 隐藏手机端首页的Banner
	 */
	public final static Short MSHOWBANNER_HIDDEN = 0;
	/**
	 * 显示手机端首页的Banner
	 */
	public final static Short MSHOWBANNER_SHOW = 1;
	
	/**
	 * 客户端类型：PC电脑端
	 */
	public static final Short CLIENT_PC = 1;
	/**
	 * 客户端类型：WAP手机端
	 */
	public static final Short CLIENT_WAP = 2;
	/**
	 * 高级自定义模版CMS
	 */
	public static final Short CLIENT_CMS = 3;
	
	/**
	 * 站点状态，1:正常
	 */
	public static final Short STATE_NORMAL = 1;
	/**
	 * 站点状态，2:冻结、暂停
	 */
	public static final Short STATE_FREEZE = 2;
	
	/**
	 * 网站html存储方式：默认方式，采用AttachmentUtil存储
	 */
	public static final String GENERATE_HTML_STORAGE_TYPE_DEFAULT = "default";
	@Setter
    private Integer id;						//自动编号
	@Setter
    private String name;					//站点名字
	@Setter
    private Integer userid;					//站点所属用户，是哪个用户创建的，对应 User.id
	@Setter
    private Integer addtime;				//站点添加时间，10位linux时间戳
	@Setter
    private Short mShowBanner;				//pc、wap模式使用的，已废弃！
	@Setter
    private String phone;					//联系手机，可以在模版中，通过 {site.phone} 调用
	@Setter
    private String qq;						//联系QQ，可以在模版中，通过 {site.qq} 调用
	@Setter
    private String domain;					//站点自动分配的二级域名
	@Setter
    private Short client;					//客户端类型，现在都是 CMS模式，之前的pc、wap已经没有了
	@Setter
    private String keywords;				//站点的关键字，可以在模版中，通过 {site.keywords} 调用
	@Setter
    private String address;					//站点公司地址，可以在模版中，通过 {site.address} 调用
	@Setter
    private String username;				//联系人姓名
	@Setter
    private String companyName;				//单位名、工作室名字、团体名字, 可以在模版中，通过 {site.companyName} 调用
	@Setter
    private String bindDomain;				//用户自己绑定的域名
    /**
     * -- SETTER --
     *
     * @param columnId 传入如 345,
     */
    @Setter
    private String columnId;				//栏目id，这里暂时只记录栏目类型的ID，方便生成页面时，生成Nav标签的填充，方便搜索引擎抓取
	@Setter
    private Short state;					//站点状态，1正常；2冻结
	private String templateName;			//自定义模版的模版名字，位于 /template/模版名字，这里的模版单纯修改HTML，没有动态后台
	@Setter
    private Integer expiretime;				//网站过期时间，linux时间戳
	@Setter
    private Integer attachmentUpdateDate;	//当前附件占用空间大小，最后一次计算的日期，存储格式如 20191224 ，每天登录时都会计算一次
	@Setter
    private Integer attachmentSize;			//当前附件占用的空间大小，服务器空间，或云存储空间。计算的是 site/$siteid/ 下的空间占用大小，单位是KB
	@Setter
    private Integer attachmentSizeHave;		//当前用户网站所拥有的空间大小，单位是MB
	
	@Setter
    private String generateHtmlStorageType;	//生成html页面的方式，存储方式， obs:obs buckname存储，  ftp:ftp方式存储，  空或者default或者其他则是默认的AttachmentUtil 方式存储
	
	//v6.1 增加
	@Setter
    private Integer newsSize;			//当前网站的文章条数
	@Setter
    private Integer newsSizeHave;				//当前网站的文章允许上传多少条。默认是1000（独立栏目也是一个文章）
	
	/**
	 * @deprecated
	 */
	@Setter
    private Integer templateId;				//pc、wap模式使用的，已废弃！
	/**
	 * @deprecated
	 */
	@Setter
    private Integer aboutUsCid;				//pc、wap模式使用的，已废弃！
	/**
	 * @deprecated
	 */
	@Setter
    private String logo;					//pc、wap模式使用的，已废弃！
	
	@Setter
    private String remark;					//站点备注，代理商给网站的备注，方便代理商记录这个网站干嘛的
	
	public Site() {
		this.state = STATE_NORMAL;
		this.attachmentSizeHave = G.REG_GENERAL_OSS_HAVE;
		this.remark = "";
		this.generateHtmlStorageType = GENERATE_HTML_STORAGE_TYPE_DEFAULT;
		this.newsSizeHave = 1000;
		this.newsSize = 0;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "id", unique = true, nullable = false)
	public Integer getId() {
		if(this.id == null){
			return 0;
		}
		return this.id;
	}

    @Column(name = "name", columnDefinition="char(40) COMMENT '站点名字' default ''")
	public String getName() {
		return name;
	}

    @Column(name = "userid", columnDefinition="int(11) COMMENT '站点所属用户，是哪个用户创建的，对应 User.id' default '0'")
	public Integer getUserid() {
		return userid;
	}

    @Column(name = "addtime", columnDefinition="int(11) COMMENT '站点添加时间，10位linux时间戳'")
	public Integer getAddtime() {
		return addtime;
	}

    @Column(name = "phone", columnDefinition="char(12) COMMENT '联系手机，可以在模版中，通过 {site.phone} 调用' default ''")
	public String getPhone() {
		return phone;
	}

    @Column(name = "qq", columnDefinition="char(12) COMMENT '联系QQ，可以在模版中，通过 {site.qq} 调用' default ''")
	public String getQq() {
		return qq;
	}

    @Column(name = "template_id", columnDefinition="int(11) COMMENT 'pc、wap模式使用的，已废弃！' default '0'")
	public Integer getTemplateId() {
		return templateId;
	}

    @Column(name = "domain", columnDefinition="char(30) COMMENT '站点自动分配的二级域名' default ''")
	public String getDomain() {
		if(domain == null || domain.isEmpty()){
			domain = id+"";
		}
		return domain;
	}

    @Column(name = "about_us_cid", columnDefinition="int(11) COMMENT 'pc、wap模式使用的，已废弃！' default '0'")
	public Integer getAboutUsCid() {
		return aboutUsCid;
	}

    @Column(name = "logo", columnDefinition="char(80) COMMENT 'pc、wap模式使用的，已废弃！' default ''")
	public String getLogo() {
		return logo;
	}

    @Column(name = "client", columnDefinition="tinyint(2) COMMENT '客户端类型，现在都是 CMS模式，之前的pc、wap已经没有了' default '0'")
	public Short getClient() {
		return client;
	}

    @Column(name = "keywords", columnDefinition="char(38) COMMENT '站点的关键字，可以在模版中，通过 {site.keywords} 调用' default ''")
	public String getKeywords() {
		return keywords;
	}

    @Column(name = "address", columnDefinition="char(80) COMMENT '站点公司地址，可以在模版中，通过 {site.address} 调用' default ''")
	public String getAddress() {
		return address;
	}

    @Column(name = "username", columnDefinition="char(10) COMMENT '联系人姓名' default ''")
	public String getUsername() {
		return username;
	}

    @Column(name = "company_name", columnDefinition="char(30) COMMENT '单位名、工作室名字、团体名字, 可以在模版中，通过 {site.companyName} 调用' default ''")
	public String getCompanyName() {
		return companyName;
	}

    @Column(name = "bind_domain", columnDefinition="char(30) COMMENT '用户自己绑定的域名' default ''")
	public String getBindDomain() {
		return bindDomain;
	}

    @Column(name = "column_id", columnDefinition="char(80) COMMENT '栏目id，这里暂时只记录栏目类型的ID，方便生成页面时，生成Nav标签的填充，方便搜索引擎抓取' default ''")
	public String getColumnId() {
		return columnId;
	}

    @Column(name = "state", columnDefinition="tinyint(2) COMMENT '站点状态，1正常；2冻结' default '0'")
	public Short getState() {
		return state;
	}

    @Column(name = "template_name", columnDefinition="char(20) COMMENT '自定义模版的模版名字，位于 /template/模版名字，这里的模版单纯修改HTML，没有动态后台' default ''")
	public String getTemplateName() {
		return templateName;
	}

	public void setTemplateName(String templateName) {
		this.templateName = Sql.filter(templateName);
	}

	@Column(name = "expiretime", columnDefinition="int(11) COMMENT '网站过期时间，linux时间戳'")
	public Integer getExpiretime() {
		return expiretime;
	}

    @Column(name = "attachment_update_date", columnDefinition="int(11) COMMENT '当前附件占用空间大小，最后一次计算的日期，存储格式如 20191224 ，每天登录时都会计算一次'")
	public Integer getAttachmentUpdateDate() {
		return attachmentUpdateDate;
	}

    @Column(name = "attachment_size", columnDefinition="int(11) COMMENT '当前附件占用的空间大小，服务器空间，或云存储空间。计算的是 site/siteid/ 下的空间占用大小，单位是KB' default '0'")
	public Integer getAttachmentSize() {
		return attachmentSize;
	}

    @Column(name = "attachment_size_have", columnDefinition="int(11) COMMENT '当前用户网站所拥有的空间大小，单位是MB' default '0'")
	public Integer getAttachmentSizeHave() {
		if(attachmentSizeHave == null){
			this.attachmentSizeHave = 0;
		}
		return attachmentSizeHave;
	}

    @Column(name = "remark", columnDefinition="char(255) COMMENT '站点备注，代理商给网站的备注，方便代理商记录这个网站干嘛的' default ''")
	public String getRemark() {
		return remark;
	}

    @Column(name = "generate_html_storage_type", columnDefinition="char(20) COMMENT '生成html页面的方式，存储方式， obs:obs buckname存储，  ftp:ftp方式存储，  空或者default或者其他则是默认的AttachmentUtil 方式存储' default ''")
	public String getGenerateHtmlStorageType() {
		if(generateHtmlStorageType == null || generateHtmlStorageType.length() == 0) {
			generateHtmlStorageType = GENERATE_HTML_STORAGE_TYPE_DEFAULT;
		}
		return generateHtmlStorageType;
	}

    @Column(name = "news_size_have", columnDefinition="int(11) COMMENT '当前网站的文章允许上传多少条。默认是1000（独立栏目也是一个文章）'")
	public Integer getNewsSizeHave() {
		if(this.newsSizeHave == null) {
			this.newsSizeHave = 1000;
		}
		return newsSizeHave;
	}

    @Column(name = "news_size", columnDefinition="int(11) COMMENT '当前网站的文章条数'")
	public Integer getNewsSize() {
		if(newsSize == null) {
			newsSize = 0;
		}
		return newsSize;
	}

    @Override
	public String toString() {
		return "Site [id=" + id + ", name=" + name + ", userid=" + userid
				+ ", addtime=" + addtime + ", mShowBanner=" + mShowBanner
				+ ", phone=" + phone + ", qq=" + qq + ", templateId="
				+ templateId + ", domain=" + domain + ", aboutUsCid="
				+ aboutUsCid + ", logo=" + logo + ", client=" + client
				+ ", keywords=" + keywords + ", address=" + address
				+ ", username=" + username + ", companyName=" + companyName
				+ ", bindDomain=" + bindDomain + ", columnId=" + columnId
				+ ", state=" + state + ", templateName=" + templateName
				+ ", expiretime=" + expiretime + "]";
	}
	
}