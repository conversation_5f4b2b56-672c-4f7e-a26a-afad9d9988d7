package cn.edu.sjtu.gateway.manager.controller;

import cn.edu.sjtu.gateway.vm.Global;

import cn.edu.sjtu.gateway.tools.version.VersionUtil;
import cn.edu.sjtu.gateway.tools.version.VersionVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

/**
 * 版本相关
 * <AUTHOR>
 */
@Controller
@RequestMapping("/")
@Slf4j
public class VersionController_ extends BaseController {
	/**
	 * 获取当前系统版本，此需要开发者自行修改，根据当前版本的记录方式返回版本号，如返回 3.7
	 * @return
	 */
	private String getCurrentVersion(){
		return Global.VERSION;
	}
	
	/**
	 * 检查当前系统是否有最新版本
	 * @return 若有最新版本，VersionVO.getResult == VersionVO.SUCCESS
	 */
	@RequestMapping("getNewVersion${url.suffix}")
	@ResponseBody
	public VersionVO getNewVersion(HttpServletRequest request){
		log.info("检查当前系统是否有最新版本 {}", "当前版本v"+getCurrentVersion());
		return VersionUtil.cloudContrast("http://version.xnx3.com/naii.html", getCurrentVersion());
	}
	
}
