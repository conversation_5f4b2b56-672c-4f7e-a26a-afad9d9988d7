package cn.edu.sjtu.gateway.manager.controller;


import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;

/**
 * 公共的
 * <AUTHOR>
 */
@Controller
@RequestMapping("/page")
@Slf4j
public class PageController extends BaseController {
	/**
	 * 修改独立页面
	 * 以废弃，保留是为了兼容以前得。以后使用，用 NewsController.updateNewsByCid()
	 * @param cid {@link cn.edu.sjtu.gateway.manager.entity.SiteColumn}.id
	 * @deprecated
	 */
	@RequestMapping("page${url.suffix}")
	public String page(HttpServletRequest request,
			@RequestParam(value = "cid", required = false , defaultValue="0") int cid,Model model){
		log.info("修改独立页面，已废弃的接口，不应在用");
		return redirect("news/updateNewsByCid.naii?cid="+cid);
	}

	
//	/**
//	 * 自定义页面列表，可以获取当前网站所有的以html为后缀的页面
//	 * 临时用不到了，预留。没准什么时候用到
//	 */
//	@RequestMapping("customPageList.naii")
//	public String customPageList(Model model){
//		List<OSSObjectSummary> allList = OSSUtil.getFolderObjectList("site/"+getSiteId()+"/");
//		List<OSSObjectSummary> htmlList = new ArrayList<OSSObjectSummary>();
//		for (int i = 0; i < allList.size(); i++) {
//			OSSObjectSummary obj = allList.get(i);
//			String suffix = Lang.findFileSuffix(obj.getKey());
//			if(suffix == null || !suffix.equals("html")){
//				continue;
//			}
//			obj.setKey(obj.getKey().replace("site/"+getSiteId()+"/", "").replace(".html", ""));
//			htmlList.add(obj);
//		}
//		
//		AliyunLog.addActionLog(getSiteId(), "打开当前网站所有以html为后缀的页面列表");
//		
//		siteService.getTemplateCommonHtml(getSite(), "自定义页面列表", model);
//		model.addAttribute("htmlList", htmlList);
//		return "page/customPageList";
//	}
}
