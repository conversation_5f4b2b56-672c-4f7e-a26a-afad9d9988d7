package cn.edu.sjtu.gateway.manager.entity;

import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * SiteCarousel entity. <AUTHOR> Persistence Tools
 */
@Setter
@Entity
@Table(name = "carousel")
public class Carousel implements java.io.Serializable {

    /**
     * 显示
     */
    public final static short ISSHOW_SHOW = 1;
    /**
     * 隐藏
     */
    public final static short ISSHOW_HIDDEN = 2;

    /**
     * 默认的，头部通用的图，比如模版3、5、6的首页跟所有内页、模版7的内页，都是用的这个
     */
    public final static short TYPE_DEFAULT_PAGEBANNER = 1;
    /**
     * 只有首页的Banner才会使用的，比如模版7的首页第一屏的大Banner图。
     */
    public final static short TYPE_INDEXBANNER = 2;

    // Fields
    private Integer id;
    //点击跳转的目标url
    private String url;
    //添加时间
    private Integer addtime;
    //是否显示，1为显示，0为不显示
    private Short isshow;
    //排序，数小越靠前
    private Integer rank;
    //轮播图属于哪个站点，对应site.id
    private Integer siteid;
    //轮播图属于哪个用户建立的，对应user.id
    private Integer userid;
    //轮播图的url，分两种，一种只是文件名，如asd.png  另一种为绝对路径
    private String image;
    //类型，默认1:内页通用的头部图(有的模版首页也用)；2:只有首页顶部才会使用的图
    private Short type;

    /**
     * default constructor
     */
    public Carousel() {
        this.type = TYPE_DEFAULT_PAGEBANNER;
    }

    /**
     * full constructor
     */
    public Carousel(String url, Integer addtime, Short isshow, Integer rank) {
        this.url = url;
        this.addtime = addtime;
        this.isshow = isshow;
        this.rank = rank;
    }

    // Property accessors
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    public Integer getId() {
        return this.id;
    }

    @Column(name = "url", columnDefinition = "char(120) COMMENT '点击跳转的目标url' default ''")
    public String getUrl() {
        return this.url;
    }

    @Column(name = "addtime", columnDefinition = "int(11) COMMENT '添加时间'")
    public Integer getAddtime() {
        return this.addtime;
    }

    @Column(name = "isshow", columnDefinition = "tinyint(2) COMMENT '是否显示，1为显示，0为不显示' default '0'")
    public Short getIsshow() {
        return this.isshow;
    }

    @Column(name = "rank", columnDefinition = "int(11) COMMENT '排序，数小越靠前' default '0'")
    public Integer getRank() {
        return this.rank;
    }

    @Column(name = "siteid", columnDefinition = "int(11) COMMENT '轮播图属于哪个站点，对应site.id' default '0'")
    public Integer getSiteid() {
        return siteid;
    }

    @Column(name = "userid", columnDefinition = "int(11) COMMENT '轮播图属于哪个用户建立的，对应user.id' default '0'")
    public Integer getUserid() {
        return userid;
    }

    @Column(name = "image", columnDefinition = "char(120) COMMENT '轮播图的url，分两种，一种只是文件名，如asd.png  另一种为绝对路径' default ''")
    public String getImage() {
        return image;
    }

    @Column(name = "type", columnDefinition = "tinyint(2) COMMENT '类型，默认1:内页通用的头部图(有的模版首页也用)；2:只有首页顶部才会使用的图' default '0'")
    public Short getType() {
        return type;
    }

    @Override
    public String toString() {
        return "Carousel [id=" + id + ", url=" + url + ", addtime=" + addtime + ", isshow=" + isshow + ", rank=" + rank
                + ", siteid=" + siteid + ", userid=" + userid + ", image=" + image + ", type=" + type + "]";
    }

}