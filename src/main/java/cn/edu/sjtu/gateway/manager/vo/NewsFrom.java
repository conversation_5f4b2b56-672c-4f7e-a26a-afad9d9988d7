package cn.edu.sjtu.gateway.manager.vo;

import cn.edu.sjtu.gateway.vm.vo.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 信息相关
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class NewsFrom extends BaseVO {
	private Integer id;
	//对应user.id，是哪个用户发表的
	private Integer userid;
	//发布时间
	private Integer addtime;
	//标题
	private String title;
	//标题图片
	private String titlepic;	//头图
	//简介,从内容正文里自动剪切出开始的160个汉字
	private String intro;
	//评论的总数量
	//反对的总数量
	private Integer opposenum;
	//阅读的总数量
	private Integer readnum;
	//1新闻；2图文；3独立页面
	private Short type;
	//1：正常显示；2：隐藏不显示
	private Short status;
	//评论的总数量
	private Integer commentnum;
	//所属栏目id，对应site_column.id
	private Integer cid;
	//所属站点，对应site.id
	private Integer siteid;
	//是否是合法的，1是，0不是，涉嫌
	private Short legitimate;
	//要生成的html文件的名字。比如这里的值为 abc ，那么一键部署时，该页面的url访问路径便是 abc.html
	private String htmlName;

	//以下两个为预留字段，可以通过输入模型进行扩展
	private String reserve1;
	private String reserve2;
	private Long count;


}
