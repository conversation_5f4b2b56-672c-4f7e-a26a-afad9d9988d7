package cn.edu.sjtu.gateway.manager.util.TemplateManagerMenu;

import java.util.ArrayList;
import java.util.List;

/**
 * 一级菜单，顶级菜单。网站用户登录后，能看到哪些菜单，每个大菜单（一级菜单）都是这里生成的。每个能生成一个一级菜单
 * <AUTHOR>
 *
 */
public class FirstMenu {
	/*
	 * 一级菜单，顶级菜单
	 */
	public String firstMenu_li_id;		//一级菜单中，li标签的id
	public String firstMenu_a_id;		//一级菜单中，a标签的id
	public String firstMenu_a_href;		//一级菜单中，a标签的href的值
	public String firstMenu_icon;		//一级菜单中，图标，如： &#xe609;
	public String firstMenu_font;		//一级菜单中，菜单显示的文字，如： 一键部署
	
	/*
	 * 二级菜单，子菜单。如果有值，那么就有二级菜单，没有的话就是没有二级菜单
	 */
	List<TwoMenu> twoMenuList;
	
	/**
	 * @param li_id 一级菜单中，li标签的id，没有可为null
	 * @param a_id 一级菜单中，a标签的id，没有可为null
	 * @param a_href 一级菜单中，a标签的href的值
	 * @param icon 一级菜单中，图标，如： &#xe609;
	 * @param font 一级菜单中，菜单显示的文字，如： 一键部署
	 */
	public FirstMenu(String li_id, String a_id, String a_href, String icon, String font) {
		twoMenuList = new ArrayList<TwoMenu>();
		
		firstMenu_li_id = li_id;
		firstMenu_a_id = a_id;
		firstMenu_a_href = a_href;
		firstMenu_icon = icon;
		firstMenu_font = font;
	}
	
	/**
	 * 向当前顶级菜单中，加入二级菜单
	 * @param dd_id dd的id，若没有，可为null
	 * @param a_id	a标签的id，若没有，可为null
	 * @param a_href	a标签的href的值，必填
	 * @param font		a标签的文字，也就是二级菜单的文字，必填
	 */
	public void addTwoMenu(String dd_id, String a_id, String a_href, String font){
		TwoMenu tmb = new TwoMenu();
		if(dd_id != null && !dd_id.isEmpty()){
			tmb.setTwoMenu_dd_id(dd_id);
		}
		if(a_id != null && !a_id.isEmpty()){
			tmb.setTwoMenu_a_id(a_id);
		}
		tmb.setTwoMenu_a_href(a_href);
		tmb.setTwoMenu_font(font);
		this.twoMenuList.add(tmb);
	}
	
	/**
	 * 生成当前菜单的li标签的html代码。
	 */
	public String gainMenuHTML(){
		StringBuffer sb = new StringBuffer();
		sb.append("<li class=\"layui-nav-item\"").append((this.firstMenu_li_id != null && !this.firstMenu_li_id.isEmpty()) ? " id=\"" + this.firstMenu_li_id + "\" " : "").append(">").append("	<a ").append((this.firstMenu_a_id != null || this.firstMenu_a_id.length() > 0) ? "id=\"" + this.firstMenu_a_id + "\"" : "").append(" href=\"").append(this.firstMenu_a_href).append("\">").append("		<i class=\"layui-icon firstMenuIcon\">").append(this.firstMenu_icon).append("</i>").append("		<span class=\"firstMenuFont\">").append(this.firstMenu_font).append("</span>").append("	</a>");
		//判断是否有子栏目，如果有，那么也加入进去
		if(!this.twoMenuList.isEmpty()){
			sb.append("<dl class=\"layui-nav-child\">");
			for (int i = 0; i < this.twoMenuList.size(); i++) {
				TwoMenu twoMenu = this.twoMenuList.get(i);
				sb.append("<dd ").append((twoMenu.getTwoMenu_dd_id() != null && !twoMenu.getTwoMenu_dd_id().isEmpty()) ? "id=\"" + twoMenu.getTwoMenu_dd_id() + "\"" : "").append(" class=\"twoMenu\"><a ").append((twoMenu.getTwoMenu_a_id() != null || twoMenu.getTwoMenu_a_id().length() > 0) ? "id=\"" + twoMenu.getTwoMenu_a_id() + "\"" : "").append(" class=\"subMenuItem\" href=\"").append(twoMenu.getTwoMenu_a_href()).append("\">").append(twoMenu.getTwoMenu_font()).append("</a></dd>");
			}
			sb.append("</dl>");
		}
		
		sb.append("</li>");
		return sb.toString();
	}
	
}
