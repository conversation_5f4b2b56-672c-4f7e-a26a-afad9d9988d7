package cn.edu.sjtu.gateway.http;

import lombok.Getter;

import java.util.List;
import java.util.Map;
import java.util.Vector;

/**
 * 响应对象
 * <AUTHOR>
 */
@Getter
public class Response {
 
	public String urlString;
	public int defaultPort;
	public String file;
	public String host;
	public String path;
	public int port;
	public String protocol;
	public String query;
	public String ref;
	public String userInfo;
	public String contentEncoding;
	public String content;
	public String contentType;
	public int code;
	public String message;
	public String method;
	public int connectTimeout;
	public int readTimeout;
	public String cookie;
	public Map<String, List<String>> headerFields;
 
	public Vector<String> contentCollection;

    @Override
	public String toString() {
		return "HttpResponse [urlString=" + urlString + ", defaultPort="
				+ defaultPort + ", file=" + file + ", host=" + host + ", path="
				+ path + ", port=" + port + ", protocol=" + protocol
				+ ", query=" + query + ", ref=" + ref + ", userInfo="
				+ userInfo + ", contentEncoding=" + contentEncoding
				+ ", content=" + content + ", contentType=" + contentType
				+ ", code=" + code + ", message=" + message + ", method="
				+ method + ", connectTimeout=" + connectTimeout
				+ ", readTimeout=" + readTimeout + ", cookie=" + cookie
				+ ", headerFields=" + headerFields + ", contentCollection="
				+ contentCollection + "]";
	}
	
}
