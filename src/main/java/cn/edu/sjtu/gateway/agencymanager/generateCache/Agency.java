package cn.edu.sjtu.gateway.agencymanager.generateCache;

import org.springframework.stereotype.Component;
import cn.edu.sjtu.gateway.vm.generateCache.BaseGenerate;

/**
 * 站点栏目导航
 * <AUTHOR>
 */
@Component(value="agencymanagerAgency")
public class Agency extends BaseGenerate {
	public Agency() {
		allowCreateSubAgency();
		allowSubAgencyCreateSub();
	}
	
	public void allowCreateSubAgency(){
		createCacheObject("allowCreateSubAgency");
		cacheAdd(cn.edu.sjtu.gateway.agencymanager.entity.Agency.ALLOW_CREATE_SUBAGENCY_YES, "允许");
		cacheAdd(cn.edu.sjtu.gateway.agencymanager.entity.Agency.ALLOW_CREATE_SUBAGENCY_NO, "禁止");
		generateCacheFile();
	}
	
	public void allowSubAgencyCreateSub(){
		createCacheObject("allowSubAgencyCreateSub");
		cacheAdd(cn.edu.sjtu.gateway.agencymanager.entity.Agency.ALLOW_CREATE_SUBAGENCY_YES, "允许");
		cacheAdd(cn.edu.sjtu.gateway.agencymanager.entity.Agency.ALLOW_CREATE_SUBAGENCY_NO, "禁止");
		generateCacheFile();
	}
	
}
