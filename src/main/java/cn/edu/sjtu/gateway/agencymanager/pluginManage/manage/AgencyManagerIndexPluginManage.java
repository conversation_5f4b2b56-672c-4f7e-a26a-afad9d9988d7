package cn.edu.sjtu.gateway.agencymanager.pluginManage.manage;

import cn.edu.sjtu.gateway.tools.ScanClassUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;


/**
 * 代理后台首页的html源码处理
 * <AUTHOR>
 *
 */
@Component(value="PluginManageForAgencyManagerIndex")
@Slf4j
public class AgencyManagerIndexPluginManage {
	//处理html源代码的插件，这里开启项目时，便将有关此的插件加入此处
	public static List<Class<?>> classList;
	static{
		List<Class<?>> allClassList = ScanClassUtil.getClasses("cn.edu.sjtu.gateway.tools.naii");
		classList = ScanClassUtil.searchByInterfaceName(allClassList, "cn.edu.sjtu.gateway.agencymanager.pluginManage.interfaces.AgencyManagerIndexInterface");
        for (Class<?> aClass : classList) {
            log.info("装载 AgencyManagerIndex 插件：" + aClass.getName());
        }
	}

	/**
	 * 代理后台追加的html
	 * @return 要追加到html最后面的 html代码
	 */
	public static String manage() throws InstantiationException, IllegalAccessException, IllegalArgumentException, InvocationTargetException, NoSuchMethodException, SecurityException{
		/**** 针对html源代码处理的插件 ****/
		StringBuilder sb = new StringBuilder();
        for (Class<?> c : classList) {
            Object invoke = null;
            invoke = c.newInstance();
            //运用newInstance()来生成这个新获取方法的实例
            Method m = c.getMethod("agencyManagerIndexAppendHtml", new Class[]{});    //获取要调用的init方法
            //动态构造的Method对象invoke委托动态构造的InvokeTest对象，执行对应形参的add方法
            Object o = m.invoke(invoke, new Object[]{});
            if (o != null && !"null".equals(o)) {
                sb.append(o.toString());
            }
        }
		return sb.toString();
	}
	
	
}
