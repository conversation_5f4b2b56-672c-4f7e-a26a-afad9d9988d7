package cn.edu.sjtu.gateway.agencymanager;

import cn.edu.sjtu.gateway.agencymanager.entity.Agency;
import cn.edu.sjtu.gateway.agencymanager.entity.AgencyData;

/**
 * 常用的一些函数
 * <AUTHOR>
 */
public class Func {
	
	/**
	 * 获取当前登录用户的上级用户代理信息。如果当前用户的上级有，且是代理的话
	 * @return {@link cn.edu.sjtu.gateway.agencymanager.entity.Agency} 或 null
	 */
	public static Agency getParentAgency(){
		return cn.edu.sjtu.gateway.vm.util.SessionUtil.getParentAgency();
	}
	
	/**
	 * 获取当前登录用户的上级用户代理信息的变长表 (agency_data) 数据。如果当前用户的上级有，且是代理的话
	 * @return {@link cn.edu.sjtu.gateway.agencymanager.entity.AgencyData} 或 null
	 */
	public static AgencyData getParentAgencyData(){
		return cn.edu.sjtu.gateway.vm.util.SessionUtil.getParentAgencyData();
	}
	
	
	/**
	 * 获取当前登录用户的代理信息。如果当前用户是代理的话
	 * @return {@link cn.edu.sjtu.gateway.agencymanager.entity.Agency} 或 null
	 */
	public static Agency getMyAgency(){
		return cn.edu.sjtu.gateway.vm.util.SessionUtil.getAgency();
	}
	

	/**
	 * 获取当前代理信息的变长表 (agency_data) 数据。如果当前用户的上级有，且是代理的话
	 * @return {@link cn.edu.sjtu.gateway.agencymanager.entity.AgencyData} 或 null
	 */
	public static AgencyData getMyAgencyData(){
		return cn.edu.sjtu.gateway.vm.util.SessionUtil.getAgencyData();
	}
}
