package cn.edu.sjtu.gateway.agencymanager.util;

import cn.edu.sjtu.gateway.vm.entity.User;
import cn.edu.sjtu.gateway.vm.shiro.ShiroFunc;

import java.util.HashMap;
import java.util.Map;

/**
 * 日志
 *
 * <AUTHOR>
 */
public class SiteSizeChangeLogUtil {
    /**
     * 增加动作日志。此方法不可直接调用，需间接
     *
     * @param userid   当前日志属于哪个登录用户的，对应其user.id
     * @param username 当前日志属于哪个登录用户的，对应其user.username
     * @param remark   备注说明
     * @param goalid   其余额变动，是开通的哪个站点引起的，记录站点的id，或者是哪个人给他增加的，记录给他增加的人的userid
     * @param ip       操做人的ip地址
     * @param topic    主题，分类。减去站币，消费，传入"xiaofei"， 增加站币，充值，传入"chongzhi"
     */
    private static void addChangeLog(int userid, String username, String agencyName, String remark, int agencySiteSizeChange, int changeBefore, int changeAfter, int goalid, String ip, String topic) {
        StackTraceElement st = Thread.currentThread().getStackTrace()[3];

        Map<String, Object> params = new HashMap<String, Object>();
        /*用户相关信息*/
        params.put("userid", userid + "");
        params.put("username", username);
        /*日志信息*/
        params.put("goalid", goalid + "");
        params.put("remark", remark);
        /*代理信息，如果是代理操作的话*/
        params.put("agencyName", agencyName);
        params.put("changeBefore", changeBefore + "");
        params.put("changeAfter", changeAfter + "");
        params.put("agencySiteSizeChange", agencySiteSizeChange + "");
        /*使用的类的信息，来源位置*/
        params.put("class", st.getClassName());
        params.put("method", st.getMethodName());

    }

    /**
     * 消费日志，那肯定就是有代理操作的。管理员操作只是充值
     *
     * @see #addChangeLog(int, String, String, String, int, int, int, int, String, String)
     */
    public static void xiaofei(String agencyName, String remark, int agencySiteSizeChange, int changeBefore, int changeAfter, int goalid, String ip) {
        //当前登录用户信息
        User user = ShiroFunc.getUser();
        int userid = 0;
        String username = "";
        if (user != null) {
            userid = user.getId();
            username = user.getUsername();
        }

        addChangeLog(userid, username, agencyName, remark, agencySiteSizeChange, changeBefore, changeAfter, goalid, ip, "xiaofei");
    }

    /**
     * 充值日志，那肯定就是有代理操作的。（管理员操作只能是消费，给代理充值后是消费日志）充值大多都是我(当前登录用户)给对方充值，所以对方是未登录的，手动传入其用户信息
     *
     * @param agencyName
     * @param remark
     * @param agencySiteSizeChange
     * @param changeBefore
     * @param changeAgter
     * @param goalid
     * @param ip
     */
    public static void chongzhi(int userid, String username, String agencyName, String remark, int agencySiteSizeChange, int changeBefore, int changeAgter, int goalid, String ip) {
        addChangeLog(userid, username, agencyName, remark, agencySiteSizeChange, changeBefore, changeAgter, goalid, ip, "chongzhi");
    }
}
