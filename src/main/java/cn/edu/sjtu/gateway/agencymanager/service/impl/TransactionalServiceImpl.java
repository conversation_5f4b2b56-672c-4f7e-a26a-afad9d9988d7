package cn.edu.sjtu.gateway.agencymanager.service.impl;

import cn.edu.sjtu.gateway.manager.entity.Site;
import cn.edu.sjtu.gateway.manager.service.SiteService;
import cn.edu.sjtu.gateway.manager.vo.SiteVO;
import cn.edu.sjtu.gateway.manager.vo.UserVO;
import cn.edu.sjtu.gateway.agencymanager.entity.Agency;
import cn.edu.sjtu.gateway.agencymanager.entity.SiteSizeChange;
import cn.edu.sjtu.gateway.agencymanager.service.TransactionalService;
import cn.edu.sjtu.gateway.vm.util.SessionUtil;
import cn.edu.sjtu.gateway.agencymanager.util.SiteSizeChangeLog;
import cn.edu.sjtu.gateway.utils.PasswordUtil;
import cn.edu.sjtu.gateway.vm.Global;
import cn.edu.sjtu.gateway.vm.dao.SqlDAO;
import cn.edu.sjtu.gateway.vm.entity.User;
import cn.edu.sjtu.gateway.vm.entity.UserRole;
import cn.edu.sjtu.gateway.vm.util.ActionLogUtil;
import cn.edu.sjtu.gateway.utils.IpUtils;
import cn.edu.sjtu.gateway.utils.IpUtils;
import cn.edu.sjtu.gateway.vm.util.LanguageUtil;
import cn.edu.sjtu.gateway.vm.util.SafetyUtil;
import cn.edu.sjtu.gateway.vm.util.Sql;
import cn.edu.sjtu.gateway.vm.util.SystemUtil;
import cn.edu.sjtu.gateway.vm.vo.BaseVO;
import cn.edu.sjtu.gateway.tools.DateUtil;
import cn.edu.sjtu.gateway.tools.MD5Util;
import cn.edu.sjtu.gateway.tools.StringUtil;
import cn.edu.sjtu.gateway.tools.exception.NotReturnValueException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.Random;

import static cn.edu.sjtu.gateway.utils.PasswordUtil.OLD_ALGORITHM_NAME;

/**
 * <AUTHOR>
 */
@Service("transactionalService")
@Transactional
@Slf4j
public class TransactionalServiceImpl implements TransactionalService {

    private final SqlDAO sqlDAO;
    private final SiteService siteService;

    public TransactionalServiceImpl(SqlDAO sqlDAO, SiteService siteService) {
        this.sqlDAO = sqlDAO;
        this.siteService = siteService;
    }

    @Override
    public BaseVO transferSiteSizeToSubAgency(HttpServletRequest request, int targetAgencyId, int transferSiteSize) {
        BaseVO vo = new BaseVO();
        if (transferSiteSize < 1) {
            vo.setBaseVO(BaseVO.FAILURE, "请输入要充值站币的数量！");
            return vo;
        }

        Agency shiroMyAgency = cn.edu.sjtu.gateway.agencymanager.Func.getMyAgency();

        //我的代理信息
        Agency myAgency = sqlDAO.findById(Agency.class, shiroMyAgency.getId());

        if (myAgency.getSiteSize() - transferSiteSize <= 0) {
            vo.setBaseVO(BaseVO.FAILURE, "您当前只拥有" + myAgency.getSiteSize() + "站币！给下级充值金额超出，充值失败！");
            return vo;
        }

        //我的下级代理信息，要给他转账的代理信息
        Agency agency = sqlDAO.findById(Agency.class, targetAgencyId);

        if (agency.getParentId() - myAgency.getId() != 0) {
            vo.setBaseVO(BaseVO.FAILURE, "要充值的代理不是您的直属下级，无法充值");
            return vo;
        }

        //我的代理信息里，减去转账的站币
        myAgency.setSiteSize(myAgency.getSiteSize() - transferSiteSize);
        sqlDAO.save(myAgency);
        //将资金变动记录入数据库，以我（当前用户）为主
        SiteSizeChange ssc = new SiteSizeChange();
        ssc.setAddtime(DateUtil.timeForUnix10());
        ssc.setAgencyId(myAgency.getId());
        ssc.setChangeAfter(myAgency.getSiteSize());
        ssc.setChangeBefore(myAgency.getSiteSize() + transferSiteSize);
        ssc.setGoalid(agency.getId());
        ssc.setSiteSizeChange(-transferSiteSize);
        ssc.setUserid(myAgency.getUserid());
        sqlDAO.save(ssc);

        //下级代理的信息里，增加转账的站币
        agency.setSiteSize(agency.getSiteSize() + transferSiteSize);
        sqlDAO.save(agency);
        //将资金变动记录入数据库，以对方为主，这是对方的金钱变动日志，充值日志
        SiteSizeChange ssc_other = new SiteSizeChange();
        ssc_other.setAddtime(DateUtil.timeForUnix10());
        ssc_other.setAgencyId(agency.getId());
        ssc_other.setChangeAfter(agency.getSiteSize());
        ssc_other.setChangeBefore(agency.getSiteSize() - transferSiteSize);
        ssc_other.setGoalid(myAgency.getId());
        ssc_other.setSiteSizeChange(transferSiteSize);
        ssc_other.setUserid(agency.getUserid());
        sqlDAO.save(ssc_other);

        //当前我的IP地址
        String ip = IpUtils.getIpAddress(request);

        //记录我的资金消费记录
        SiteSizeChangeLog.xiaofei(myAgency.getName(), "给下级代理" + agency.getName() + "充值站币", ssc.getSiteSizeChange(), myAgency.getSiteSize() + transferSiteSize, myAgency.getSiteSize(), agency.getId(), ip);

        //记录我下线代理的资金充值记录
        User user = sqlDAO.findById(User.class, agency.getUserid());
        SiteSizeChangeLog.chongzhi(user.getId(), user.getUsername(), agency.getName(), "直属上级给充值站币", transferSiteSize, agency.getSiteSize() - transferSiteSize, agency.getSiteSize(), myAgency.getId(), ip);

        //记录操作日志
        ActionLogUtil.insertUpdateDatabase(request, agency.getId(), "给下级代理" + agency.getName() + "充值站币：" + transferSiteSize);

        //发送短信通知对方，待短信模板通过审核
        //G.aliyunSMSUtil.send(G.AliyunSMS_SignName, G.AliyunSMS_agencySiteSizeRecharge_TemplateCode, "{\"chongzhi\":\""+transferSiteSize+"\", \"username\":\""+agency.getName()+"\", \"siteSize\":\""+agency.getSiteSize()+"\"}", agency.getPhone());

        //刷新Session中我的代理信息缓存
        SessionUtil.setAgency(myAgency);

        return vo;
    }

    @Override
    public BaseVO siteXuFei(HttpServletRequest request, int siteid, int year) {
        BaseVO vo = new BaseVO();

        if (year < 1) {
            vo.setBaseVO(BaseVO.FAILURE, "请输入要续费的年数，1～10");
            return vo;
        }
        if (year > 10) {
            vo.setBaseVO(BaseVO.FAILURE, "请输入要续费的年数，1～10，最大可往后续费10年");
            return vo;
        }

        Agency shiroMyAgency = cn.edu.sjtu.gateway.agencymanager.Func.getMyAgency();

        //我的代理信息
        Agency myAgency = sqlDAO.findById(Agency.class, shiroMyAgency.getId());

        if (myAgency.getSiteSize() - year <= 0) {
            vo.setBaseVO(BaseVO.FAILURE, "您当前只拥有" + myAgency.getSiteSize() + "站币！续费花费的金额超出，续费失败！");
            return vo;
        }

        //我的下级的网站信息，要给他续费的网站信息
        Site site = sqlDAO.findById(Site.class, siteid);
        //我的下级的网站所属人的信息
        User user = sqlDAO.findById(User.class, site.getUserid());
        if (user.getReferrerid() - myAgency.getUserid() != 0) {
            vo.setBaseVO(BaseVO.FAILURE, "要续费的网站不是您的直属下级，无法续费");
            log.info("myAgency.id:" + myAgency.getId() + ",给我开通的站点续费：" + vo.getInfo());
            return vo;
        }

        //避免int溢出
        long expiretime = site.getExpiretime();
        expiretime = expiretime + (year * 31622400);
        log.info("" + expiretime);
        if (expiretime > 2147483647) {
            vo.setBaseVO(BaseVO.FAILURE, "网站往后续费最大可续费到2038年！此年份后将开启全新建站时代。");
            return vo;
        }

        //判断续费后的网站是否超过了10年 ,当前时间 ＋ 3660天
        if (expiretime > (DateUtil.timeForUnix10() + 316224000)) {
            vo.setBaseVO(BaseVO.FAILURE, "网站往后续费最大为10年！当前网站已经延期很多年了，不需要在进行延期了");
            return vo;
        }


        //当前我的IP地址
        String ip = IpUtils.getIpAddress(request);

        /**** 进行数据保存 ****/

        //我的代理信息里，减去续费花费的站币
        myAgency.setSiteSize(myAgency.getSiteSize() - year);
        sqlDAO.save(myAgency);
        //数据库保存我的消费记录
        SiteSizeChange ssc = new SiteSizeChange();
        ssc.setAddtime(DateUtil.timeForUnix10());
        ssc.setAgencyId(myAgency.getId());
        ssc.setChangeAfter(myAgency.getSiteSize());
        ssc.setChangeBefore(myAgency.getSiteSize() + year);
        ssc.setGoalid(site.getId());
        ssc.setSiteSizeChange(0 - year);
        ssc.setUserid(myAgency.getUserid());
        sqlDAO.save(ssc);
        //记录我的资金消费记录
        SiteSizeChangeLog.xiaofei(myAgency.getName(), "给网站" + site.getName() + "续费" + year + "年", year, myAgency.getSiteSize() + year, myAgency.getSiteSize(), site.getId(), ip);

        //网站增加过期时间
        site.setExpiretime(site.getExpiretime() + (year * 31622400));
        sqlDAO.save(site);

        //到期时间
        String daoqishijian = "";
        try {
            daoqishijian = DateUtil.dateFormat(site.getExpiretime(), "yyyy年MM月dd日");
        } catch (NotReturnValueException e) {
            log.error("错误信息：------>" + e);
        }

        //记录操作日志
        log.info("给网站" + site.getName() + "续费" + year + "年。网站续费后，日期到" + daoqishijian);

        //发送短信通知对方
//		G.aliyunSMSUtil.send(G.AliyunSMS_SignName, G.AliyunSMS_siteYanQi_templateCode, "{\"siteName\":\""+site.getName()+"\", \"year\":\""+year+"\", \"time\":\""+daoqishijian+"\"}", site.getPhone());

        //刷新Session中我的代理信息缓存
        SessionUtil.setAgency(myAgency);
        return vo;
    }

    @Override
    public BaseVO agencyCreateSite(HttpServletRequest request, Agency agency,
                                   User user, Site site, String email) {
        BaseVO vo = new BaseVO();

        if (agency.getSiteSize() == 0) {
            vo.setBaseVO(BaseVO.FAILURE, "您的账户余额还剩 " + agency.getSiteSize() + " 站，不足以再开通网站！请联系相关人员充值");
            return vo;
        }

        if (site.getClient() == 0) {
            vo.setBaseVO(BaseVO.FAILURE, "请选择站点类型，是电脑网站呢，还是手机网站呢？");
            return vo;
        }
        if (site.getName().isEmpty() || site.getName().length() > 30) {
            vo.setBaseVO(BaseVO.FAILURE, "请输入1～30个字符的要建立的站点名字");
            return vo;
        }

        //创建用户
        user.setPhone(StringUtil.filterXss(Sql.filter(site.getPhone())));
        user.setEmail(StringUtil.filterXss(Sql.filter((email))));
        user.setReferrerid(agency.getUserid());    //设定用户的上级是当前代理商本人
        UserVO userVO = regUser(user, request, false);
        if (userVO.getResult() == BaseVO.SUCCESS) {

            //创建站点
            site.setExpiretime(DateUtil.timeForUnix10() + 31622400);    //到期，一年后，366天后

            SiteVO siteVO = siteService.saveSite(site, userVO.getUser().getId(), request);
            if (siteVO.getResult() - SiteVO.SUCCESS == 0) {

                //减去当前代理的账户余额的站币
                agency.setSiteSize(agency.getSiteSize() - 1);
                sqlDAO.save(agency);
                //将变动记录入数据库
                SiteSizeChange ssc = new SiteSizeChange();
                ssc.setAddtime(DateUtil.timeForUnix10());
                ssc.setAgencyId(agency.getId());
                ssc.setChangeAfter(agency.getSiteSize());
                ssc.setChangeBefore(agency.getSiteSize() + 1);
                ssc.setGoalid(siteVO.getSite().getId());
                ssc.setSiteSizeChange(-1);
                ssc.setUserid(agency.getUserid());
                sqlDAO.save(ssc);

                //记录动作日志
                log.info("开通网站：" + site.getName());

                vo.setInfo(userVO.getUser().getId() + "_" + passwordMD5(userVO.getUser().getPassword()));
            } else {
                vo.setBaseVO(BaseVO.FAILURE, "添加用户成功，但添加站点失败！");
            }
        } else {
            vo.setBaseVO(BaseVO.FAILURE, userVO.getInfo());
        }

        return vo;
    }

    @Override
    public UserVO regUser(User user, HttpServletRequest request,
                          boolean isAgency) {
        UserVO baseVO = new UserVO();
        if (!StringUtil.isEnglishAndNumber(user.getUsername())) {
            baseVO.setBaseVO(BaseVO.FAILURE, "用户名只允许输入英文跟数字");
            return baseVO;
        }

        user.setUsername(StringUtil.filterXss(user.getUsername()));
        user.setEmail(SafetyUtil.filter(user.getEmail()));
        user.setPhone(SafetyUtil.filter(user.getPhone()));

        //判断用户名、邮箱、手机号是否有其中为空的
        if (user.getUsername() == null || "".equals(user.getUsername())) {
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_userNameToLong"));
        }

        //判断用户名、邮箱、手机号是否有其中已经注册了，唯一性
        //判断用户名唯一性

        if (!sqlDAO.findByProperty(User.class, "username", user.getUsername()).isEmpty()) {
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_regFailureForUsernameAlreadyExist"));
            return baseVO;
        }

        //判断邮箱是否被注册了，若被注册了，则邮箱设置为空
        if (!sqlDAO.findByProperty(User.class, "email", user.getEmail()).isEmpty()) {
            user.setEmail("");
        }

        //判断手机号是否被用过。若被用过了，则自动将手机号给抹除，不写入User表
        if (user.getPhone() != null && !user.getPhone().isEmpty()) {
            if (!sqlDAO.findByProperty(User.class, "phone", user.getPhone()).isEmpty()) {
                if (isAgency) {
                    //如果是创建代理，手机号必须的，并且唯一
                    baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_regFailureForPhoneAlreadyExist"));
                    return baseVO;
                } else {
                    //如果只是建站，则可以允许手机号为空
                    user.setPhone("");
                }
            }
        }

        user.setRegip(IpUtils.getIpAddress(request));
        user.setLastip(IpUtils.getIpAddress(request));
        user.setRegtime(DateUtil.timeForUnix10());
        user.setLasttime(DateUtil.timeForUnix10());
        user.setNickname(user.getUsername());
        user.setAuthority(isAgency ? SystemUtil.get("AGENCY_ROLE") + "" : SystemUtil.get("USER_REG_ROLE"));    //设定是普通代理，还是会员权限
        user.setCurrency(0);
        user.setFreezemoney(0);
        user.setMoney(0);
        user.setIsfreeze(User.ISFREEZE_NORMAL);
        user.setHead("default.png");

        Random random = new Random();
        user.setSalt(random.nextInt(10) + "" + random.nextInt(10) + random.nextInt(10) + random.nextInt(10));
        String md5Password = PasswordUtil.hashPassword(user.getPassword(), user.getSalt(),OLD_ALGORITHM_NAME, Global.USER_PASSWORD_SALT_NUMBER);
        user.setPassword(md5Password);
        sqlDAO.save(user);
        if (user.getId() > 0) {
            //赋予该用户系统设置的默认角色，是代理，还是会员
            UserRole userRole = new UserRole();
            int roleid = 0;
            if (isAgency) {
                roleid = SystemUtil.getInt("AGENCY_ROLE");
            } else {
                roleid = SystemUtil.getInt("USER_REG_ROLE");
            }
            userRole.setRoleid(roleid);
            userRole.setUserid(user.getId());
            sqlDAO.save(userRole);

            baseVO.setBaseVO(BaseVO.SUCCESS, LanguageUtil.show("user_regSuccess"));
            baseVO.setUser(user);
        } else {
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_regFailure"));
        }

        return baseVO;
    }

    /**
     * 将32位的 user.password 密码进行再加密，生成128位加密字符串
     *
     * @param password user.password 密码
     * @return 新生成的128位加密字符串
     */
    private static String passwordMD5(String password) {
        return MD5Util.MD5(password);
    }

}
