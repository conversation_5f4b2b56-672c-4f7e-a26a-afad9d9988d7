package cn.edu.sjtu.gateway.agencymanager.controller;

import cn.edu.sjtu.gateway.agencymanager.entity.Agency;
import cn.edu.sjtu.gateway.agencymanager.entity.AgencyData;
import cn.edu.sjtu.gateway.agencymanager.pluginManage.manage.AgencyManagerIndexPluginManage;
import cn.edu.sjtu.gateway.vm.entity.User;
import cn.edu.sjtu.gateway.vm.pluginManage.PluginManage;
import cn.edu.sjtu.gateway.vm.pluginManage.PluginRegister;
import cn.edu.sjtu.gateway.vm.service.SqlService;
import cn.edu.sjtu.gateway.vm.util.ActionLogUtil;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.InvocationTargetException;
import java.util.Map;

/**
 * 代理后台管理首页
 * <AUTHOR>
 */
@Controller(value="AgencyManagerIndexController")
@RequestMapping("/agency")
@Slf4j
public class ManagerIndexController extends BaseController{
	private final SqlService sqlService;

	public ManagerIndexController(SqlService sqlService) {
		this.sqlService = sqlService;
	}

	/**
	 * 代理后台首页
	 * @param jumpUrl 这里在应用插件里面，安装插件后要刷新页面，所以加入了jumpUrl，传入加载地址，如果有jumpUrl，那么默认页面就是访问这个。这里传入的地址，如 plugin/pluginManage/index.naii 
	 */
	@RequestMapping("index${url.suffix}")
	public String index(HttpServletRequest request, Model model,
			@RequestParam(value = "jumpUrl", required = false , defaultValue="") String jumpUrl){
		log.info("进入代理后台首页");
		StringBuilder pluginMenu = new StringBuilder();
		if(!PluginManage.agencyClassManage.isEmpty()){
			for (Map.Entry<String, PluginRegister> entry : PluginManage.agencyClassManage.entrySet()) {
				PluginRegister plugin = entry.getValue();
				pluginMenu.append("<dd><a id=\"").append(entry.getKey()).append("\" class=\"subMenuItem\" href=\"javascript:loadUrl('").append(plugin.menuHref()).append("'), notUseTopTools();\">").append(plugin.menuTitle()).append("</a></dd>");
			}
		}
		model.addAttribute("pluginMenu", pluginMenu.toString());

        /* 针对html追加的插件 ****/
		try {
			String pluginAppendHtml = AgencyManagerIndexPluginManage.manage();
			model.addAttribute("pluginAppendHtml", pluginAppendHtml);
		} catch (InstantiationException | IllegalAccessException
				| NoSuchMethodException | SecurityException
				| IllegalArgumentException | InvocationTargetException e) {
            log.error("错误信息：------>{}","error", e);
		}
		
		User user = getUser();
		model.addAttribute("user", user);
		
		return "/agency/index";
	}
	
	/**
	 * 代理商后台欢迎页面
	 */
	@RequestMapping("welcome${url.suffix}")
	public String welcome(HttpServletRequest request, Model model){
		Agency agency = getMyAgency();
		if(agency == null){
			return error(model, "代理信息出错！");
		}
		//上级代理的变长表数据
		AgencyData parentAgencyData = getParentAgencyData();
		
		ActionLogUtil.insert(request, agency.getId(), "进入代理商后台首页");
		User user = sqlService.findById(User.class, getUserId());
		
		model.addAttribute("user", user);
		model.addAttribute("agency", agency);
		model.addAttribute("parentAgency", getParentAgency());	//上级代理
		//上级代理的公告内容，要显示出来的
		model.addAttribute("parentAgencyNotice", parentAgencyData == null ? "":parentAgencyData.getNotice());
		return "agency/welcome";
	}
	
}
