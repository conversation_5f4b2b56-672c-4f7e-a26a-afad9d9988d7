package cn.edu.sjtu.gateway.core;

import org.apache.commons.configuration2.Configuration;
import org.apache.commons.configuration2.PropertiesConfiguration;
import org.apache.commons.configuration2.XMLConfiguration;
import org.apache.commons.configuration2.builder.ReloadingFileBasedConfigurationBuilder;
import org.apache.commons.configuration2.builder.fluent.Parameters;
import org.apache.commons.configuration2.io.FileHandler;
import org.apache.commons.configuration2.reloading.PeriodicReloadingTrigger;
import org.apache.commons.configuration2.reloading.ReloadingController;

import java.io.StringWriter;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 读取xml配置文件的值
 * <br/><b/>需</b>
 * <br/><i>commons-configuration-1.7.jar</i>
 * <br/><i>commons-collections-3.2.1.jar</i>
 * <br/><i>commons-io-1.3.2.jar</i>
 * <br/><i>commons-lang-2.5.jar</i>
 * <br/><i>commons-logging-1.2.jar</i>
 *
 * <AUTHOR>
 */
public class ConfigManagerUtil {
    private static final HashMap<String, ConfigManagerUtil> hashMap = new HashMap<>();
    private Configuration config;
    private ReloadingFileBasedConfigurationBuilder<?> builder;

    private ConfigManagerUtil(String configFileName) {
        try {
            Parameters params = new Parameters();
            if (configFileName.toLowerCase().endsWith("xml")) {
                builder = new ReloadingFileBasedConfigurationBuilder<>(XMLConfiguration.class)
                        .configure(params.xml().setFileName(configFileName));
            } else if (configFileName.toLowerCase().endsWith("properties")) {
                builder = new ReloadingFileBasedConfigurationBuilder<>(PropertiesConfiguration.class)
                        .configure(params.properties().setFileName(configFileName));
            }

            // 获取 ReloadingController
            ReloadingController reloadingController = null;
            if (builder != null) {
                reloadingController = builder.getReloadingController();
            }

            // 定期触发重新加载
            PeriodicReloadingTrigger trigger = null;
            if (reloadingController != null) {
                trigger = new PeriodicReloadingTrigger(
                        reloadingController,
                        null,
                        1, TimeUnit.SECONDS);
            }
            if (trigger != null) {
                trigger.start();
            }
            // 获取配置对象
            if (builder != null) {
                config = builder.getConfiguration();
            }
            hashMap.put(configFileName, this);
        } catch (Exception e) {
            System.err.println("错误信息：" + e);
        }
    }

    public synchronized static ConfigManagerUtil getSingleton(String configFileName) {
        if (hashMap.get(configFileName) == null) {
            hashMap.put(configFileName, new ConfigManagerUtil(configFileName));
        }
        return hashMap.get(configFileName);
    }

    // 获取配置对象
    public Configuration getFileConfiguration() {
        return this.config;
    }

    // 获取某个键的值
    public String getValue(String path) {
        return this.config.getString(path);
    }

    // 获取配置文件的键值对列表
    public Iterator<String> getKeys() {
        return this.config.getKeys();
    }

    public List<Object> getList(String name) {
        return config.getList(name);
    }

    public void setValue(String path, String value) {
        config.setProperty(path, value);
    }

    public String[] selectValues(String path) {
        return config.getStringArray(path);
    }

    public void save() {
        try {
            FileHandler handler = builder.getFileHandler();
            handler.save();
        } catch (Exception e) {
            System.err.println("保存配置文件失败：" + e);
        }
    }

    public String toFileContent() {
        try (StringWriter stringWriter = new StringWriter()) {
            FileHandler handler = builder.getFileHandler();
            handler.save(stringWriter);
            return stringWriter.toString();
        } catch (Exception e) {
            System.err.println("错误信息：" + e);
            return null;
        }
    }

    public String getFilePath() {
        return Paths.get(builder.getFileHandler().getFileName()).toAbsolutePath().toString();
    }

    public static void main(String[] args) {
        ConfigManagerUtil singleton = ConfigManagerUtil.getSingleton("systemConfig.xml");
        // 获取所有keys
        Iterator<String> englishIt = singleton.getFileConfiguration().getKeys();
        Map<String, Map<String, String>> languageMaps = new HashMap<>();

        while (englishIt.hasNext()) {
            String key = englishIt.next();

            // 只处理包含 '.' 的 key
            if (key.contains(".")) {
                String[] parts = key.split("\\.");
                String prefix = parts[0];

                // 为每个前缀创建一个语言map
                languageMaps.putIfAbsent(prefix, new HashMap<>());

                // 获取该前缀对应的value
                String value = singleton.getValue(key);
                languageMaps.get(prefix).put(key.replace(prefix + ".", ""), value);
            }
        }

// 打印结果
        for (Map.Entry<String, Map<String, String>> entry : languageMaps.entrySet()) {
            System.out.println("Language map for prefix " + entry.getKey() + ": " + entry.getValue());
        }
    }
}
