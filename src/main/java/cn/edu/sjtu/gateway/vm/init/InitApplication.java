package cn.edu.sjtu.gateway.vm.init;

import cn.edu.sjtu.gateway.core.ConfigManagerUtil;
import cn.edu.sjtu.gateway.utils.IpUtils;
import cn.edu.sjtu.gateway.vm.Global;
import cn.edu.sjtu.gateway.vm.generateCache.Role;
import cn.edu.sjtu.gateway.vm.generateCache.User;
import cn.edu.sjtu.gateway.vm.service.impl.SqlServiceImpl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 系统启动初始化
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class InitApplication implements CommandLineRunner {
    private final SqlServiceImpl sqlService;
    private final Environment environment;

    public String getProtocol() {
        String protocol = environment.getProperty("server.ssl.key-store");
        return protocol != null ? "https://" : "http://";
    }

    public int getPort() {
        String protocol = environment.getProperty("server.port");
        return protocol != null ? Integer.parseInt(protocol) : 0;
    }

    public InitApplication(SqlServiceImpl sqlService, Environment environment) {
        this.sqlService = sqlService;
        this.environment = environment;
    }

    @Override
    public void run(String... args) throws Exception {
        new Thread(() -> {
            execData();
            printStr();
        }).start();
    }

    private void printStr() {
        int port = getPort();
        String protocol = getProtocol();
        String str = "\n"
                + "***************************\n"
                + "上海交通大学宁波人工智能研究院建站系统已开启完毕！\n"
                + "***************************\n"
                + "  请直接访问：" + protocol + IpUtils.getIpaddress() + ":" + port + "/login.naii\n"
                + "  官网： https://naii.sjtu.edu.cn\n"
                + "***************************\n";
        log.info(str);
    }

    public void execData() {
        log.debug("-----------------------------> 项目启动后开启自动初始化缓存数据加载 <-----------------------------");
        //加在systemConfig.xml中自动检测的项
        try {
            //数据库自动检测
            checkDb();
        } catch (org.springframework.beans.factory.NoSuchBeanDefinitionException e) {
            //未使用数据库，此项忽略
            log.info("检测到spring中没有sqlService这个bean，也就是当前项目未使用数据库！数据库自动检测略过");
        }

        try {
            new User();
        } catch (Throwable ignored) {
            log.error("初始化缓存数据时，发现spring没有注入User类，请检查是否使用了User类，如果没有，请忽略本条信息");
        }
    }

    /**
     * 项目自动运行后，检测数据库是否导入 iw.sql
     */
    public void checkDb() {
        //判断一下，当 system 表中有数据时，才会加载postClass、role、system等数据库信息。反之，如果system表没有数据，也就是认为开发者刚吧iw框架假设起来，还没有往里填充数据，既然没有数据，便不需要加载这几个数据表的数据了
        boolean useDB = false;
        //使用Mysql
        log.info("Using the database : Mysql");
        List<Map<String, Object>> map = sqlService.findMapBySqlQuery("SHOW TABLES LIKE '%system%'");
        if (!map.isEmpty()) {
            useDB = true;
        }

        //如果使用数据库，则加载初始化的一些数据
        if (useDB) {
            readSystemTable();
            try {
                new Role().role(sqlService);
            } catch (Throwable e) {
                log.debug("权限系统异常: " + e + "，如果您当前项目使用不到权限编辑操作，此项忽略即可");
            }
        } else {
            Global.databaseCreateFinish = false;
            Global.databaseCreateFinish_explain = "数据库异常：请将数据库中的初始数据导入，数据文件地址  ";
            log.debug(Global.databaseCreateFinish_explain);
        }
    }

    /**
     * 读system表数据
     */
    public void readSystemTable() {
        Global.system.clear();
        log.debug("------------------------------------>开始装载System数据表信息<------------------------------------");
        List<Map<String, Object>> list = sqlService.findMapBySqlQuery("SELECT name,value FROM `system`");
        for (Map<String, Object> map : list) {
            String name = map.get("name").toString();
            String value = map.get("value") != null ? map.get("value").toString() : "";
            Global.system.put(name, value);
            log.debug("装载 name: {} value: {}完成！", name, value);
        }
        log.info("system 表数据载入内存完毕，共" + list.size() + "条数据");
    }

}
