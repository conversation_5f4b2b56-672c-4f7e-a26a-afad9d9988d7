package cn.edu.sjtu.gateway.vm.controller.manager;

import cn.edu.sjtu.gateway.vm.controller.BaseController;

import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;

/**
 * 日志管理，使用的是阿里云日志服务
 *
 * <AUTHOR>
 */
@Controller(value = "WMLogManagerController")
@RequestMapping("/manager/log")
@Slf4j
public class LogManagerController extends BaseController {
    /**
     * 日志列表
     */
    @RequiresPermissions("managerLogList")
    @RequestMapping("list${url.suffix}")
    public String list(HttpServletRequest request, Model model,
                       @RequestParam(value = "queryString", defaultValue = "") String queryString) {
        return error(model, "您未开启日志服务！无法查看操作日志");
    }

}
