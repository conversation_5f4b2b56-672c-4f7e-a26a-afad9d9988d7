package cn.edu.sjtu.gateway.vm.util;

import cn.edu.sjtu.gateway.agencymanager.entity.Agency;
import cn.edu.sjtu.gateway.agencymanager.entity.AgencyData;
import cn.edu.sjtu.gateway.manager.entity.InputModel;
import cn.edu.sjtu.gateway.manager.entity.Site;
import cn.edu.sjtu.gateway.manager.entity.SiteColumn;
import cn.edu.sjtu.gateway.manager.entity.SiteUser;
import cn.edu.sjtu.gateway.manager.vo.TemplateVarVO;
import cn.edu.sjtu.gateway.vm.bean.ActiveUser;
import cn.edu.sjtu.gateway.vm.bean.PermissionMark;
import cn.edu.sjtu.gateway.vm.bean.PermissionTree;
import cn.edu.sjtu.gateway.vm.entity.Permission;
import cn.edu.sjtu.gateway.vm.entity.User;
import cn.edu.sjtu.gateway.vm.shiro.ShiroFunc;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.ExcessiveAttemptsException;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.apache.shiro.authc.LockedAccountException;
import org.apache.shiro.authc.UnknownAccountException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.session.Session;
import org.apache.shiro.session.mgt.DefaultSessionKey;
import org.apache.shiro.session.mgt.SessionKey;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.subject.support.DefaultSubjectContext;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.apache.shiro.web.session.mgt.DefaultWebSessionManager;
import org.springframework.beans.BeanUtils;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * session 操作
 *
 * <AUTHOR>
 */
@Slf4j
public class SessionUtil extends ShiroFunc {

    //是否允许百度UEDITOR上传图片、文件。 true允许，false不允许
    public static final String PLUGIN_NAME_UEDITOR_ALLOW_UPLOAD = "system:ueditor:allowUploadForUEditor";
    //UEditor上传文件相关的参数，可用{uploadParam1}来调用。每个人的上传参数都会不同
    public static final String PLUGIN_NAME_UEDITOR_UPLOAD_PARAM_1 = "system:ueditor:ueUploadParam1";
    //当前使用哪个语言包
    public static final String PLUGIN_NAME_LANGUAGE_PACKAGE_NAME = "system:languagePackageName";

    /**
     * 从Shrio的Session中获取当前用户的缓存信息
     */
//	public static UserBean getUserBeanForSession(){
//		return getUserBeanForShiroSession();
//	}
    //如果使用redis，那么将每个用户的 activeUser.getPluginMap() 作为value存入到redis的key，如 plugin_map_userid_1
    public static final String REDIS_PLUGIN_MAP_NAME = "cn.edu.sjtu.gateway.vm.bean.ActiveUser.plugin.userid";
    public static final int REDIS_PLUGIN_MAP_EXPIRETIME = 2 * 60 * 60 * 24;    //pluginMap 在redis中存在的有效期是2天

    /**
     * 获取当前用户是否已经登录
     *
     * @return true:已登录；  false:未登录
     */
    public static boolean isLogin() {
        Subject subject = SecurityUtils.getSubject();
        //取身份信息
        return subject.getPrincipal() != null;
    }

    /**
     * 从Shrio的Session中获取当前用户的缓存信息
     */
    public static ActiveUser getActiveUser() {
        if (SecurityUtils.getSubject() == null) {
            return null;
        } else {
            Subject subject = SecurityUtils.getSubject();
            //取身份信息
            ActiveUser activeUser = null;
            Object obj = subject.getPrincipal();
            if (obj == null) {
                return null;
            }
            if (SystemUtil.isJarRun()) {
                //jar包方式运行，可能是开发环境
                if (obj instanceof ActiveUser) {
                    activeUser = (ActiveUser) obj;
                } else {
//					Gson gson = new Gson();
                    activeUser = new ActiveUser();
                    BeanUtils.copyProperties(obj, activeUser);
                    log.debug("fuzhi -- " + activeUser.toString());
//					activeUser = gson.fromJson(gson.toJson(obj), ActiveUser.class);
                }
            } else {
                //部署到服务器了
                activeUser = (ActiveUser) obj;
            }

            return activeUser;
        }
    }

    /**
     * 从Shrio的Session中，获取指定SessionId的用户的缓存信息
     *
     * @param token 也就是SessionId
     * @return 获取到的 {@link cn.edu.sjtu.gateway.vm.bean.ActiveUser} 如果当前tokne没有对应的信息，也就是未登录、或者假token，那么返回null
     */
    public static ActiveUser getActiveUser(String token) {

        //不使用redis，那么就走shiro管理的从shiro中取session对应的用户
        SessionKey sessionKey = new DefaultSessionKey(token);
        org.apache.shiro.mgt.SecurityManager manager = SecurityUtils.getSecurityManager();
//			DefaultSecurityManager manager = (DefaultSecurityManager) SecurityUtils.getSecurityManager();

        Session se;
        try {
            se = manager.getSession(sessionKey);
        } catch (org.apache.shiro.session.UnknownSessionException e) {
            return null;
        }
        if (se == null) {
            //未登录，没有用户信息
            return null;
        }

        Object obj = se.getAttribute(DefaultSubjectContext.PRINCIPALS_SESSION_KEY);
        if (obj == null) {
            return null;
        }
        SimplePrincipalCollection coll = (SimplePrincipalCollection) obj;
        Object p = coll.getPrimaryPrincipal();
        if (p != null) {
            return (ActiveUser) p;
        }

        return null;
    }

    public static void main(String[] args) {
        log.info("" + getActiveUser("s"));
    }


    /**
     * 获取用户Session中缓存的某个插件信息。这里的信息，可以用 {@link #setPlugin(String, Object)} 来设置写入
     * 这里是从 {@link cn.edu.sjtu.gateway.vm.bean.ActiveUser#getPluginMap()} 中取的
     * 可以从session中获取该用户某个插件的缓存信息，避免频繁查数据库。
     * 前提是已经将该用户的插件的信息缓存进去了
     *
     * @param activeUser 当前登录的 activeUser 信息
     * @param key        整个网市场云建站系统中，pluginMap.key,唯一的，具体插件可以为 kefu、cnzz， 网站管理后台缓存网站对象的key可以为 wangmarket_site
     * @return 如果获取到，返回。如果获取不到，如用户未登录、插件信息不存在，则返回null
     */
    public static Object getPluginObject(ActiveUser activeUser, String key) {
        if (activeUser == null) {
            return null;
        }

        Object obj = null;
//		User user = activeUser.getUser();
//		if (user == null) {
//			//未登陆
//			return null;
//		}
        //判断是从redis中取，还是从本地服务器中取
        if (cn.edu.sjtu.gateway.vm.util.CacheUtil.isUseRedis()) {

            User user = activeUser.getUser();
            if (user == null) {
                //未登陆,这个有时候热部署在遇到springmvc拦截器时会遇到异常
                return null;
            }

            Object redisObj = CacheUtil.get(REDIS_PLUGIN_MAP_NAME + ":" + user.getId());
            if (redisObj == null) {
                return null;
            }
            Map<String, Object> map = (HashMap<String, Object>) redisObj;
            obj = map.get(key);
        } else {
            obj = activeUser.getPluginMap().get(key);
        }

        if (obj == null) {
            return null;
        }
        return obj;
    }

    /**
     * 获取用户Session中缓存的某个插件信息。这里的信息，可以用 {@link #setPlugin(String, Object)} 来设置写入
     * 这里是从 {@link cn.edu.sjtu.gateway.vm.bean.ActiveUser#getPluginMap()} 中取的
     * 可以从session中获取该用户某个插件的缓存信息，避免频繁查数据库。
     * 前提是已经将该用户的插件的信息缓存进去了
     *
     * @param key 整个网市场云建站系统中，pluginMap.key,唯一的，具体插件可以为 kefu、cnzz， 网站管理后台缓存网站对象的key可以为 wangmarket_site
     * @return 如果获取到，返回。如果获取不到，如用户未登录、插件信息不存在，则返回null
     */
    public static <T> T getPlugin(String key) {
        ActiveUser activeUser = getActiveUser();
        Object obj = getPluginObject(activeUser, key);
        if (obj == null) {
            return null;
        }
        return (T) obj;
    }

    /**
     * 获取用户Session中缓存的某个插件信息。这里的信息，可以用 {@link #setPlugin(String, Object)} 来设置写入
     * 这里是从 {@link cn.edu.sjtu.gateway.vm.bean.ActiveUser#getPluginMap()} 中取的
     * 可以从session中获取该用户某个插件的缓存信息，避免频繁查数据库。
     * 前提是已经将该用户的插件的信息缓存进去了
     *
     * @param key 整个网市场云建站系统中，pluginMap.key,唯一的，具体插件可以为 kefu、cnzz， 网站管理后台缓存网站对象的key可以为 wangmarket_site
     * @return 如果获取到，返回。如果获取不到，如用户未登录、插件信息不存在，则返回null
     */
    public static Object getPluginObject(String key) {
        ActiveUser activeUser = getActiveUser();
        return getPluginObject(activeUser, key);
    }


    /**
     * 设置用户Session中缓存的某个插件信息。这里设置的，可以用 {@link #getPlugin(String)} 取出来
     *
     * @param activeUser 要设置的已登录用户的 {@link cn.edu.sjtu.gateway.vm.bean.ActiveUser}
     * @param key        唯一key
     * @param value      存储的信息对象
     */
    public static void setPlugin(ActiveUser activeUser, String key, Object value) {
        if (activeUser == null) {
            return;
        }
        User user = activeUser.getUser();
        if (user == null) {
            //未登陆
            return;
        }

            activeUser.getPluginMap().put(key, value);
    }


    /**
     * 设置用户Session中缓存的某个插件信息。这里设置的，可以用 {@link #getPlugin(String)} 取出来
     *
     * @param key   唯一key
     * @param value 存储的信息对象
     */
    public static void setPlugin(String key, Object value) {
        ActiveUser activeUser = getActiveUser();
        setPlugin(activeUser, key, value);
    }

    /**
     * 是否允许用户通过富文本编辑器(UEditor)上传文件、图片
     *
     * @return true：允许上传；  false：不允许上传。 如果之前没设置过，默认是能正常上传的。
     */
    public static boolean isAllowUploadForUEditor() {
        Object obj = getPluginObject(PLUGIN_NAME_UEDITOR_ALLOW_UPLOAD);
        if (obj == null) {
            return true;
        }
        return (boolean) obj;
    }

    /**
     * 是否允许用户通过富文本编辑器(UEditor)上传文件、图片
     *
     * @param isAllow true：允许上传；  false：不允许上传。
     */
    public static void setAllowUploadForUEditor(boolean isAllow) {
        setPlugin(PLUGIN_NAME_UEDITOR_ALLOW_UPLOAD, isAllow);
    }

    /**
     * 获取UEditor上传文件相关的参数，可用{uploadParam1}来调用。每个人的上传参数都会不同
     *
     * @return 字符串 如果之前没设置过，则默认获取到 “defaultparam1”
     */
    public static String getUeUploadParam1() {
        String param = getPlugin(PLUGIN_NAME_UEDITOR_UPLOAD_PARAM_1);
        if (param == null) {
            return "defaultparam1";
        }
        return param;
    }

    /**
     * 设置UEditor上传文件相关的参数，可用{uploadParam1}来调用。每个人的上传参数都会不同
     *
     * @param param 字符串,路径，也就是文件夹名字
     */
    public static void setUeUploadParam1(String param) {
        setPlugin(PLUGIN_NAME_UEDITOR_UPLOAD_PARAM_1, param);
    }

    /**
     * 获取当前使用的是哪个语言包
     *
     * @return 语言包的名字
     */
    public static String getLanguagePackageName() {
        return getPlugin(PLUGIN_NAME_LANGUAGE_PACKAGE_NAME);
    }

    /**
     * 设置当前使用的是哪个语言包
     *
     * @param name 语言包的名字
     */
    public static void setLanguagePackageName(String name) {
        setPlugin(PLUGIN_NAME_LANGUAGE_PACKAGE_NAME, name);
    }

    /**
     * 推出登录
     */
    public static void logout() {
        Subject subject = SecurityUtils.getSubject();
        if (subject.isAuthenticated()) {
            int userid = getUserId();
            if (userid > 0) {
//				RedisUtil.delkeyObject(key);
            }
            subject.logout(); // session 会销毁，在SessionListener监听session销毁，清理权限缓存
        }
    }


    /**
     * Session中更新当前登录用户的信息。
     * 如果当前用户已登陆，则更新；
     * 如果当前用户未登陆，则自动将传入的 user.username 作为登陆用户。（需要确认 username 在 user 表中是存在的，不然登陆不成功）
     */
    public static void setUser(User user) {
        ActiveUser activeUser = SessionUtil.getActiveUser();
        if (activeUser == null) {
            UsernamePasswordToken token = new UsernamePasswordToken(user.getUsername(), user.getUsername());
            token.setRememberMe(false);
            Subject currentUser = SecurityUtils.getSubject();
            try {
                currentUser.login(token);
            } catch (UnknownAccountException uae) {
                log.error("错误信息：------>" + uae);
            } catch (IncorrectCredentialsException ice) {
                log.error("错误信息：------>" + ice);
            } catch (LockedAccountException lae) {
                log.error("错误信息：------>" + lae);
            } catch (ExcessiveAttemptsException eae) {
                log.error("错误信息：------>" + eae);
            } catch (org.apache.shiro.authc.AuthenticationException ae) {
                log.error("错误信息：------>" + ae);
            }

            activeUser = SessionUtil.getActiveUser();
            activeUser.setId(user.getId());
            activeUser.setUser(user);
        } else {
            activeUser.setUser(user);
        }

    }

    /**
     * 清理掉当前登录用户的session，也就是根据某个用户id，删除这个用户的session，让这个用户下线
     *
     * @param userid 对应User.id 要删除哪个用户的登录状态，就传入哪个用户的id
     */
    public static void removeSession(int userid) {
        //获取shiro的sessionManager
        DefaultWebSecurityManager securityManager = (DefaultWebSecurityManager) SecurityUtils.getSecurityManager();
        DefaultWebSessionManager sessionManager = (DefaultWebSessionManager) securityManager.getSessionManager();
        // 4.获取所有已登录用户的session列表
        Collection<Session> sessions = sessionManager.getSessionDAO().getActiveSessions();

        if (!sessions.isEmpty()) {
            for (Session onlineSession : sessions) {
                // 清除当前用户以前登录时保存的session会话

                SimplePrincipalCollection principalCollection = (SimplePrincipalCollection) onlineSession.getAttribute(DefaultSubjectContext.PRINCIPALS_SESSION_KEY);
                if (principalCollection == null) {
                    continue;
                }
                Object obj = principalCollection.getPrimaryPrincipal();
                if (obj == null) {
                    continue;
                }
                ActiveUser activeUser = (ActiveUser) obj;
                User user = activeUser.getUser();
                if (user == null) {
                    continue;
                }
                if (user.getId() - userid == 0) {
                    sessionManager.getSessionDAO().delete(onlineSession);
                }
            }
        }
    }


    /**
     * 获取当前登录用户所显示的shiro权限的菜单的HTML
     *
     * @return
     */
    public static String getUserMenuHTML() {
        StringBuilder sb = new StringBuilder();
        ActiveUser activeUser = SessionUtil.getActiveUser();
        if (activeUser == null) {
            return "";
        }
        List<PermissionTree> list = activeUser.getPermissionTreeList();
        for (PermissionTree ptree : list) {
            Permission firstPermission = ptree.getPermissionMark().getPermission();
            if (firstPermission.getMenu() != null && firstPermission.getMenu() - 1 == 0) {
                //是菜单，显示

                //判断是否有下级
                StringBuffer subSB = new StringBuffer();
                if (ptree.getList() != null) {
                    List<PermissionMark> subList = ptree.getList();

                    subSB.append("<dl class=\"layui-nav-child\">");
                    boolean haveSubMenu = false;    //是否有子菜单
                    for (PermissionMark permissionMark : subList) {
                        Permission subPermission = permissionMark.getPermission();
                        if (subPermission.getMenu() != null && subPermission.getMenu() - 1 == 0) {
                            //是菜单,那么也加入进去
                            subSB.append("<dd id=\"" + subPermission.getId() + "\" class=\"twoMenu\"><a id=\"" + subPermission.getId() + "_a\" class=\"subMenuItem wm_menu\" href=\"" + subPermission.getUrl() + "\">" + subPermission.getName() + "</a></dd>");
                            haveSubMenu = true;
                        }
                    }
                    subSB.append("</dl>");

                    sb.append("<li class=\"layui-nav-item\" id=\"" + firstPermission.getId() + "\"><a id=\"" + firstPermission.getId() + "_a\" href=\"" + (haveSubMenu ? "javascript:;" : firstPermission.getUrl()) + "\" class=\"wm_menu\"><i class=\"layui-icon firstMenuIcon\" id=\"" + firstPermission.getPercode() + "_icon\">" + (firstPermission.getIcon().length() > 0 ? firstPermission.getIcon() : "") + "</i><span class=\"firstMenuFont\">" + firstPermission.getName() + "</span>" + (haveSubMenu ? "<span class=\"layui-nav-more\"></span>" : "") + "</a>");
                    if (haveSubMenu) {
                        //有子菜单，加入进去
                        sb.append(subSB);
                    }
                    sb.append("</li>");
                }
            }
        }

        return sb.toString();
    }
    //缓存用户自己站点的信息， session中的ActiveUser的 pluginMap.key
    public static final String PLUGIN_NAME_SITE = "naii_site";
    //用户网站的模版变量，可能是已被编译(替换)过标签的内容了。key:template.name	value:模版变量的内容
    public static final String PLUGIN_NAME_TEMPLATEVAR_COMPILE_DATA_MAP = "naii_templateVarCompileDataMap";
    //原始的模版变量，其内包含模版变量的数据库中的原始内容. key:templateVar.name
    public static final String PLUGIN_NAME_TEMPLATEVAR_ORIGINAL_MAP = "naii_templateVarMapForOriginal";
    //缓存的当前用户的栏目信息 key:siteColumn.id 登录时不会缓存此处，在使用时才会缓存
    public static final String PLUGIN_NAME_SITECOLUMN_MAP = "naii_siteColumnMap";
    //当前CMS网站的输入模型，由 inputModelService 初始化赋予值。在用户进入内容管理，编辑时才会判断，如果此为null，才会从数据库加载数据
    public static final String PLUGIN_NAME_INPUTMODEL_MAP = "naii_inputModelMap";
    //网站管理后台的左侧菜单使用权限，只限网站用户有效。key: id，也就是左侧菜单的唯一id标示，比如模版管理是template，一键部署是 shengchengzhengzhan， 至于value，无意义，1即可
    public static final String PLUGIN_NAME_SITE_MENU_ROLE = "naii_siteMenuRole";
    //site_user 表的信息
    public static final String PLUGIN_NAME_SITE_USER = "naii_site_user";

    /**
     * 获取当前用户登录的站点信息。若是不存在，则返回null
     * @return
     */
    public static Site getSite(){
        return getPlugin(PLUGIN_NAME_SITE);
    }

    /**
     * 设置当前登录的站点信息
     * @param site {@link cn.edu.sjtu.gateway.manager.entity.Site}当前登录用户所管理的站点信息
     */
    public static void setSite(Site site){
        setPlugin(PLUGIN_NAME_SITE, site);
    }

    /**
     * 获取我当前使用的模版变量，可能是已被编译(替换)过标签的内容了。
     * @return map <ul>
     * 					<li>key:template.name</li>
     * 					<li>value:模版变量的内容</li>
     * 				</ul>
     */
    public static Map<String, String> getTemplateVarCompileDataMap(){
        return getPlugin(PLUGIN_NAME_TEMPLATEVAR_COMPILE_DATA_MAP);
    }

    /**
     * 设置我当前使用的模版变量，可能是已被编译(替换)过标签的内容了。
     * @param map <ul>
     * 					<li>key:template.name</li>
     * 					<li>value:模版变量的内容</li>
     * 				</ul>
     */
    public static void setTemplateVarCompileDataMap(Map<String, String> map){
        setPlugin(PLUGIN_NAME_TEMPLATEVAR_COMPILE_DATA_MAP, map);
    }

    /**
     * 获取原始的模版变量，其内包含模版变量的数据库中的原始内容.
     * @return map key:templateVar.name
     */
    public static Map<String, TemplateVarVO> getTemplateVarMapForOriginal(){
        return getPlugin(PLUGIN_NAME_TEMPLATEVAR_ORIGINAL_MAP);
    }

    /**
     * 设置 获取原始的模版变量，其内包含模版变量的数据库中的原始内容.
     * @param map key:templateVar.name
     */
    public static void setTemplateVarMapForOriginal(Map<String, TemplateVarVO> map){
        setPlugin(PLUGIN_NAME_TEMPLATEVAR_ORIGINAL_MAP, map);
    }

    /**
     * 获取缓存的当前用户的栏目信息 登录时不会缓存此处，在使用时才会缓存
     * @return map.key:siteColumn.id
     */
    public static Map<Integer, SiteColumn> getSiteColumnMap(){
        return getPlugin(PLUGIN_NAME_SITECOLUMN_MAP);
    }

    /**
     * 设置缓存的当前用户的栏目信息 登录时不会缓存此处，在使用时才会缓存
     * @param map key:siteColumn.id
     */
    public static void setSiteColumnMap(Map<Integer, SiteColumn> map){
        setPlugin(PLUGIN_NAME_SITECOLUMN_MAP, map);
    }

    /**
     * 获取当前CMS网站的输入模型，由 inputModelService 初始化赋予值。在用户进入内容管理，编辑时才会判断，如果此为null，才会从数据库加载数据
     * @return
     */
    public static Map<Integer, InputModel> getInputModel(){
        return getPlugin(PLUGIN_NAME_INPUTMODEL_MAP);
    }

    /**
     * 获取当前CMS网站的输入模型，由 inputModelService 初始化赋予值。在用户进入内容管理，编辑时才会判断，如果此为null，才会从数据库加载数据
     * @param map
     */
    public static void setInputModel(Map<Integer, InputModel> map){
        setPlugin(PLUGIN_NAME_INPUTMODEL_MAP, map);
    }

    /**
     * 网站管理后台的左侧菜单使用权限，只限网站用户有效。
     * @return key: id，也就是左侧菜单的唯一id标示，比如模版管理是template，一键部署是 shengchengzhengzhan， 至于value，无意义，1即可
     */
    public static Map<String, String> getSiteMenuRole(){
        return getPlugin(PLUGIN_NAME_SITE_MENU_ROLE);
    }

    /**
     * 网站管理后台的左侧菜单使用权限，只限网站用户有效。
     * @param map key: id，也就是左侧菜单的唯一id标示，比如模版管理是template，一键部署是 shengchengzhengzhan， 至于value，无意义，1即可
     */
    public static void setSiteMenuRole(Map<String, String> map){
        setPlugin(PLUGIN_NAME_SITE_MENU_ROLE, map);
    }



    /**
     * 获取当前用户登录的站点信息。若是不存在，则返回null
     * @return
     */
    public static SiteUser getSiteUser(){
        return getPlugin(PLUGIN_NAME_SITE_USER);
    }

    /**
     * 设置当前登录的站点信息
     * @param site {@link cn.edu.sjtu.gateway.manager.entity.Site}当前登录用户所管理的站点信息
     */
    public static void setSiteUser(SiteUser siteUser){
        setPlugin(PLUGIN_NAME_SITE_USER, siteUser);
    }
    //我的代理信息，如果我是代理的话，才有内容
    public static final String PLUGIN_NAME_AGENCY_MY = "naii_agency_my";
    //我的代理信息-边长表agency_data的信息，如果我是代理的话，才有内容
    public static final String PLUGIN_NAME_AGENCY_DATA_MY = "naii_agency_data_my";

    //我的上级代理信息，如果我有上级代理的话，才有内容
    public static final String PLUGIN_NAME_AGENCY_PARENT = "naii_agency_parent";
    //我的上级代理信息-变长表agency_data的信息，如果我有上级代理的话，才有内容
    public static final String PLUGIN_NAME_AGENCY_DATA_PARENT = "naii_agency_data_parent";


    /**
     * 获取我的代理信息
     */
    public static Agency getAgency(){
        return getPlugin(PLUGIN_NAME_AGENCY_MY);
    }

    /**
     * 设置我的代理信息
     */
    public static void setAgency(Agency agency){
        setPlugin(PLUGIN_NAME_AGENCY_MY, agency);
    }

    /**
     * 获取我的代理信息-变长表的信息
     */
    public static AgencyData getAgencyData(){
        return getPlugin(PLUGIN_NAME_AGENCY_DATA_MY);
    }
    /**
     * 设置我的代理信息-变长表的信息
     */
    public static void setAgencyData(AgencyData agencyData){
        setPlugin(PLUGIN_NAME_AGENCY_DATA_MY, agencyData);
    }



    /**
     * 获取我的上级代理信息
     */
    public static Agency getParentAgency(){
        return getPlugin(PLUGIN_NAME_AGENCY_PARENT);
    }

    /**
     * 设置我的上级代理信息
     */
    public static void setParentAgency(Agency agency){
        setPlugin(PLUGIN_NAME_AGENCY_PARENT, agency);
    }

    /**
     * 获取我的上级代理信息-变长表的信息
     */
    public static AgencyData getParentAgencyData(){
        return getPlugin(PLUGIN_NAME_AGENCY_DATA_PARENT);
    }
    /**
     * 设置我的上级代理信息-变长表的信息
     */
    public static void setParentAgencyData(AgencyData agencyData){
        setPlugin(PLUGIN_NAME_AGENCY_DATA_PARENT, agencyData);
    }
}
