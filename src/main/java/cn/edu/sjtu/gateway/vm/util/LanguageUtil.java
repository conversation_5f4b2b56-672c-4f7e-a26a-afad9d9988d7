package cn.edu.sjtu.gateway.vm.util;


import lombok.extern.slf4j.Slf4j;

/**
 * 语言相关，比如当前系统的语言包、显示文字调用等
 * <AUTHOR>
 */
@Slf4j
public class LanguageUtil {
	
	/**
	 * 调用语言包中设置的具体内容显示出来
	 * @param key 要显示的语言调用代码
	 * @return 显示的制定语言内容
	 */
	public static String show(String key) {
		return Language.show(getCurrentLanguagePackageName(), key);
	}

	
	/**
	 * 调用语言包中设置的具体内容显示出来
	 * @param key 要显示的语言调用代码
	 * @remark 当前调取显示内容的备注，无任何使用价值，仅仅只是方便开发者读代码
	 * @return 显示的制定语言内容
	 */
	public static String show(String key, String remark) {
		return Language.show(getCurrentLanguagePackageName(), key);
	}
	
	/**
	 * 从Shiro中，获取当前用户的语言包
	 * @return	<li>登陆了，则返回ActiveUser对象
	 * 			<li>未登陆，返回null
	 */
	public static String getCurrentLanguagePackageName() {
		String language = cn.edu.sjtu.gateway.vm.util.SessionUtil.getLanguagePackageName();
		if(language == null){
			//如果没有制定，那么使用默认的语言包
			return Language.language_default;
		}else{
			return language;
		}
	}
	
	/**
	 * 设置某个用户当前使用哪种语言
	 * @param languagePackageName 语种、语言包，如chinese、english
	 * @return 设置是否成功，true:成功
	 */
	public static boolean setCurrentLanguagePackageName(String languagePackageName) {
		if(Language.isHaveLanguagePackageName(languagePackageName)){
			SessionUtil.setLanguagePackageName(languagePackageName);
			return true;
		}else{
			return false;
		}
	}
	
	public static void main(String[] args) {
		log.info(""+show("user_loginSuccess"));
	}
}
