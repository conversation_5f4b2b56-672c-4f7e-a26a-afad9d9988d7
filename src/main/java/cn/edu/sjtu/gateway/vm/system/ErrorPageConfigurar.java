package cn.edu.sjtu.gateway.vm.system;

import org.springframework.boot.web.server.ErrorPage;
import org.springframework.boot.web.server.ErrorPageRegistrar;
import org.springframework.boot.web.server.ErrorPageRegistry;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

/**
 * 错误页配置，如404、500错误
 *
 * <AUTHOR>
 */
@Component
public class ErrorPageConfigurar implements ErrorPageRegistrar {
    @Override
    public void registerErrorPages(ErrorPageRegistry registry) {
        ErrorPage[] errorPages = new ErrorPage[2];
        errorPages[0] = new ErrorPage(HttpStatus.NOT_FOUND, "/404.naii");
		//优先根据此来进行排查。 先根据具体异常的类、再根据错误码
        errorPages[1] = new ErrorPage(org.apache.shiro.authz.AuthorizationException.class, "/403.naii");
//		errorPages[2]=new ErrorPage(HttpStatus.INTERNAL_SERVER_ERROR,"/500.naii");
        //errorPages[2]=new ErrorPage(HttpStatus.NOT_ACCEPTABLE,"/406.naii");


        registry.addErrorPages(errorPages);
    }

}
