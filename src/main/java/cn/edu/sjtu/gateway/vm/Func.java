package cn.edu.sjtu.gateway.vm;

/**
 * 常用函数
 * <AUTHOR>
 */
public class Func {

	/**
	 * 根据user表的authority字段的值，用户是否具有某个指定的角色(Role)
	 * @param authority User表的authority
	 * @param roleId role.id判断用户是否具有某个指定的角色，这里便是那个指定的角色，判断用户是否是授权了这个角色。多个用,分割，如传入  2,3,4
	 * 				<br/>
	 * @return true：是,用户拥有此角色
	 */
	public static boolean isAuthorityBySpecific(String authority, String roleId){
		String[] authArray = authority.split(",");
        for (String auth : authArray) {
            if (auth != null && !auth.isEmpty()) {
                if (auth.equals(roleId)) {
                    return true;
                }
            }
        }
		return false;
	}
	
}
