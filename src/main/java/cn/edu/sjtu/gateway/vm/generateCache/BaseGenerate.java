package cn.edu.sjtu.gateway.vm.generateCache;

import cn.edu.sjtu.gateway.manager.cache.AutoCreateDataFolder;
import cn.edu.sjtu.gateway.vm.Global;

import cn.edu.sjtu.gateway.vm.util.SystemUtil;
import cn.edu.sjtu.gateway.tools.DateUtil;
import cn.edu.sjtu.gateway.tools.file.FileUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Random;

/**
 * 所有js缓存生成的父类
 *
 * <AUTHOR>
 */
@Slf4j
public class BaseGenerate {

    /**
     * 生成js缓存文件的内容
     */
    public String content;

    /**
     * 生成的js存储数据的对象名，保存的文件名也是使用此有关联
     */
    public String objName;

    /**
     * 创建js对象
     *
     * @param objName js对象名（保存的js文件名、使用时引用的js对象名）
     */
    public void createCacheObject(String objName) {
        this.objName = objName;
        content = "var " + objName + " = new Array(); ";
    }

    /**
     * 往缓存js对象中增加键值对
     *
     * @param key   键
     * @param value 值
     */
    public void cacheAdd(Object key, Object value) {
        content += objName + "['" + key + "']='" + value + "'; ";
    }

    /**
     * 生成js缓存文件保存
     */
    public void generateCacheFile() {
        addCommonJsFunction();
        initCacheFolder();
        String filePath = SystemUtil.getProjectPath() + Global.CACHE_FILE + getClass().getSimpleName() + "_" + objName + ".js";

        try {
            content = content + " var xnx3_r" + DateUtil.timeForUnix10() + " = '" + getRandomValue() + "';";
            FileUtil.write(filePath, content, "UTF-8");
            log.info("create cache js file success ! file path : " + filePath);
        } catch (IOException e) {
            log.error("错误信息：------>" + e);
        }
        this.content = null;
    }

    /**
     * 每个js生成时，会加入一个长度随即的，100个以内字符的随机变量，防止浏览器因字节数一样而缓存数据
     *
     * @return 长度不固定的String，100个字符以内
     */
    private String getRandomValue() {
        Random r = new Random();
        int ri = r.nextInt(100);
        return "1".repeat(ri);
    }

    /**
     * 初始化缓存文件夹，若根目录下没有缓存文件夹，自动创建
     */
    private void initCacheFolder() {
        AutoCreateDataFolder.createPath(Global.CACHE_FILE);
    }



    /**
     * 增加一些常用的js函数
     */
    public void addCommonJsFunction() {
        this.content += "/*页面上输出选择框的所有option，显示到页面上*/ function writeSelectAllOptionFor" + this.objName + "(selectValue){ writeSelectAllOptionFor" + this.objName + "_(selectValue,'所有', false); } function writeSelectAllOptionFor" + this.objName + "_(selectValue,firstTitle,required){ var content = \"\"; if(selectValue==''){ content = content + '<option value=\"\" selected=\"selected\">'+firstTitle+'</option>'; }else{ content = content + '<option value=\"\">'+firstTitle+'</option>'; } for(var p in " + this.objName + "){ if(p == selectValue){ content = content+'<option value=\"'+p+'\" selected=\"selected\">'+" + this.objName + "[p]+'</option>'; }else{ content = content+'<option value=\"'+p+'\">'+" + this.objName + "[p]+'</option>'; } } document.write('<select name=" + this.objName + " '+(required? 'required':'')+' lay-verify=\"" + this.objName + "\" lay-filter=\"" + this.objName + "\" id=\"" + this.objName + "\" lay-search>'+content+'</select>'); }";
    }

    /**
     * 向写出的js文件里增加内容
     */
    public void appendContent(String content) {
        this.content = this.content + " " + content;
    }
}

