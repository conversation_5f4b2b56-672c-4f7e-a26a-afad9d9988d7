package cn.edu.sjtu.gateway.vm;

import cn.edu.sjtu.gateway.vm.util.ApplicationPropertiesUtil;
import cn.edu.sjtu.gateway.vm.util.SystemUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * 基础配置、集中管理
 *
 * <AUTHOR>
 */
@Slf4j
public class Global {
    //是否是以jar包的形式运行。默认为是jar包方式运行。若为false，则为war方式，放到自行放tomcat中运行
    public static boolean isJarRun = true;
    /**
     * 当前的版本号
     */
    public static final String VERSION = "3.12";

    /********文件目录相关，会在当前项目的根目录下的文件夹*********/
    public final static String CACHE_FILE = "cache" + File.separator + "js" + File.separator;
    public static final String CACHE_INPUT_MODEL_FILE = "cache" + File.separator + "inputmodel" + File.separator;
    public static final String CACHE_INPUT_MODEL_IMAGE = "cache" + File.separator + "naii" + File.separator;
    public static final String INPUT_MODEL_TEMPLATE_FILE = "templates" + File.separator + "inputmodel" + File.separator + "default_template.html";

    /*
     * 当前项目再硬盘的路径，绝对路径。动态参数，会在项目启动时加载。取此参数，可以使用 {@link #getProjectPath()} 取
     */
    public static String projectPath = System.getProperty("user.dir") + File.separator;

    /***** system表的参数,name-value ******/
    public static Map<String, String> system = new HashMap<String, String>();    //value：String字符串，此数据会在应用启动起来后，自动从数据库中将system表的全部数据取出来放到这里。
    /**
     * 同上system，
     * 只不过这里的value是Integer
     * 取这里的数据时，会先判断这个map中是否有数据
     * <ul>
     * 	<li>若有，直接取出</li>
     * 	<li>若没有，再找上面的system的map中是否有这个值
     * 		<ul>
     * 			<li>若有，则将其转换位int，并缓存到这个map里，下次再取这个值的时候就直接从这里就能取</li>
     * 			<li>若没有，返回 null （不返回0，因为如果返回null，开发程序的时候就会报错，就能直接定位到问题所在）</li>
     * 		</ul>
     * 	</li>
     * </ul>
     */
    public static Map<String, Integer> systemForInteger = new HashMap<String, Integer>();    //同上system，只不过这里的value是Integer，取这里的数据时，会先判断这map中是否有数据，若没有，再找system中是否有，若有，则将其转换位int，并缓存到这个map里，下次取的时候就直接从这里取


    /**********固定参数**********/
    public final static int USER_PASSWORD_SALT_NUMBER = 2;    //密码散列几次，2即可,需要跟配置文件的散列次数一致
    public final static int PROMPT_STATE_SUCCESS = 1;            //中专提示页面prompt.jsp的成功提示状态
    public final static int PROMPT_STATE_ERROR = 0;            //中专提示页面prompt.jsp的失败（错误）提示状态

    /**** 文件上传 *****/
    public static String ossFileUploadImageSuffixList = "png|jpg|jpeg|gif|bmp|flv|swf|mkv|avi|rm|rmvb|mpeg|mpg|ogg|ogv|mov|wmv|mp4|webm|mp3|wav|mid|rar|zip|tar|gz|7z|bz2|cab|iso|doc|docx|xls|xlsx|ppt|pptx|pdf|txt|md|xml"; //图片文件，允许上传的图片的后缀名，在 systemConfig.xml 中的 attachmentFile.allowUploadSuffix 中配置

    /***** 权限相关 *****/
    public static int roleId_manager = 9;                        //超级管理员的角色id
    public static int roleId_user = 1;                        //普通用户的角色id

    /****** iw框架down下来，搭建开发环境时使用到的参数 ******/
    public static boolean databaseCreateFinish = true;        //数据库是否完成创建导入。默认是数据库正常已创建。有initServlet初始化时进行判断，给其赋值
    public static String databaseCreateFinish_explain = "";    //其上出错的说明
    public static boolean xnx3Config_oss = true;            //OSS文件上传是否完成配置
    public static String xnx3Config_oss_explain = "";        //其上出错的说明

    //v2.29
    public static boolean translateUse = true;    //默认使用
    public static String translateApiHost = null;    //默认使用云端的


    static {
        //翻译相关
        Object translateUseObj = ApplicationPropertiesUtil.getProperty("translate.use");
        if (translateUseObj != null) {
            if ("FALSE".equals(translateUseObj.toString().toUpperCase())) {
                //不用
                translateUse = false;
            }
        }
        Object translateApiHostObj = ApplicationPropertiesUtil.getProperty("translate.api.host");
        if (translateApiHostObj != null) {
            String translateApiHostStr = translateApiHostObj.toString();
            if (!translateApiHostStr.isEmpty()) {
                translateApiHost = translateApiHostStr;
            }
        }

    }


    /**
     * 返回 system 表的值
     *
     * @param systemName
     * @return
     * @deprecated 已废弃，请使用 {@link cn.edu.sjtu.gateway.vm.util.SystemUtil#get(String)}
     */
    public static String get(String systemName) {
        return SystemUtil.get(systemName);
    }

    /**
     * 返回 system 表的值（int型的，此取的数据源来源于 {@link #get(String)}，只不过针对Integer进行了二次缓存 ）
     *
     * @param systemName 要获取的值的变量名
     * @return 变量的值。注意，若没有，会返回0
     * @deprecated 已废弃，请使用 {@link cn.edu.sjtu.gateway.vm.util.SystemUtil#getInt(String)}
     */
    public static int getInt(String systemName) {
        return SystemUtil.getInt(systemName);
    }

    /**
     * 当前项目再硬盘的路径，绝对路径 返回格式如 /aaa/bb/ccc/WEB-INF/classes/  最后会加上 /
     *
     * @deprecated 已废弃，请使用 {@link cn.edu.sjtu.gateway.vm.util.SystemUtil#getProjectPath()}
     */
    public static String getProjectPath() {
        return SystemUtil.getProjectPath();
    }

    public static void main(String[] args) {
        log.info("version:" + VERSION);
    }
}
