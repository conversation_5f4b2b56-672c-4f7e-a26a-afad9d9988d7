package cn.edu.sjtu.gateway.vm.service.impl;

import cn.edu.sjtu.gateway.vm.dao.SqlDAO;
import cn.edu.sjtu.gateway.vm.entity.User;
import cn.edu.sjtu.gateway.vm.service.ApiService;
import cn.edu.sjtu.gateway.vm.shiro.ShiroFunc;

import cn.edu.sjtu.gateway.vm.vo.UserVO;
import cn.edu.sjtu.gateway.tools.Lang;
import cn.edu.sjtu.gateway.tools.MD5Util;
import cn.edu.sjtu.gateway.tools.StringUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.subject.Subject;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */ //@Service
@Setter
@Getter
@Slf4j
public class ApiServiceImpl implements ApiService {
    @Resource
    private SqlDAO sqlDAO;

    @Override
    public UserVO identityVerify(String key) {
        UserVO vo = new UserVO();
        /*
         * 验证Key的格式
         */
        if (key.length() < 128 || !key.contains("_")) {
            vo.setBaseVO(UserVO.FAILURE, "key错误1");
            return vo;
        }

        String[] ks = key.split("_");
        if (ks[0].isEmpty() || ks[1].isEmpty()) {
            vo.setBaseVO(UserVO.FAILURE, "key错误2");
            return vo;
        }

        int userid = Lang.stringToInt(ks[0], 0);
        if (userid == 0) {
            vo.setBaseVO(UserVO.FAILURE, "key错误3");
            return vo;
        }
        String pwd = StringUtil.removeBlank(ks[1]);
        if (pwd.length() != 128) {
            vo.setBaseVO(UserVO.FAILURE, "key错误4");
            return vo;
        }

        /*
         * 验证Key是否存在
         * 获取代理商的user信息
         */
        User user = sqlDAO.findById(User.class, userid);
        if (user == null) {
            //统一提示，避免被利用
            vo.setBaseVO(UserVO.FAILURE, "key错误5");
            return vo;
        }
        if (!passwordMD5(user.getPassword()).equals(pwd)) {
            vo.setBaseVO(UserVO.FAILURE, "key错误6");
            return vo;
        }

        vo.setUser(user);
        return vo;
    }


    /**
     * 将32位的 user.password 密码进行再加密，生成128位加密字符串
     *
     * @param password user.password 密码
     * @return 新生成的128位加密字符串
     */
    private static String passwordMD5(String password) {
        return MD5Util.MD5(password);
    }

    @Override
    public UserVO identityVerifyAndSession(String key) {
        UserVO vo = identityVerify(key);
        if (vo.getResult() - UserVO.FAILURE == 0) {
            return vo;
        }

        UsernamePasswordToken token = new UsernamePasswordToken(vo.getUser().getUsername(), vo.getUser().getUsername());
        token.setRememberMe(false);
        Subject currentUser = SecurityUtils.getSubject();

        try {
            currentUser.login(token);
        } catch (AuthenticationException ae) {
            log.error("错误信息：------>" + ae);
        }

        return vo;
    }
	@Override
    public String getKey() {
        User user = ShiroFunc.getUser();
        if (user == null) {
            return null;
        }

        return user.getId() + "_" + passwordMD5(user.getPassword());
    }

}
