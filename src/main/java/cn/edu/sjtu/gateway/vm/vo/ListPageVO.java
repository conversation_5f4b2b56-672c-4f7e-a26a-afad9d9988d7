package cn.edu.sjtu.gateway.vm.vo;

import cn.edu.sjtu.gateway.vm.util.Page;
import cn.edu.sjtu.gateway.vm.vo.BaseVO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 通用列表分页，返回 List<Map>
 * <AUTHOR>
 *
 */
@Setter
@Getter
public class ListPageVO extends BaseVO {
	
	private List<Map<String,Object>> list;
	private Page page;

    @Override
	public String toString() {
		return "ListPageVO [list=" + list + ", page=" + page + ", getResult()=" + getResult() + ", getInfo()="
				+ getInfo() + "]";
	}
	
}
