package cn.edu.sjtu.gateway.vm.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import static cn.edu.sjtu.gateway.vm.pluginManage.interfaces.manage.SpringMVCInterceptorPluginManage.CONFIG_MAP;

/**
 * 读取 application.properties 属性，可以直接使用 ApplicationProperties.getProperty("key"); 进行调用
 *
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class ApplicationPropertiesUtil {
    /**
     * 获取 application.properties 的配置属性
     *
     * @param key 要获取的配置的名字，如 database.name
     * @return 获取的配置的值。需要判断是否为null
     */
    public static String getProperty(String key) {
        return CONFIG_MAP.getOrDefault(key, null) != null ? CONFIG_MAP.get(key).toString() : null;
    }

    public static String getProperty(String key, String defaultValue) {
        return CONFIG_MAP.getOrDefault(key, null) != null ? CONFIG_MAP.get(key).toString() : defaultValue;
    }
}
