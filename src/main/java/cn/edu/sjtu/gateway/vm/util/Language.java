package cn.edu.sjtu.gateway.vm.util;/**
 * @Description
 * <AUTHOR>
 * @Date 2024/9/25
 */

import cn.edu.sjtu.gateway.core.ConfigManagerUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;


/**
 * 语言包。
 * <br/>多种语言，如中文、英语、日语等
 * <br/>应用中需要选择切换到自己的语言的，可以使用
 *
 * <AUTHOR>
 */
public class Language {
    /*
     * 当用户选择使用哪种语言后，会在某个地方缓存下来这个语种的名字，这个缓存的名字统一都是叫这个。比如，session缓存、cookie缓存，cookie的名字便是调用这个的
     */
    public final static String cacheLanguagePackageName = "language_default";

    /**
     * 用户可自由切换当前语言，此处为语言库配置的(language.xml)，默认使用哪种语言包。（默认设想的是根据IP智能判断，若是在中国则使用简体中文尚未做ip地域匹配）
     * chinese:简体中文
     * english:英语
     */
    public static String language_default = "chinese";

    /**
     * language.get("chinese").get("collect_notCollectOneself")
     */
    public static Map<String, Map<String, String>> languageMap;

    static {
        language_default = ConfigManagerUtil.getSingleton("language.xml").getValue("defaultLanguage");
        loadLanguagePackage();
    }

    public static void main(String[] args) {

        for (Map.Entry<String, Map<String, String>> entry : languageMap.entrySet()) {
            System.out.println(entry.getKey() + " ---->  " + entry.getValue().size());

            for (Map.Entry<String, String> e : entry.getValue().entrySet()) {
                System.out.println("   " + e.getKey());
            }

        }
    }
    // 从 language.xml 加载语言包
    private static void loadLanguagePackage() {
        languageMap = new HashMap<>();
        // 获取配置文件的实例
        ConfigManagerUtil config = ConfigManagerUtil.getSingleton("language.xml");

        // 获取所有的顶级键
        Iterator<String> keys = config.getFileConfiguration().getKeys();

        // 遍历所有键，提取语言包的前缀部分（即顶级路径）
        while (keys.hasNext()) {
            String key = keys.next();

            // 判断是否包含 '.'，表示该键属于某种语言
            if (key.contains(".")) {
                // 提取语言名
                String languageName = key.split("\\.")[0];

                // 如果该语言名尚未加载，则加载对应的语言包
                if (!languageMap.containsKey(languageName)) {
                    loadLanguagePackage(languageName, config);
                }
            }
        }
    }

    /**
     * 从 language.xml 加载某个语言包
     *
     * @param languageName 语言名，如 chinese、english 等
     * @param config       配置文件实例
     */
    private static void loadLanguagePackage(String languageName, ConfigManagerUtil config) {
        // 获取该语言包的所有键
        Iterator<String> languageKeys = config.getFileConfiguration().getKeys(languageName);
        Map<String, String> languageMapForCurrent = new HashMap<>();

        // 遍历该语言包的所有键，存储对应的键值对
        while (languageKeys.hasNext()) {
            String key = languageKeys.next();
            String value = config.getValue(key);

            // 去掉语言前缀（例如 "english."），只保留实际的键
            String languageKey = key.replace(languageName + ".", "");
            languageMapForCurrent.put(languageKey, value);
        }

        // 将语言包存入全局语言表
        languageMap.put(languageName, languageMapForCurrent);
    }
    /**
     * 获取语言包language.xml中的描述文字显示出来。如果没有找到返回空字符""
     *
     * @param packageName 要显示的描述文字是使用的哪个语言包的，这里是语言包的名字，如 chinese 、 english。若传入null，则使用默认的语言包
     * @param key         要调用文字的代码
     * @return 显示出来的文字
     */
    public static String show(String packageName, String key) {
        if (packageName == null) {
            packageName = language_default;
        }
        if (key == null) {
            return "";
        }

        // 优化：添加空值检查，避免NullPointerException
        Map<String, String> languagePackage = languageMap.get(packageName);
        if (languagePackage == null) {
            return "";
        }

        String value = languagePackage.get(key);
        return value != null ? value : "";
    }

    /**
     * 获取当前多语言配置中，有多少种语言包、或有多少语种、有多少种语言
     * <br/>可用于，给用户选择使用哪种语种时，调取当前的所有语种
     *
     * @return 语种的List列表，如 chinese,english
     */
    public static List<String> getLanguagePackageList() {
        List<String> list = new ArrayList<String>();
        for (Map.Entry<String, Map<String, String>> entry : languageMap.entrySet()) {
            list.add(entry.getKey());
        }
        return list;
    }

    /**
     * 判断当前多语言配置(language.xml)中，是否有叫这个名字的语言包，若有，返回true
     * <br/>此方法多用在用户设置语言包后，缓存或保存时，判断一下用户设置的这个语言包是否真的存在
     *
     * @param packageName 语言包的名字，语种的名字，如chinese、english
     * @return true：有这个语言包
     */
    public static boolean isHaveLanguagePackageName(String packageName) {
        // 优化：直接返回判断结果，避免冗余的if-else
        return languageMap.get(packageName) != null;
    }
}