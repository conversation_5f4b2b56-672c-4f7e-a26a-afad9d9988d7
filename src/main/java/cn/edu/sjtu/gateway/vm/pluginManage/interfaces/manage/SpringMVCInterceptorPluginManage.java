package cn.edu.sjtu.gateway.vm.pluginManage.interfaces.manage;

import cn.edu.sjtu.gateway.tools.ScanClassUtil;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.PropertySource;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.PostConstruct;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Spring MVC 拦截器 扩展的接口
 * 注意，在这个插件执行的时候，spring 是还未加载的，也就是这个插件中不能调用 spring 的bean
 *
 * <AUTHOR>
 */
@Component(value = "PluginManageForSpringMVCInterceptor")
@Slf4j
public class SpringMVCInterceptorPluginManage {
    @Autowired
    private ApplicationContext applicationContext;
    public static final Map<String, Object> CONFIG_MAP = new HashMap<>();
    /**
     * 存储拦截器信息：
     * key: class、pathPatterns
     * value: HandlerInterceptor、List<String>
     */
    public static List<Map<String, Object>> handlerInterceptorList = new ArrayList<>();

    @PostConstruct
    public void init() {
        initConfigData();
        initializeHandlerInterceptors();
    }

    // 用于缓存加载的配置

    public void initConfigData() {
        // 获取 ConfigurableEnvironment
        ConfigurableEnvironment environment = (ConfigurableEnvironment) applicationContext.getEnvironment();
        // 遍历所有的 PropertySource
        for (PropertySource<?> propertySource : environment.getPropertySources()) {
            // 如果是 Map 类型的 PropertySource
            if (propertySource instanceof org.springframework.core.env.MapPropertySource) {
                Map<String, Object> properties = (Map<String, Object>) propertySource.getSource();
                properties.forEach((key, value) -> CONFIG_MAP.put(key, value));
            }
        }
        log.info("加载 application.properties 配置文件成功"+CONFIG_MAP);
    }
    /**
     * 初始化 SpringMVC 拦截器插件
     */
    private static void initializeHandlerInterceptors() {
        try {
            List<Class<?>> allClassList = ScanClassUtil.getClasses("cn.edu.sjtu");
            // 用于存储插件的类列表
            List<Class<?>> classList = ScanClassUtil.searchByInterfaceName(
                    allClassList,
                    "cn.edu.sjtu.gateway.vm.pluginManage.interfaces.SpringMVCInterceptorInterface"
            );

            for (Class<?> clazz : classList) {
                loadInterceptorPlugin(clazz);
            }
        } catch (Exception e) {
            log.error("初始化拦截器插件时发生错误：", e);
        }
    }

    /**
     * 加载单个拦截器插件
     *
     * @param clazz 插件类
     */
    private static void loadInterceptorPlugin(Class<?> clazz) {
        try {
            Object instance = clazz.getDeclaredConstructor().newInstance();

            Method pathPatternsMethod = clazz.getMethod("pathPatterns");
            Object result = pathPatternsMethod.invoke(instance);

            if (result instanceof List<?>) {
                @SuppressWarnings("unchecked")
                List<String> pathPatterns = (List<String>) result;

                Map<String, Object> map = new HashMap<>();
                map.put("class", instance);
                map.put("pathPatterns", pathPatterns);

                handlerInterceptorList.add(map);
                log.info("装载 SpringMVCInterceptor 插件：{}", clazz.getName());
            }
        } catch (ReflectiveOperationException e) {
            log.error("加载拦截器插件 {} 时发生错误：", clazz.getName(), e);
        }
    }
}

