package cn.edu.sjtu.gateway.vm.vo;

import cn.edu.sjtu.gateway.vm.vo.BaseVO;
import lombok.Getter;
import lombok.Setter;

/**
 * 文件上传规则，允许上传哪些后缀、最大上传大小等
 * <AUTHOR>
 */
@Setter
@Getter
public class UploadFileRuleVO extends BaseVO {
	//允许上传的文件后缀列表，多个以|分割，如 png|jpg|rar|zip
	private String allowUploadSuffix;
	//最大上传限制，单位：KB，在AttachmentUtil.getMaxFileSizeKB()获取
	private int maxFileSizeKB;

    @Override
	public String toString() {
		return "UploadFileRuleVO [allowUploadSuffix=" + allowUploadSuffix + ", maxFileSizeKB=" + maxFileSizeKB + "]";
	}
	
}
