package cn.edu.sjtu.gateway.vm.controller.manager;

import cn.edu.sjtu.gateway.vm.bean.PermissionTree;
import cn.edu.sjtu.gateway.vm.bean.RoleMark;
import cn.edu.sjtu.gateway.vm.controller.BaseController;
import cn.edu.sjtu.gateway.vm.entity.Permission;
import cn.edu.sjtu.gateway.vm.entity.Role;
import cn.edu.sjtu.gateway.vm.entity.User;
import cn.edu.sjtu.gateway.vm.service.RoleService;
import cn.edu.sjtu.gateway.vm.service.SqlService;
import cn.edu.sjtu.gateway.vm.shiro.ShiroFunc;
import cn.edu.sjtu.gateway.vm.util.ActionLogUtil;

import cn.edu.sjtu.gateway.vm.util.Page;
import cn.edu.sjtu.gateway.vm.util.Sql;
import cn.edu.sjtu.gateway.vm.vo.BaseVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * 角色权限管理
 * <AUTHOR>
 */
@Controller(value="WMRoleManagerController")
@RequestMapping("/manager/role")
@Slf4j
public class RoleManagerController extends BaseController {
	
	private final RoleService roleService;
	private final SqlService sqlService;

	public RoleManagerController(RoleService roleService, SqlService sqlService) {
		this.roleService = roleService;
		this.sqlService = sqlService;
	}

	/**
	 * 新增、编辑角色
	 * @param id 要编辑的角色的id, Role.id
	 */
	@RequestMapping("role${url.suffix}")
	@RequiresPermissions("managerRoleRole")
	public String role(@RequestParam(value = "id", required = true) int id,Model model, HttpServletRequest request){
		if(id>0){
			//修改
			Role role = sqlService.findById(Role.class, id);
			if(role!=null){
				ActionLogUtil.insert(request, role.getId(), "进入编辑角色页面", role.getName()+"，"+role.getDescription());
				model.addAttribute("role", role);
			}
		}else{
			//新增
			log.info("进入添加角色页面");
		}
		return "wm/manager/role/role";
	}
	
	/**
	 * 添加／修改角色提交页
	 * @param role {@link cn.edu.sjtu.gateway.vm.entity.Role}
	 */
	@RequestMapping(value="saveRole${url.suffix}", method = RequestMethod.POST)
	@RequiresPermissions("managerRoleRole")
	@ResponseBody
	public BaseVO saveRole(Role role,Model model, HttpServletRequest request){
		if(role.getId() != null && role.getId() > 0){
			//是修改
			Role editrole = sqlService.findById(Role.class, role.getId());
			editrole.setName(role.getName());
			editrole.setDescription(role.getDescription());
			sqlService.save(editrole);
		}else{
			//是新增
			sqlService.save(role);
		}
		
		ActionLogUtil.insertUpdateDatabase(request, role.getId(), "角色保存", role.getName()+"，"+role.getDescription());
		return success();
	}
	

	/**
	 * 删除角色
	 * @param id 要删除的角色id，Role.id
	 */
	@RequestMapping(value="deleteRole${url.suffix}", method = RequestMethod.POST)
	@RequiresPermissions("managerRoleDeleteRole")
	@ResponseBody
	public BaseVO deleteRole(@RequestParam(value = "id", required = true) int id, Model model, HttpServletRequest request){
		if(id>0){
			Role role = sqlService.findById(Role.class, id);
			if(role!=null){
				sqlService.delete(role);
				ActionLogUtil.insertUpdateDatabase(request, role.getId(), "删除角色", role.getName()+"，"+role.getDescription());
				return success();
			}
		}
		return error("删除失败");
	}
	
	/**
	 * 角色列表
	 */
	@RequestMapping("roleList${url.suffix}")
	@RequiresPermissions("managerRoleRoleList")
	public String roleList(Model model, HttpServletRequest request){
		List<Role> list = sqlService.findAll(Role.class);
		
		log.info("角色列表");
		model.addAttribute("list", list);
		return "wm/manager/role/roleList";
	}
	
	/**
	 * 添加资源permission
	 * @param parentId 所添加的资源的所属上级资源。如果是顶级资源，则为0
	 */
	@RequiresPermissions("managerRolePermission")
	@RequestMapping("addPermission${url.suffix}")
	public String addPermission(
			@RequestParam(value = "parentId", required = true) int parentId,
			Model model, HttpServletRequest request){
		Permission parentPermission = sqlService.findById(Permission.class, parentId);
		String parentPermissionDescription = "顶级";
		if(parentPermission!=null){
			parentPermissionDescription = parentPermission.getName() +","+ parentPermission.getDescription();
		}
		
		Permission permission = new Permission();
		permission.setParentId(parentId);
		
		ActionLogUtil.insert(request, parentId, "进入添加资源Permission页面", "所属上级："+parentPermissionDescription);
		model.addAttribute("permission", permission);
		model.addAttribute("parentPermissionDescription", parentPermissionDescription);
		return "wm/manager/role/permission";
	}
	
	/**
	 * 编辑资源
	 * @param id 资源的id，Permission.id 
	 */
	@RequiresPermissions("managerRolePermission")
	@RequestMapping("editPermission${url.suffix}")
	public String editPermission(@RequestParam(value = "id", required = true) int id,Model model, HttpServletRequest request){
		if(id>0){
			Permission permission = sqlService.findById(Permission.class, id);
			if(permission!=null){
				String parentPermissionDescription="顶级";
				if(permission.getParentId()>0){
					Permission parentPermission = sqlService.findById(Permission.class, permission.getParentId());
					parentPermissionDescription = parentPermission.getName() +","+ parentPermission.getDescription();
				}
				
				ActionLogUtil.insert(request, permission.getId(), "进入修改资源Permission页面", "所属上级："+parentPermissionDescription);
				
				model.addAttribute("permission", permission);
				model.addAttribute("parentPermissionDescription", parentPermissionDescription);
				return "wm/manager/role/permission";
			}
		}
		return error(model, "出错，参数错误");
	}

	/**
	 * Permission提交保存
	 * @param request 要保存的{@link cn.edu.sjtu.gateway.vm.entity.Permission}
	 */
	@RequiresPermissions("managerRolePermission")
	@RequestMapping(value="savePermission${url.suffix}", method = RequestMethod.POST)
	@ResponseBody
	public BaseVO savePermission(Permission permissionInput,Model model, HttpServletRequest request){
		Permission permission;
		if(permissionInput.getId() != null && permissionInput.getId() > 0){
			//修改
			permission = sqlService.findById(Permission.class, permissionInput.getId());
			if(permission == null){
				return error("修改的资源不存在");
			}
		}else{
			permission = new Permission();
		}
		permission.setDescription(permissionInput.getDescription());
		permission.setName(permissionInput.getName());
		permission.setParentId(permissionInput.getParentId());
		permission.setPercode(permissionInput.getPercode());
		permission.setUrl(permissionInput.getUrl());
		permission.setIcon(permissionInput.getIcon()+"");
//		permission.setMenu(permissionInput.getMenu());
		sqlService.save(permission);
		ActionLogUtil.insertUpdateDatabase(request, permission.getId(), "资源Permission保存", permission.getName()+"，"+permission.getDescription());
		return success();
	}

	/**
	 * 删除资源Permission
	 * @param id 要删除的资源的id，Permission.id ，根据此来删除
	 */
	@RequiresPermissions("managerRoleDeletePermission")
	@RequestMapping(value="deletePermission${url.suffix}", method = RequestMethod.POST)
	@ResponseBody
	public BaseVO deletePermission(@RequestParam(value = "id", required = true) int id, HttpServletRequest request){
		if(id>0){
			Permission permission = sqlService.findById(Permission.class, id);
			if(permission!=null){
				sqlService.delete(permission);
				ActionLogUtil.insertUpdateDatabase(request, permission.getId(), "删除资源Permission", permission.getName()+"，"+permission.getDescription());
				return success();
			}
		}
		
		return error("删除失败");
	}
	
	/**
	 * 资源Permission列表
	 */
	@RequiresPermissions("managerRolePermissionList")
	@RequestMapping("permissionList${url.suffix}")
	public String permissionList(HttpServletRequest request,Model model){
		Sql sql = new Sql(request);
		sql.setSearchColumn(new String[]{"description","url","name","parent_id","percode","menu"});
		int count = sqlService.count("permission", sql.getWhere());
		Page page = new Page(count, 1000, request);
		sql.setSelectFromAndPage("SELECT * FROM permission", page);
		sql.setDefaultOrderBy("permission.rank ASC");
		List<Permission> list = sqlService.findBySql(sql, Permission.class);
		List<PermissionTree> permissionTreeList = new ShiroFunc().PermissionToTree(new ArrayList<Permission>(), list);
		
		log.info("资源Permission列表","第"+page.getCurrentPageNumber()+"页");
		
		model.addAttribute("page", page);
		model.addAttribute("list", permissionTreeList);
		return "wm/manager/role/permissionList";
	}
	
	/**
	 * 编辑某个权限下拥有的资源
	 * @param roleId 角色id，Rold.id
	 */
	@RequiresPermissions("managerRoleEditRolePermission")
	@RequestMapping("editRolePermission${url.suffix}")
	public String editRolePermission(@RequestParam(value = "roleId", required = true) int roleId, Model model, HttpServletRequest request){
		if(roleId>0){
			List<Permission> myList = roleService.findPermissionByRoleId(roleId);	//选中的
			List<Permission> allList = sqlService.findAll(Permission.class);
			//转换为树状集合
			List<PermissionTree> list = new ShiroFunc().PermissionToTree(myList, allList);	
			
			Role role = sqlService.findById(Role.class, roleId);
			
			ActionLogUtil.insert(request, role.getId(), "编辑某个权限下拥有的资源",role.getName());
			model.addAttribute("role", role);
			model.addAttribute("list", list);
			return "wm/manager/role/rolePermission";
		}
		return null;
	}

	/**
	 * 保存角色－资源设置
	 * @param roleId 角色id，Role.id
	 * @param permission 多选框的资源列表，如 1,2,3,4
	 */
	@RequiresPermissions("managerRoleEditRolePermission")
	@RequestMapping(value="saveRolePermission${url.suffix}", method = RequestMethod.POST)
	public String saveRolePermission(
			@RequestParam(value = "roleId", required = true) int roleId,
			@RequestParam(value = "permission", required = false) String permission,
			Model model, HttpServletRequest request){
		BaseVO vo = roleService.saveRolePermission(roleId, permission);
		if(vo.getResult() - BaseVO.SUCCESS == 0){
			ActionLogUtil.insertUpdateDatabase(request, roleId, "保存角色所管理的资源");
			return success(model, "保存成功","manager/role/roleList.naii");
		}else{
			return error(model, "保存失败："+vo.getInfo());
		}
	}
	

	/**
	 * 编辑用户－权限关系
	 * @param userid 用户id，User.id
	 */
	@RequiresPermissions("managerRoleEditUserRole")
	@RequestMapping("editUserRole${url.suffix}")
	public String editUserRole(@RequestParam(value = "userid", required = true) int userid, Model model, HttpServletRequest request){
		User user = sqlService.findById(User.class, userid);
		List<RoleMark> list = roleService.getUserRoleMarkList(userid);
		
		log.info("管理后台，编辑用户－权限关系");
			
		model.addAttribute("currentUser", user);
		model.addAttribute("list", list);
		return "wm/manager/role/userRole";
	}

	/**
	 * 保存用户－角色设置
	 * @param userid 修改的是哪个用户的权限，用户id, User.id
	 * @param role 权限多选框提交列表，如 1,2,3,4,5
	 */
	@RequiresPermissions("managerRoleEditUserRole")
	@RequestMapping(value="saveUserRole${url.suffix}", method = RequestMethod.POST)
	@ResponseBody
	public BaseVO saveUserRole(
			@RequestParam(value = "userid", required = true) int userid,
			@RequestParam(value = "role", required = false) String role,
			Model model, HttpServletRequest request){
		BaseVO vo = roleService.saveUserRole(userid, role);
		if(vo.getResult() - BaseVO.SUCCESS == 0){
			log.info("修改用户的角色权限");
			return success();
		}else{
			return error(vo.getInfo());
		}
	}
	

	/**
	 * 设置当前资源是否是菜单
	 * @param permissionid 资源的id
	 * @param model 权限多选框提交列表，如 1,2,3,4,5
	 */
	@RequiresPermissions("managerRoleEditPermissionMenu")
	@RequestMapping(value="editPermissionMenu${url.suffix}", method = RequestMethod.POST)
	@ResponseBody
	public BaseVO editPermissionMenu(
			@RequestParam(value = "permissionid", required = true, defaultValue="0") int permissionid,
			@RequestParam(value = "menu", required = false, defaultValue="0") short menu,
			Model model, HttpServletRequest request){
		Permission permission = sqlService.findById(Permission.class, permissionid);
		if(permission == null){
			return error("要操作的资源不存在");
		}
		
		permission.setMenu((short) (menu - 1 == 0? 1:0));
		sqlService.save(permission);
		
		ActionLogUtil.insertUpdateDatabase(request, permission.getId(), "修改资源是否是菜单", permission.toString());
		return success();
	}
	
	/**
	 * 保存资源的排序
	 * @param permissionid 要调整排序的资源id
	 * @param rank 排序的数字，数字越小越靠前
	 */
	@RequiresPermissions("managerRoleEditPermissionRank")
	@RequestMapping(value="/savePermissionRank${url.suffix}", method = RequestMethod.POST)
	@ResponseBody
	public BaseVO savePermissionRank(HttpServletRequest request,
			@RequestParam(value = "permissionid", required = false , defaultValue="0") int permissionid,
			@RequestParam(value = "rank", required = false , defaultValue="0") int rank){
		Permission permission = sqlService.findById(Permission.class, permissionid);
		if(permission == null){
			return error("要操作的资源不存在");
		}
		permission.setRank(rank);
		sqlService.save(permission);
		
		ActionLogUtil.insertUpdateDatabase(request, permission.getId(), "保存权限资源的排序", permission.toString());
		return success();
	}
	
}