package cn.edu.sjtu.gateway.vm.generateCache;

import cn.edu.sjtu.gateway.vm.generateCache.BaseGenerate;
import cn.edu.sjtu.gateway.vm.service.SqlService;

import java.util.List;

/**
 * 权限相关数据缓存生成
 * <AUTHOR>
 *
 */
public class Role extends BaseGenerate {
	
	/**
	 * 项目启动时，中进行初始化
	 */
	public void role(SqlService sqlService){
		List<cn.edu.sjtu.gateway.vm.entity.Role> list = sqlService.findAll(cn.edu.sjtu.gateway.vm.entity.Role.class);
		createCacheObject("role");
        for (cn.edu.sjtu.gateway.vm.entity.Role role : list) {
            cacheAdd(role.getId(), role.getName());
        }
		appendContent("/* 将 2,3,4 的权限字段转换为会员,超级管理员显示在html */ function writeName(authority){var roleArray=authority.split(',');var s='';for(var i=0;i<roleArray.length;i++){if(s!=''){s=s+','}s=s+role[roleArray[i]]}document.write(s)} ");
		
		generateCacheFile();
	}
}
