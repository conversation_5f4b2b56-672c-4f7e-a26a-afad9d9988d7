package cn.edu.sjtu.gateway.vm.util;

import cn.edu.sjtu.gateway.vm.Global;
import cn.edu.sjtu.gateway.tools.Lang;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Objects;

/**
 * 系统工具,比如获取系统变量的参数
 *
 * <AUTHOR>
 */
@Slf4j
public class SystemUtil extends cn.edu.sjtu.gateway.tools.SystemUtil {

    /**
     * 返回 system 表的值，系统变量的值
     *
     * @param systemVarName 系统变量的name，也就是 system 表的 name 列
     * @return 值，也就是 system 表的 value 列。如果没有，则会返回null
     */
    public static String get(String systemVarName) {
        return Global.system.get(systemVarName);
    }

    /**
     * 向缓存中加入一个临时的系统变量 （系统重启后会消失），可以用 SystemUtil.get 进行取
     *
     * @param name  系统变量的name
     * @param value 系统变量的值
     */
    public static void put(String name, String value) {
        Global.system.put(name, value);
    }

    /**
     * 返回 system 表的值（int型的，此取的数据源来源于 {@link #get(String)}，只不过针对Integer进行了二次缓存 ）
     *
     * @param systemVarName 系统变量的name，也就是 system 表的 name 列
     * @return 值，也就是 system 表的 value 列。如果没有，会返回0
     */
    public static int getInt(String systemVarName) {
        Integer i = Global.systemForInteger.get(systemVarName);
        if (i == null) {
            //没有这个值，那么从system这个原始map中找找
            String s = Global.system.get(systemVarName);
            if (s != null) {
                i = Lang.stringToInt(s, 0);
                Global.systemForInteger.put(systemVarName, i);
            }
        }
        if (i == null) {
            i = 0;
        }
        Global.systemForInteger.put(systemVarName, i);

        return i;
    }

    /**
     * 当前项目再硬盘的路径，绝对路径。
     * <br/>返回格式如 /Users/<USER>/git/wangmarket/target/classes/  最后会加上 /
     * <br/>如果是在编辑器中开发时运行，返回的是 /Users/<USER>/git/wangmarket/target/classes/ 这种，最后是有 /target/classes/ 的
     * <br/>如果是在实际项目中运行，也就是在服务器的Tomcat中运行，返回的是 /mnt/tomcat8/webapps/ROOT/ 这样的，最后是结束到 ROOT/ 下
     */
    public static String getProjectPath() {
        if (Global.projectPath == null) {
            String path = Objects.requireNonNull(Global.class.getResource("/")).getPath();
            Global.projectPath = path.replace("WEB-INF/classes/", "");
            log.info("projectPath : " + Global.projectPath);
        }
        return Global.projectPath;
    }

    /**
     * 获取JAR包所在的目录路径（用于JAR包部署模式）
     * 返回格式如 /home/<USER>/app/ 最后会加上 /
     * 如果是开发模式运行，返回项目根目录
     */
    public static String getJarDirPath() {
        if (isJarRun()) {
            // JAR包运行模式，返回JAR包所在目录
            try {
                URI location = SystemUtil.class.getProtectionDomain().getCodeSource().getLocation().toURI();
                String jarPath;
                
                // 判断是否为jar协议
                if (location.toString().startsWith("jar:")) {
                    // 对于jar:file:/path/to/app.jar!/BOOT-INF/classes!/ 这样的URI
                    String path = location.toString();
                    // 提取JAR文件路径部分
                    if (path.contains("!/")) {
                        path = path.substring(0, path.indexOf("!/"));
                    }
                    if (path.startsWith("jar:")) {
                        path = path.substring(4);
                    }
                    if (path.startsWith("file:")) {
                        path = path.substring(5);
                    }
                    jarPath = path;
                } else {
                    // 直接是文件URI
                    jarPath = new File(location).getPath();
                }
                
                // 如果是JAR文件路径，则获取其所在目录
                if (jarPath.endsWith(".jar")) {
                    return new File(jarPath).getParent() + File.separator;
                } else {
                    // 其他情况返回user.dir
                    return System.getProperty("user.dir") + File.separator;
                }
            } catch (Exception e) {
                log.warn("获取JAR包路径失败{}，获取user.dir:{}, {}", SystemUtil.class.getProtectionDomain().getCodeSource().getLocation(), System.getProperty("user.dir") + File.separator, e.getMessage());
                return System.getProperty("user.dir") + File.separator;
            }
        } else {
            // 开发模式，返回项目根目录
            return System.getProperty("user.dir") + File.separator;
        }
    }

    private static String classesPath = null;

    /**
     * 获取当前class文件所在的路径根目录。
     * <p>如果是在maven项目中开发时，则返回的是 ....../target/classes/</p>
     * <p>如果是在服务器上tomcat中时，则返回的是 ....../tomcat/webapps/ROOT/WEB-INF/classes/</p>
     *
     * @return 绝对路径
     */
    public static String getClassesPath() {
        if (classesPath == null) {
            classesPath = Objects.requireNonNull(SystemUtil.class.getResource("/")).getPath();
        }
        return classesPath;
    }

    public static boolean isJarRun() {
        return Global.isJarRun;
    }

    public static void main(String[] args) {
        try {
            URI uri = SystemUtil.class.getProtectionDomain().getCodeSource().getLocation().toURI();
            System.out.println(uri);
            String url = System.getProperty("user.dir") + File.separator;
            System.out.println(url);
        } catch (URISyntaxException e) {
            throw new RuntimeException(e);
        }
    }
}
