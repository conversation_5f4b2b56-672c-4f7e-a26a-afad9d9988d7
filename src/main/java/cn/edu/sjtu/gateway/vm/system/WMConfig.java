package cn.edu.sjtu.gateway.vm.system;

import cn.edu.sjtu.gateway.vm.service.ApiService;
import cn.edu.sjtu.gateway.vm.service.RoleService;
import cn.edu.sjtu.gateway.vm.service.SqlCacheService;
import cn.edu.sjtu.gateway.vm.service.SystemService;
import cn.edu.sjtu.gateway.vm.service.UserService;
import cn.edu.sjtu.gateway.vm.service.impl.ApiServiceImpl;
import cn.edu.sjtu.gateway.vm.service.impl.RoleServiceImpl;
import cn.edu.sjtu.gateway.vm.service.impl.SqlCacheServiceImpl;
import cn.edu.sjtu.gateway.vm.service.impl.SystemServiceImpl;
import cn.edu.sjtu.gateway.vm.service.impl.UserServiceImpl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

/**
 * WM 的配置，service、dao等
 * <AUTHOR>
 */
@Configuration
@Order(11)
@Slf4j
public class WMConfig {
	
	public WMConfig() {
		log.info("Spring Scan : WMConfig");
	}
	@Bean
	public ApiService apiService(){
		return new ApiServiceImpl();
	}
	@Bean
	public RoleService roleService(){
		return new RoleServiceImpl();
	}
	@Bean
	public SqlCacheService sqlCacheService(){
		return new SqlCacheServiceImpl();
	}
	@Bean
	public SystemService systemService(){
		return new SystemServiceImpl();
	}
	@Bean
	public UserService userService(){
		return new UserServiceImpl();
	}
	
}
