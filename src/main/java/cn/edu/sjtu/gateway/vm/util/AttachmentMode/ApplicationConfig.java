package cn.edu.sjtu.gateway.vm.util.AttachmentMode;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * application.properties / yml 中的一些相关配置项目
 * 用于适配旧版本设置，AttachmentUtil 、 FileUploadUtil 这两个同步适配用的
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Component(value = "FildUploadApplicationConfig")
@ConfigurationProperties(prefix = "fileupload")
public class ApplicationConfig {
	//自定义存储方式时，创建存储方式的初始化相关参数
	private Map<String, Map<String, String>> storage;
	
	//设置上传后文件所访问URL的域名，传入如： http://xxxx.com/  注意格式，后面以 / 结尾
	private String domain;
	
	//设置允许上传的后缀名,传入格式如 png|jpg|gif|zip 多个用英文|分割。如果不设置，默认允许像是pdf、word、图片、音频、视频、zip等常用的且安全的文件后缀可上传
	private String allowUploadSuffix;
	
	//设置允许上传的文件最大是多大，比如10MB 单位为 KB、MB ， 如果此项不设置，这里默认是3MB
	private String maxSize;
}