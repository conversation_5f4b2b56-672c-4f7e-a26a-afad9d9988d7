package cn.edu.sjtu.gateway.vm.system;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.servlet.resource.ResourceHttpRequestHandler;

import javax.servlet.ServletContext;
import java.io.File;
import java.io.IOException;
import java.net.URL;

/**
 * JSP资源配置 - 专门处理JAR包中的JSP资源访问
 * 
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class JspResourceConfiguration {
    
    /**
     * Web服务器工厂定制器 - 确保JSP资源能被正确访问
     */
    @Bean
    public WebServerFactoryCustomizer<ConfigurableServletWebServerFactory> jspResourceCustomizer() {
        return factory -> {
            log.info("配置JSP资源访问支持");
            
            // 设置文档根目录
            try {
                // 获取META-INF/resources目录
                ClassPathResource resource = new ClassPathResource("META-INF/resources");
                if (resource.exists()) {
                    URL resourceUrl = resource.getURL();
                    log.info("找到JSP资源目录: {}", resourceUrl);
                    
                    // 如果是JAR包中的资源，需要特殊处理
                    if ("jar".equals(resourceUrl.getProtocol())) {
                        log.info("检测到JAR包模式，启用JSP资源特殊处理");
                        
                        // 创建临时目录并解压所有资源文件
                        File tempDir = createTempJspDir();
                        extractAllResources(tempDir);

                        // 设置文档根目录为临时目录
                        factory.setDocumentRoot(tempDir);
                        log.info("设置JSP文档根目录: {}", tempDir.getAbsolutePath());
                    }
                } else {
                    log.warn("未找到META-INF/resources目录");
                }
            } catch (Exception e) {
                log.error("配置JSP资源访问失败", e);
            }
        };
    }
    
    /**
     * 创建临时JSP目录
     */
    private File createTempJspDir() {
        try {
            File tempDir = new File(System.getProperty("java.io.tmpdir"), "naii-jsp-" + System.currentTimeMillis());
            if (!tempDir.exists()) {
                tempDir.mkdirs();
            }
            tempDir.deleteOnExit();
            return tempDir;
        } catch (Exception e) {
            throw new RuntimeException("无法创建临时JSP目录", e);
        }
    }
    
    /**
     * 从JAR包中解压所有资源文件到临时目录
     */
    private void extractAllResources(File tempDir) {
        try {
            log.info("开始解压所有资源文件到临时目录");

            // 1. 解压META-INF/resources下的所有文件（包括JSP和静态资源）
            copyResourceDirectory("META-INF/resources/", tempDir);

            // 2. 解压static目录下的所有静态资源
            File staticDir = new File(tempDir, "static");
            staticDir.mkdirs();
            copyResourceDirectory("static/", staticDir);

            log.info("所有资源文件解压完成");
        } catch (Exception e) {
            log.error("解压资源文件失败", e);
        }
    }
    
    /**
     * 递归复制资源目录 - 包括JSP和静态资源
     */
    private void copyResourceDirectory(String resourcePath, File targetDir) {
        try {
            // 使用Spring的资源加载机制
            org.springframework.core.io.support.PathMatchingResourcePatternResolver resolver =
                new org.springframework.core.io.support.PathMatchingResourcePatternResolver();

            // 复制所有类型的资源文件
            String[] patterns = {
                "**/*.jsp",     // JSP文件
                "**/*.css",     // CSS文件
                "**/*.js",      // JavaScript文件
                "**/*.png",     // 图片文件
                "**/*.jpg",
                "**/*.jpeg",
                "**/*.gif",
                "**/*.ico",
                "**/*.svg",
                "**/*.woff",    // 字体文件
                "**/*.woff2",
                "**/*.ttf",
                "**/*.eot",
                "**/*.html",    // HTML文件
                "**/*.json",    // JSON文件
                "**/*.xml"      // XML文件
            };

            int totalFiles = 0;

            for (String pattern : patterns) {
                org.springframework.core.io.Resource[] resources =
                    resolver.getResources("classpath*:" + resourcePath + pattern);

                for (org.springframework.core.io.Resource resource : resources) {
                    if (resource.exists() && resource.isReadable()) {
                        String relativePath = extractRelativePath(resource.getURI().toString(), resourcePath);

                        File targetFile = new File(targetDir, relativePath);
                        targetFile.getParentFile().mkdirs();

                        // 复制文件
                        try (java.io.InputStream is = resource.getInputStream();
                             java.io.FileOutputStream fos = new java.io.FileOutputStream(targetFile)) {

                            byte[] buffer = new byte[8192];
                            int bytesRead;
                            while ((bytesRead = is.read(buffer)) != -1) {
                                fos.write(buffer, 0, bytesRead);
                            }
                        }

                        log.debug("复制资源文件: {} -> {}", resource.getURI(), targetFile.getAbsolutePath());
                        totalFiles++;
                    }
                }
            }

            log.info("共复制了 {} 个资源文件到临时目录", totalFiles);

        } catch (Exception e) {
            log.error("复制资源目录失败", e);
        }
    }
    
    /**
     * 提取相对路径
     */
    private String extractRelativePath(String fullPath, String basePath) {
        int index = fullPath.indexOf(basePath);
        if (index >= 0) {
            return fullPath.substring(index + basePath.length());
        }
        return fullPath;
    }
}
