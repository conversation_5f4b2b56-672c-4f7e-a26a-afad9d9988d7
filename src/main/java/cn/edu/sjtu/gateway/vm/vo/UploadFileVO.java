package cn.edu.sjtu.gateway.vm.vo;


import cn.edu.sjtu.gateway.fileupload.vo.NaiiUploadFileVO;
import lombok.NoArgsConstructor;

/**
 * 文件上传相关
 * <AUTHOR> cn.edu.sjtu.fileupload.
 */
@NoArgsConstructor
public class UploadFileVO extends NaiiUploadFileVO {
	/**
	 * 上传成功后的返回值
	 * @param fileName 上传成功后的文件名，如 "xnx3.jar"
	 * @param path 上传成功后的路径，如 "/jar/file/xnx3.jar"
	 * @param url 文件上传成功后，外网访问的url
	 */
	public UploadFileVO(String fileName,String path,String url) {
		this.name = fileName;
		this.path = path;
		this.url = url;
	}
	public String getFileName() {
		return name;
	}

	public void setFileName(String fileName) {
		this.name = fileName;
	}
}
