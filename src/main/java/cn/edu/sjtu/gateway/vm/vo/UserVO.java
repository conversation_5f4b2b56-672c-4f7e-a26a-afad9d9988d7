package cn.edu.sjtu.gateway.vm.vo;

import cn.edu.sjtu.gateway.vm.entity.User;
import cn.edu.sjtu.gateway.vm.system.responseBody.ResponseBodyManage;
import cn.edu.sjtu.gateway.vm.vo.BaseVO;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户
 * <AUTHOR>
 */
@Setter
@Getter
@ResponseBodyManage(ignoreField = {"password","salt","version"}, nullSetDefaultValue = true)
public class UserVO extends BaseVO {
	private User user;

}
