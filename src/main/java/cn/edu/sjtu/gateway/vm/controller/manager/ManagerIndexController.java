package cn.edu.sjtu.gateway.vm.controller.manager;

import cn.edu.sjtu.gateway.vm.controller.BaseController;
import cn.edu.sjtu.gateway.vm.entity.User;
import cn.edu.sjtu.gateway.vm.pluginManage.PluginManage;
import cn.edu.sjtu.gateway.vm.pluginManage.PluginRegister;
import cn.edu.sjtu.gateway.vm.pluginManage.interfaces.manage.SuperManagerIndexPluginManage;

import cn.edu.sjtu.gateway.vm.util.SessionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.InvocationTargetException;
import java.util.Map;

/**
 * 管理后台首页
 * <AUTHOR>
 */
@Controller(value="WMManagerIndexController")
@RequestMapping("/manager/index")
@Slf4j
public class ManagerIndexController extends BaseController{
	
	/**
	 * 管理后台首页
	 * @param jumpUrl 这里在应用插件里面，安装插件后要刷新页面，所以加入了jumpUrl，传入加载地址，如果有jumpUrl，那么默认页面就是访问这个。这里传入的地址，如 plugin/pluginManage/index.naii 
	 */
	@RequiresPermissions("managerIndexIndex")
	@RequestMapping("index${url.suffix}")
	public String index(HttpServletRequest request, Model model,
			@RequestParam(value = "jumpUrl", required = false , defaultValue="") String jumpUrl){
		//登录成功后，管理后台的主题页面，默认首页的url
		String url = "manager/index/welcome.naii";
		
		log.info("Manager 进入管理后台首页");
		
		//这里在应用插件里面，安装插件后要刷新页面，所以加入了jumpUrl，传入加载地址，如果有jumpUrl，那么默认页面就是访问这个。v4.11增加
		if(jumpUrl.length() > 2){
			url = jumpUrl;
		}
		
		//获取网站后台管理系统有哪些功能插件，也一块列出来,以直接在网站后台中显示出来
		StringBuilder pluginMenu = new StringBuilder();
		if(!PluginManage.superManagerClassManage.isEmpty()){
			for (Map.Entry<String, PluginRegister> entry : PluginManage.superManagerClassManage.entrySet()) {
				PluginRegister plugin = entry.getValue();
				pluginMenu.append("<dd><a id=\"").append(entry.getKey()).append("\" class=\"subMenuItem\" href=\"javascript:loadUrl('").append(plugin.menuHref()).append("'), notUseTopTools();\">").append(plugin.menuTitle()).append("</a></dd>");
			}
		}
		model.addAttribute("pluginMenu", pluginMenu.toString());
		
		//左侧菜单，根据shiro权限来显示
		model.addAttribute("menuHTML", SessionUtil.getUserMenuHTML());
		
		/**** 针对html追加的插件 ****/
		try {
			String pluginAppendHtml = SuperManagerIndexPluginManage.manage();
			model.addAttribute("pluginAppendHtml", pluginAppendHtml);
		} catch (InstantiationException | IllegalAccessException
				| NoSuchMethodException | SecurityException
				| IllegalArgumentException | InvocationTargetException e) {
			log.error( "错误信息：------>"+e);
		}
		
		User user = getUser();
		model.addAttribute("user", user);
		//是否使用日志,  true：使用，false不使用,旧的，jsp中请使用下面的 useActionLog 判断
		model.addAttribute("useSLS", true);
		//是否使用日志,  true：使用，false不使用
		model.addAttribute("useActionLog", true);
		return "/wm/manager/index/index";
	}
	

	/**
	 * 总管理后台，登陆成功后的欢迎页面
	 */
	@RequestMapping("welcome${url.suffix}")
	public String welcome(HttpServletRequest request, Model model){
		User user = getUser();
		log.info("总管理后台，登陆成功后的欢迎页面");
		
		model.addAttribute("user", user);
		return "/wm/manager/index/welcome";
	}
	
}
