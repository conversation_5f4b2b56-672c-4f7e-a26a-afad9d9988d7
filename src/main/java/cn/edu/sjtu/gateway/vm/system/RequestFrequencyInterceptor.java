package cn.edu.sjtu.gateway.vm.system;/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/22
 */

import cn.edu.sjtu.gateway.manager.util.CacheUtil;
import cn.edu.sjtu.gateway.vm.pluginManage.interfaces.SpringMVCInterceptorInterface;
import cn.edu.sjtu.gateway.vm.util.ApplicationPropertiesUtil;
import cn.edu.sjtu.gateway.tools.DateUtil;
import cn.edu.sjtu.gateway.tools.Lang;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @FileName RequestFrequencyInterceptor
 * @Description
 * <AUTHOR>
 * @date 2024-11-22
 **/
public class RequestFrequencyInterceptor implements HandlerInterceptor, SpringMVCInterceptorInterface {
    private static final Logger log = LoggerFactory.getLogger(RequestFrequencyInterceptor.class);

    // 配置参数
    private final String[] suffixes;
    private final int urlDelay;
    private final int ipRequestNumber;
    private final int ipDelay;
    private final int forbidTime;

    public RequestFrequencyInterceptor() {
        this.suffixes = loadSuffixes();
        this.urlDelay = loadConfig("naii.request.frequency.url.delay", 0);
        this.ipRequestNumber = loadConfig("naii.request.frequency.ip.requestNumber", 20);
        this.ipDelay = loadConfig("naii.request.frequency.ip.delay", 1000);
        this.forbidTime = loadConfig("naii.request.frequency.forbidTime", 1800000);

        log.info("Interceptor config: suffixes={}, urlDelay={}ms, ipRequestNumber={}, ipDelay={}ms, forbidTime={}ms",
                Arrays.toString(suffixes), urlDelay, ipRequestNumber, ipDelay, forbidTime);
    }

    private String[] loadSuffixes() {
        String suffixList = ApplicationPropertiesUtil.getProperty("naii.request.frequency.suffix", "jsp,json,naii");
        return Arrays.stream(suffixList.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .toArray(String[]::new);
    }

    private int loadConfig(String key, int defaultValue) {
        return Lang.stringToInt(ApplicationPropertiesUtil.getProperty(key), defaultValue);
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String url = request.getRequestURI();
        String suffix = Lang.findFileSuffix(url);
//        if (isSuffixProtected(suffix)) {
//            String ip = IpUtil.getIpAddress(request);
//            return !isUrlRequestTooFrequent(ip, url, suffix, response) && !isIpRequestTooFrequent(ip, suffix, response);
//        }
        return true;
    }

    private boolean isSuffixProtected(String suffix) {
        if (suffix == null || suffix.isEmpty()) {
            return false;
        }
        for (String s : suffixes) {
            if (suffix.equalsIgnoreCase(s)) {
                return true;
            }
        }
        return false;
    }

    private boolean isUrlRequestTooFrequent(String ip, String url, String suffix, HttpServletResponse response) throws IOException {
        String cacheKey = ip + ":" + url;
        long currentTime = DateUtil.timeForUnix13();

        RequestLog requestLog = (RequestLog) CacheUtil.get(cacheKey);
        if (requestLog != null) {
            if (isRequestForbidden(requestLog, currentTime, suffix, response, url)) {
                return true;
            }
            if (currentTime - requestLog.getTime() < urlDelay) {
                forbidRequest(requestLog, currentTime, cacheKey);
                responseWrite(response, suffix, "此URL" + url + "请求太快！");
                return true;
            }
        } else {
            requestLog = new RequestLog();
        }

        updateRequestLog(requestLog, currentTime, cacheKey);
        return false;
    }

    private boolean isIpRequestTooFrequent(String ip, String suffix, HttpServletResponse response) throws IOException {
        long currentTime = DateUtil.timeForUnix13();

        List<RequestLog> ipLogs = (List<RequestLog>) CacheUtil.get(ip);
        if (ipLogs == null) {
            ipLogs = new ArrayList<>();
        }

        if (!ipLogs.isEmpty() && ipLogs.get(ipLogs.size() - 1).getForbidTime() > currentTime) {
            responseWrite(response, suffix, "您的IP请求太频繁了！");
            return true;
        }

        if (!ipLogs.isEmpty() && ipLogs.size() >= ipRequestNumber) {
            long timeSpan = currentTime - ipLogs.get(0).getTime();
            if (timeSpan <= ipDelay) {
                forbidIpRequests(ipLogs, currentTime, ip);
                responseWrite(response, suffix, "您的IP请求太频繁了！");
                return true;
            }
            ipLogs.remove(0);
        }

        addIpRequestLog(ipLogs, currentTime, ip);
        return false;
    }

    private boolean isRequestForbidden(RequestLog requestLog, long currentTime, String suffix, HttpServletResponse response, String url) throws IOException {
        if (requestLog.getForbidTime() > currentTime) {
            responseWrite(response, suffix, "此URL" + url + "暂时被禁止！请稍后再试。");
            return true;
        }
        return false;
    }

    private void forbidRequest(RequestLog requestLog, long currentTime, String cacheKey) {
        requestLog.setTime(currentTime);
        requestLog.setForbidTime(currentTime + forbidTime);
        CacheUtil.setWeekCache(cacheKey, requestLog);
    }

    private void updateRequestLog(RequestLog requestLog, long currentTime, String cacheKey) {
        requestLog.setTime(currentTime);
        CacheUtil.setWeekCache(cacheKey, requestLog);
    }

    private void forbidIpRequests(List<RequestLog> ipLogs, long currentTime, String ip) {
        RequestLog log = new RequestLog();
        log.setTime(currentTime);
        log.setForbidTime(currentTime + forbidTime);
        ipLogs.add(log);
        CacheUtil.setWeekCache(ip, ipLogs);
    }

    private void addIpRequestLog(List<RequestLog> ipLogs, long currentTime, String ip) {
        RequestLog log = new RequestLog();
        log.setTime(currentTime);
        ipLogs.add(log);
        CacheUtil.setWeekCache(ip, ipLogs);
    }

    public void responseWrite(HttpServletResponse response, String suffix, String message) throws IOException {
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setContentType("text/html;charset=UTF-8");

        if ("json".equalsIgnoreCase(suffix)) {
            // 返回 JSON 格式的消息
            response.getWriter().write(String.format("{\"result\":\"failure\",\"info\":\"%s\"}", message));
        } else {
            // 返回包含弹出框的 HTML 页面
            response.getWriter().write("<html><head><title>Message</title></head><body>");
            response.getWriter().write("<script type=\"text/javascript\">");
            response.getWriter().write("alert('" + message + "');");
            response.getWriter().write("window.location.href = 'javascript:history.back()';");
            response.getWriter().write("</script>");
            response.getWriter().write("</body></html>");
        }
    }

    @Override
    public List<String> pathPatterns() {
        return Collections.singletonList("/**");
    }
}

@Getter
@Setter
@Data
class RequestLog implements Serializable {
    // 请求时间戳
    private long time;
    // 禁止访问的到期时间戳
    private long forbidTime;
}

