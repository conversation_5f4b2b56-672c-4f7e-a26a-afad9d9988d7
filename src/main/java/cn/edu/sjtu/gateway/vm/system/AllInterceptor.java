package cn.edu.sjtu.gateway.vm.system;

import cn.edu.sjtu.gateway.vm.Global;
import cn.edu.sjtu.gateway.vm.func.StaticResource;
import cn.edu.sjtu.gateway.vm.pluginManage.interfaces.SpringMVCInterceptorInterface;

import cn.edu.sjtu.gateway.vm.util.SystemUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 拦截器，对所有请求动作拦截
 *
 * <AUTHOR>
 */
@Component
public class AllInterceptor implements HandlerInterceptor, SpringMVCInterceptorInterface {

    private static final Logger log = LoggerFactory.getLogger(AllInterceptor.class);

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 设置系统参数 ATTACHMENT_FILE_URL（仅第一次访问时设置）
        initializeAttachmentFileUrl(request);
        return true;
    }

    private void initializeAttachmentFileUrl(HttpServletRequest request) {
        String attachmentFileUrlKey = "ATTACHMENT_FILE_URL";
        String existingUrl = SystemUtil.get(attachmentFileUrlKey);

        if (existingUrl == null || existingUrl.isEmpty()) {
            String generatedUrl = String.format("http://%s:%d/",
                    request.getServerName(), request.getServerPort());

            log.info("Project request URL initialized: {}", generatedUrl);

            synchronized (Global.system) {
                Global.system.put(attachmentFileUrlKey, generatedUrl);
            }
        }
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        // 设置静态资源路径
        setStaticResourcePath(request);

        // 添加响应头
        addCustomHeaders(response);
    }

    private void setStaticResourcePath(HttpServletRequest request) {
        String staticResourcePathKey = "STATIC_RESOURCE_PATH";
        if (request.getAttribute(staticResourcePathKey) == null) {
            String staticResourcePath = StaticResource.getPath();
            request.setAttribute(staticResourcePathKey, staticResourcePath);
        }
    }

    private void addCustomHeaders(HttpServletResponse response) {
        response.addHeader("Author", "naii");
        response.addHeader("Site", "naii.sjtu.edu.cn");
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 可以在此添加清理资源或日志记录的操作（如需）。
    }

    @Override
    public List<String> pathPatterns() {
        // 拦截所有路径
        return Collections.singletonList("/**");
    }
}
