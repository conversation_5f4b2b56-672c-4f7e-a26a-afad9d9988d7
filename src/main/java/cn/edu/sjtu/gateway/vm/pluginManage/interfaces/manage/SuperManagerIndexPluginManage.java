package cn.edu.sjtu.gateway.vm.pluginManage.interfaces.manage;

import cn.edu.sjtu.gateway.tools.ScanClassUtil;

import cn.edu.sjtu.gateway.vm.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * 超级管理后台首页的html源码处理
 * <AUTHOR>
 *
 */
@Component(value="PluginManageForSuperManagerIndex")
@Slf4j
public class SuperManagerIndexPluginManage {
	//处理html源代码的插件，这里开启项目时，便将有关此的插件加入此处
	public static List<Class<?>> classList;
	static{
		classList = new ArrayList<Class<?>>();
		
		new Thread(new Runnable() {
			public void run() {
				while(SpringUtil.getApplicationContext() == null){
					try {
						Thread.sleep(1000);
					} catch (InterruptedException e) {
						log.error("错误信息：------>"+e);
					}
				}
				
				//当 SpringUtil 被Spring 加载后才会执行
				List<Class<?>> allClassList = ScanClassUtil.getClasses("com.xnx3");
				allClassList.addAll(ScanClassUtil.getClasses("cn.zvo"));
				classList = ScanClassUtil.searchByInterfaceName(allClassList, "cn.edu.sjtu.gateway.vm.pluginManage.interfaces.SuperManagerIndexInterface");
				for (int i = 0; i < classList.size(); i++) {
					log.info("装载 SuperManagerIndex 插件："+classList.get(i).getName());
				}
			}
		}).start();
		
	}
	
	/**
	 * 超级管理后台追加的html
	 * @return 要追加到html最后面的 html代码
	 */
	public static String manage() throws InstantiationException, IllegalAccessException, IllegalArgumentException, InvocationTargetException, NoSuchMethodException, SecurityException{
		/**** 针对html源代码处理的插件 ****/
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < classList.size(); i++) {
			Class<?> c = classList.get(i);
			Object invoke = null;
			invoke = c.newInstance();
			//运用newInstance()来生成这个新获取方法的实例
			Method m = c.getMethod("superManagerIndexAppendHtml",new Class[]{});	//获取要调用的init方法
			//动态构造的Method对象invoke委托动态构造的InvokeTest对象，执行对应形参的add方法
			Object o = m.invoke(invoke, new Object[]{});
			if(o != null && !"null".equals(o)){
				sb.append(o.toString());
			}
		}
		return sb.toString();
	}
	
	
}
