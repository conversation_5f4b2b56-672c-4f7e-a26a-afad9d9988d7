package cn.edu.sjtu.gateway.vm.system;

import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.Context;
import org.apache.catalina.startup.Tomcat;
import org.apache.catalina.Wrapper;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.embedded.tomcat.TomcatWebServer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.File;

/**
 * JSP配置类 - 专门处理JAR包模式下的JSP支持
 * 
 * <AUTHOR>
 */
@Configuration
@ConditionalOnMissingBean(name = "tomcatServletWebServerFactory")
@Slf4j
public class JspConfiguration {
    
    /**
     * 自定义Tomcat工厂，增强JSP支持
     */
    @Bean
    public TomcatServletWebServerFactory tomcatServletWebServerFactory() {
        return new TomcatServletWebServerFactory() {
            
            @Override
            protected TomcatWebServer getTomcatWebServer(Tomcat tomcat) {
                // 创建临时目录用于JSP编译
                File baseDir = createTempDir("tomcat");
                tomcat.setBaseDir(baseDir.getAbsolutePath());

                // 设置文档根目录
                try {
                    // 尝试从classpath中获取webapp资源
                    String webappDir = this.getClass().getClassLoader().getResource("META-INF/resources").getPath();
                    if (webappDir != null) {
                        tomcat.getHost().setAppBase(webappDir);
                        log.info("设置webapp目录: {}", webappDir);
                    }
                } catch (Exception e) {
                    log.warn("无法设置webapp目录: {}", e.getMessage());
                }

                return super.getTomcatWebServer(tomcat);
            }
            
            @Override
            protected void postProcessContext(Context context) {
                super.postProcessContext(context);
                
                // 配置JSP支持
                configureJspSupport(context);
                
                log.info("JSP配置已应用到Tomcat上下文");
            }
        };
    }
    
    /**
     * 配置JSP支持
     */
    private void configureJspSupport(Context context) {
        // 1. 添加JSP Servlet并配置详细参数
        Wrapper jspServlet = Tomcat.addServlet(context, "jsp", "org.apache.jasper.servlet.JspServlet");
        jspServlet.addMapping("*.jsp");
        jspServlet.setLoadOnStartup(3);

        // 2. 设置JSP初始化参数
        jspServlet.addInitParameter("development", "true");
        jspServlet.addInitParameter("compilerSourceVM", "17");
        jspServlet.addInitParameter("compilerTargetVM", "17");
        jspServlet.addInitParameter("compiler", "modern");
        jspServlet.addInitParameter("modificationTestInterval", "1");
        jspServlet.addInitParameter("genStringAsCharArray", "true");
        jspServlet.addInitParameter("keepgenerated", "true");
        jspServlet.addInitParameter("mappedfile", "false");
        jspServlet.addInitParameter("trimSpaces", "false");
        jspServlet.addInitParameter("suppressSmap", "false");
        jspServlet.addInitParameter("dumpSmap", "false");
        jspServlet.addInitParameter("enablePooling", "true");
        jspServlet.addInitParameter("ieClassId", "clsid:8AD9C840-044E-11D1-B3E9-00805F499D93");

        // 3. 设置工作目录
        File tempDir = new File(System.getProperty("java.io.tmpdir"), "jsp-work-" + System.currentTimeMillis());
        if (!tempDir.exists()) {
            tempDir.mkdirs();
        }
        jspServlet.addInitParameter("scratchdir", tempDir.getAbsolutePath());

        // 4. 添加默认Servlet（处理静态资源）
        Wrapper defaultServlet = Tomcat.addServlet(context, "default", "org.apache.catalina.servlets.DefaultServlet");
        defaultServlet.addMapping("/");
        defaultServlet.setLoadOnStartup(1);

        // 5. 添加JSP相关的ServletContainerInitializer
        try {
            context.addServletContainerInitializer(
                new org.apache.jasper.servlet.JasperInitializer(), null);
            log.info("JSP初始化器已添加");
        } catch (Exception e) {
            log.error("添加JSP初始化器失败", e);
        }

        // 6. 设置类加载器
        context.setParentClassLoader(this.getClass().getClassLoader());

        // 7. 设置资源路径
        context.getServletContext().setAttribute("javax.servlet.context.tempdir", tempDir);

        log.info("JSP支持配置完成 - 工作目录: {}", tempDir.getAbsolutePath());
    }
    
    /**
     * 创建临时目录
     */
    private File createTempDir(String prefix) {
        try {
            File tempDir = File.createTempFile(prefix + ".", "." + System.currentTimeMillis());
            tempDir.delete();
            tempDir.mkdir();
            tempDir.deleteOnExit();
            return tempDir;
        } catch (Exception e) {
            throw new RuntimeException("Unable to create temp dir", e);
        }
    }
}