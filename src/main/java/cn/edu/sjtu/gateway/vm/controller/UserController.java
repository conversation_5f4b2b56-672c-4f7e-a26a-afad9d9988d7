package cn.edu.sjtu.gateway.vm.controller;

import cn.edu.sjtu.gateway.utils.PasswordUtil;
import cn.edu.sjtu.gateway.vm.Global;
import cn.edu.sjtu.gateway.vm.entity.User;
import cn.edu.sjtu.gateway.vm.service.SqlService;
import cn.edu.sjtu.gateway.vm.service.UserService;
import cn.edu.sjtu.gateway.vm.vo.BaseVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;

import static cn.edu.sjtu.gateway.utils.PasswordUtil.OLD_ALGORITHM_NAME;

/**
 * 用户User的相关操作
 *
 * <AUTHOR>
 */
@Controller(value = "WMUserController")
@RequestMapping("/user")
@Slf4j
public class UserController extends BaseController {

    private final UserService userService;
    private final SqlService sqlService;

    public UserController(UserService userService, SqlService sqlService) {
        this.userService = userService;
        this.sqlService = sqlService;
    }

    /**
     * 用户退出，页面跳转提示。
     */
    @RequestMapping("logout${url.suffix}")
    public String logout(Model model, HttpServletRequest request) {
        User user = getUser();
        log.info("{} 注销登录成功", user.getUsername());
        userService.logout();
        return success(model, "注销登录成功", "login.naii");
    }


    /**
     * 修改密码
     *
     * @param oldPassword 原密码
     * @param newPassword 新密码
     * @deprecated 使用 /manager/user/updatePassword.do
     */
    @RequiresPermissions("userUpdatePassword${url.suffix}")
    @RequestMapping(value = "updatePassword", method = RequestMethod.POST)
    public String updatePassword(HttpServletRequest request, String oldPassword, String newPassword, Model model) {
        if (oldPassword == null) {
            log.info("修改密码{}", "失败：未输入密码");
            return error(model, "请输入旧密码");
        } else {
            User uu = sqlService.findById(User.class, getUser().getId());
            //将输入的原密码进行加密操作，判断原密码是否正确

            if(PasswordUtil.hashPassword(oldPassword, uu.getSalt(), OLD_ALGORITHM_NAME, Global.USER_PASSWORD_SALT_NUMBER).equals(uu.getPassword())){
                BaseVO vo = userService.updatePassword(getUserId(), newPassword);
                if(vo.getResult() - BaseVO.SUCCESS == 0){
                    log.info("修改密码{}", "成功");
                    return success(model, "修改成功");
                }else{
                    log.info("修改密码{}", "失败："+vo.getInfo());
                    return error(model, vo.getInfo());
                }
            }else{
                log.info("修改密码{}", "失败：原密码错误");
                return error(model, "原密码错误！");
            }
        }
    }

}
