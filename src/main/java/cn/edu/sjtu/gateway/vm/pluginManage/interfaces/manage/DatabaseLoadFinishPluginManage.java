package cn.edu.sjtu.gateway.vm.pluginManage.interfaces.manage;

import cn.edu.sjtu.gateway.tools.ScanClassUtil;
import cn.edu.sjtu.gateway.vm.Global;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * Shiro 权限，哪个url、目录需要登录，哪个不需要登录，在这里修改
 * <AUTHOR>
 *
 */
@Component(value="PluginManageForDatabaseLoadFinish")
@Slf4j
public class DatabaseLoadFinishPluginManage {
	//处理html源代码的插件，这里开启项目时，便将有关此的插件加入此处
	public static List<Class<?>> classList;
	static{
		classList = new ArrayList<Class<?>>();
		
		new Thread(new Runnable() {
			@Override
			public void run() {
				while(Global.system.isEmpty()){
					try {
						Thread.sleep(1000);
					} catch (InterruptedException e) {
						log.error("错误信息：------>"+e);
					}
				}
				
				//当 system数据表 加载后才会执行
				List<Class<?>> allClassList = ScanClassUtil.getClasses("com.xnx3");
				allClassList.addAll(ScanClassUtil.getClasses("cn.zvo"));
				classList = ScanClassUtil.searchByInterfaceName(allClassList, "cn.edu.sjtu.gateway.vm.pluginManage.interfaces.DatabaseLoadFinishInterface");
                for (Class<?> aClass : classList) {
                    log.info("装载 DatabaseLoadFinish 插件：" + aClass.getName());
                }
				
				new DatabaseLoadFinishThread().start();
			}
		}).start();
		
	}
	
}
@Slf4j
class DatabaseLoadFinishThread extends Thread{
	
	@Override
	public void run() {
		
		for (int i = 0; i < DatabaseLoadFinishPluginManage.classList.size(); i++) {
			try {
				Class<?> c = DatabaseLoadFinishPluginManage.classList.get(i);
				Object invoke = null;
				invoke = c.newInstance();
				//运用newInstance()来生成这个新获取方法的实例
				Method m = c.getMethod("databaseLoadFinish",new Class[]{});	//获取要调用的init方法
				//动态构造的Method对象invoke委托动态构造的InvokeTest对象，执行对应形参的add方法
				m.invoke(invoke, new Object[]{});
			} catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException
					| InstantiationException | NoSuchMethodException | SecurityException e) {
				log.error("错误信息：------>"+e);
			}	
		}
		
	}
	
}
