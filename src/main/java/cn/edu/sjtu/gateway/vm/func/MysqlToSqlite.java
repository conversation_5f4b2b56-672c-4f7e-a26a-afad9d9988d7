package cn.edu.sjtu.gateway.vm.func;


import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

/**
 * 将mysql的sql文件，转化为 Sqlite可用的sql文件
 * <AUTHOR>
 */
@Slf4j
public class MysqlToSqlite {
    public static void read(String sourcePath, String destionPath) throws Exception {
        InputStreamReader read = new InputStreamReader(new FileInputStream(sourcePath), StandardCharsets.UTF_8);
        FileWriter fw = new FileWriter(destionPath);
        BufferedReader reader = new BufferedReader(read);
        BufferedWriter writer = new BufferedWriter(fw);

        String s = null;
        StringBuilder sb = new StringBuilder();
        while ((s = reader.readLine()) != null) {
            String replace = null;
            String result = null;
            if (s.contains("SET FOREIGN_KEY_CHECKS=0;")) {
                replace = "SET FOREIGN_KEY_CHECKS=0;";
                result = "\r\n";
            } else if (s.contains("NOT NULL AUTO_INCREMENT")) {
                replace = "NOT NULL AUTO_INCREMENT";
                result = "PRIMARY KEY NOT NULL";
            } else if (s.contains("PRIMARY KEY (`id`)")) {
                replace = "";
            } else if (s.contains("ENGINE=InnoDB AUTO_INCREMENT") || s.contains(") ENGINE=InnoDB DEFAULT CHARSET=utf8")) {
                s = ");";
            }

            if (");".equals(s)) {
                String resultStr = sb.toString();
                sb = new StringBuilder();
                sb.append(resultStr, 0, resultStr.lastIndexOf(","));
            }

            if (replace != null && result != null)
                s = s.replace(replace, result);
            if (!"".equals(replace)) {
                sb.append(s).append("\r\n");
            }

        }
        log.info("" + sb);
        writer.write(sb.toString());
        reader.close();
        writer.close();

    }


}
