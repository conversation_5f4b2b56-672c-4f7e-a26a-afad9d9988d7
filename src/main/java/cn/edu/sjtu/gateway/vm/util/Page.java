package cn.edu.sjtu.gateway.vm.util;

import cn.edu.sjtu.gateway.vm.util.bean.NaiiTagA;
import cn.edu.sjtu.gateway.tools.Lang;
import org.springframework.beans.BeanUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * 分页
 * <AUTHOR>
 *
 */
public class Page extends NaiiPage{

	/*** 上面这些的作用是能使用javadoc自动生成文档 ***/

	/**
	 * @param allRecordNumber 共多少条
	 * @param everyNumber 每页多少条
	 * @param request HttpServletRequest
	 * 			get方式传入值：
	 * 			<ul>
	 * 				<li>currentPage：请求第几页。若不传，默认请求第一页</li>
	 * 				<li>orderBy：排序方式，如"user.id_DESC"，若不传则sql不会拼接上ORDER BY</li>
	 * 			</ul>
	 * 			<p>如果不传，则分页数据中的超链接相关不会显示<p>
	 */
	public Page(int allRecordNumber, int everyNumber, HttpServletRequest request) {
		this.setAllRecordNumber(allRecordNumber);
		this.setEveryNumber(everyNumber);
		
		if(request != null) {
			String url = request.getRequestURL().toString();
			String queryString = request.getQueryString();
			if(queryString != null && !queryString.isEmpty()) {
				url = url + "?" + queryString;
			}
			this.setUrl(url);
			this.setCurrentPageNumber(Lang.stringToInt(request.getParameter("currentPage"), 1));
		}else {
			this.setCurrentPageNumber(1);
		}
	}
	
	/**
	 * 当前页默认赋予第一页
	 * @param allRecordNumber 共多少条
	 * @param everyNumber 每页多少条
	 */
	public Page(int allRecordNumber, int everyNumber) {
		this.setAllRecordNumber(allRecordNumber);
		this.setEveryNumber(everyNumber);
		this.setCurrentPageNumber(1);
	}
	
	/**
	 * 设置
	 * @param page
	 */
	public Page(NaiiPage page, HttpServletRequest request) {
		if(page == null) {
			return;
		}
		BeanUtils.copyProperties(page, this);
		
		if(request != null) {
			String url = request.getRequestURL().toString();
			String queryString = request.getQueryString();
			if(queryString != null && !queryString.isEmpty()) {
				url = url + "?" + queryString;
			}
			this.setUrl(url);
		}
	}
}
