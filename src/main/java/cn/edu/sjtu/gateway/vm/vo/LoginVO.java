package cn.edu.sjtu.gateway.vm.vo;

import cn.edu.sjtu.gateway.vm.entity.User;
import cn.edu.sjtu.gateway.vm.system.responseBody.ResponseBodyManage;
import cn.edu.sjtu.gateway.vm.vo.BaseVO;
import lombok.Getter;
import lombok.Setter;

/**
 * 登陆接口的返回值
 * <AUTHOR>
 */
@Setter
@Getter
@ResponseBodyManage(ignoreField = {"password","salt","version"}, nullSetDefaultValue = true)
public class LoginVO extends BaseVO {
	//sessionid
	private String token;
	//登录的用户信息
	private User user;

}
