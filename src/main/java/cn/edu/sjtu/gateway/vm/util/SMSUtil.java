package cn.edu.sjtu.gateway.vm.util;

import cn.edu.sjtu.tools.Lang;
import cn.edu.sjtu.gateway.vm.util.ApplicationPropertiesUtil;
import cn.edu.sjtu.gateway.vm.util.ConsoleUtil;
import cn.edu.sjtu.gateway.vm.vo.BaseVO;

/**
 * 短信发送
 * <AUTHOR>
 *
 */
public class SMSUtil {
	private static cn.edu.sjtu.tools.SMSUtil smsUtil;
	private static int uid;
	private static String password;
	
	static {
		String uids = ApplicationPropertiesUtil.getProperty("sms.uid");
		uid = Lang.stringToInt(uids, 0);
		password = ApplicationPropertiesUtil.getProperty("sms.password");
		if(uid < 1) {
			ConsoleUtil.debug("sms.uid is not set!");
		}else {
			smsUtil = new cn.edu.sjtu.tools.SMSUtil(uid, password);
		}
	}
	
	/**
	 * 发送一条短信。短信内容自己定，不过前缀会加上签名。
	 * @param phone 接收短信的手机号
	 * @param content 发送短信的内容。不加签名，比如这里传入“哈哈哈”,那么用户接收到的短信为 
	 * 		<pre>
	 * 		</pre>
	 * @return 其中 {@link cn.edu.sjtu.gateway.vm.vo.BaseVO#getResult()} 为执行状态，是否成功
	 * 		<ul>
	 *	 		<li>{@link cn.edu.sjtu.gateway.vm.vo.BaseVO#SUCCESS} 	：失败,可以通过 {@link cn.edu.sjtu.gateway.vm.vo.BaseVO#getInfo()} 获得失败原因 </li>
	 * 			<li>{@link cn.edu.sjtu.gateway.vm.vo.BaseVO#FAILURE} 	：成功,可以通过 {@link cn.edu.sjtu.gateway.vm.vo.BaseVO#getInfo()} 获得发送的这个短信的消息唯一编号</li>
	 * 		</ul>
	 */
	public static BaseVO send(String phone, String content){
		cn.edu.sjtu.tools.BaseVO sendVO = smsUtil.send(phone, content);
		if(sendVO.getResult() - BaseVO.FAILURE == 0) {
			return BaseVO.failure(sendVO.getInfo());
		}else {
			return BaseVO.success(sendVO.getInfo());
		}
	}
}
