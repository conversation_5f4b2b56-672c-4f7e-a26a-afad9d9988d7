package cn.edu.sjtu.gateway.vm.service.impl;

import cn.edu.sjtu.gateway.utils.PasswordUtil;
import cn.edu.sjtu.gateway.vm.Global;
import cn.edu.sjtu.gateway.vm.dao.SqlDAO;
import cn.edu.sjtu.gateway.vm.entity.User;
import cn.edu.sjtu.gateway.vm.entity.UserRole;
import cn.edu.sjtu.gateway.vm.service.UserService;
import cn.edu.sjtu.gateway.vm.shiro.ShiroFunc;
import cn.edu.sjtu.gateway.vm.util.AttachmentUtil;

import cn.edu.sjtu.gateway.utils.IpUtils;
import cn.edu.sjtu.gateway.vm.util.LanguageUtil;
import cn.edu.sjtu.gateway.vm.util.SafetyUtil;
import cn.edu.sjtu.gateway.vm.util.SessionUtil;
import cn.edu.sjtu.gateway.vm.util.Sql;
import cn.edu.sjtu.gateway.vm.util.SystemUtil;
import cn.edu.sjtu.gateway.vm.vo.BaseVO;
import cn.edu.sjtu.gateway.vm.vo.UploadFileVO;
import cn.edu.sjtu.gateway.tools.DateUtil;
import cn.edu.sjtu.gateway.tools.Lang;
import cn.edu.sjtu.gateway.tools.StringUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.ExcessiveAttemptsException;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.apache.shiro.authc.LockedAccountException;
import org.apache.shiro.authc.UnknownAccountException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.subject.Subject;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.Random;

import static cn.edu.sjtu.gateway.utils.PasswordUtil.OLD_ALGORITHM_NAME;

@Setter
@Getter
@Slf4j
public class UserServiceImpl implements UserService {

    @Resource
    private SqlDAO sqlDAO;

    @Override
    public User findByPhone(Object phone) {
        List<User> list = sqlDAO.findByProperty(User.class, "phone", phone);
        if (!list.isEmpty()) {
            return list.get(0);
        } else {
            return null;
        }
    }

    /**
     * 登陆
     *
     * @param request {@link javax.servlet.http.HttpServletRequest}
     *                <br/>登陆时form表单需提交两个参数：username(用户名/邮箱)、password(密码)
     * @return {@link cn.edu.sjtu.gateway.vm.vo.BaseVO}，如果成功，那么info返回 user.id
     */
    @Override
    public BaseVO loginByUsernameAndPassword(HttpServletRequest request) {
        String username = request.getParameter("username");
        String password = request.getParameter("password");
        return loginByUsernameAndPassword(request, username, password);
    }

    @Override
    public BaseVO loginByUsernameAndPassword(HttpServletRequest request, String username, String password) {
        username = SafetyUtil.filter(username);

        BaseVO baseVO = new BaseVO();
        if (username == null || username.isEmpty()) {
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_loginUserOrEmailNotNull"));
            return baseVO;
        }
        if (password == null || password.isEmpty()) {
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_loginPasswordNotNull"));
            return baseVO;
        }

        //判断是用户名还是邮箱登陆的，进而查询邮箱或者用户名，进行登录
        List<User> l = sqlDAO.findByProperty(User.class, username.contains("@") ? "email" : "username", username);

        if (l != null && !l.isEmpty()) {
            User user = l.get(0);
            String md5Password = PasswordUtil.hashPassword(password, user.getSalt(), OLD_ALGORITHM_NAME, Global.USER_PASSWORD_SALT_NUMBER);
            //检验密码是否正确
            if (md5Password.equals(user.getPassword())) {
                //检验此用户状态是否正常，是否被冻结
                if (user.getIsfreeze().equals(User.ISFREEZE_FREEZE)) {
                    baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_loginUserFreeze"));
                    return baseVO;
                }

                String ip = SafetyUtil.filter(IpUtils.getIpAddress(request));
                sqlDAO.executeSql("UPDATE user SET lasttime = " + DateUtil.timeForUnix10() + ", lastip = '" + ip + "' WHERE id = " + user.getId());

                UsernamePasswordToken token = new UsernamePasswordToken(user.getUsername(), user.getUsername());
                Subject currentUser = SecurityUtils.getSubject();
                try {
                    currentUser.login(token);
                } catch (UnknownAccountException uae) {
                    log.info("UnknownAccountException:" + uae.getMessage());
                } catch (IncorrectCredentialsException ice) {
                    log.info("IncorrectCredentialsException:" + ice.getMessage());
                } catch (LockedAccountException lae) {
                    log.info("LockedAccountException:" + lae.getMessage());
                } catch (ExcessiveAttemptsException eae) {
                    log.info("ExcessiveAttemptsException:" + eae.getMessage());
                } catch (org.apache.shiro.authc.AuthenticationException ae) {
                    log.info("AuthenticationException:" + ae.getMessage());
                }
                baseVO.setBaseVO(BaseVO.SUCCESS, user.getId() + "");
            } else {
                baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_loginPasswordFailure"));
            }
        } else {
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_loginUserNotFind"));
        }

        return baseVO;
    }

    /**
     * 注册
     *
     * @param user    {@link cn.edu.sjtu.gateway.vm.entity.User}
     *                <br/>表单的用户名(username)、 密码(password)为必填项
     * @param request {@link javax.servlet.http.HttpServletRequest}
     * @return {@link cn.edu.sjtu.gateway.vm.vo.BaseVO}
     */
    @Override
    public BaseVO reg(User user, HttpServletRequest request) {
        BaseVO baseVO = new BaseVO();
        user.setEmail(SafetyUtil.xssFilter(user.getEmail()));
        user.setNickname(SafetyUtil.xssFilter(user.getNickname()));
        user.setPhone(SafetyUtil.xssFilter(user.getPhone()));
        user.setUsername(SafetyUtil.xssFilter(user.getUsername()));
        if (user.getNickname() == null || "".equals(user.getNickname())) {
            user.setNickname(user.getUsername());
        }

        //判断用户名、邮箱、手机号是否有其中为空的
        if (user.getUsername() == null || "".equals(user.getUsername()) || user.getPassword() == null || "".equals(user.getPassword())) {
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_regDataNotAll"));
        }

        //判断用户名、邮箱、手机号是否有其中已经注册了，唯一性
        //邮箱的唯一，仅当邮箱设置了之后，才会判断邮箱的唯一性
        if (user.getEmail() != null && !user.getEmail().isEmpty()) {
            if (!sqlDAO.findByProperty(User.class, "email", user.getEmail()).isEmpty()) {
                baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_regFailureForEmailAlreadyExist"));
                return baseVO;
            }
        }

        //判断用户名唯一性
        if (!sqlDAO.findByProperty(User.class, "username", user.getUsername()).isEmpty()) {
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_regFailureForUsernameAlreadyExist"));
            return baseVO;
        }

        //判断手机号唯一性
        if (user.getPhone() != null && !user.getPhone().isEmpty()) {
            if (findByPhone(user.getUsername()) != null) {
                baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_regFailureForPhoneAlreadyExist"));
                return baseVO;
            }
        }

        user.setRegip(IpUtils.getIpAddress(request));
        user.setLastip(IpUtils.getIpAddress(request));
        user.setRegtime(DateUtil.timeForUnix10());
        user.setLasttime(DateUtil.timeForUnix10());
        user.setAuthority(Global.system.get("USER_REG_ROLE"));
        user.setCurrency(0);
        user.setReferrerid(0);
        user.setFreezemoney(0);
        user.setMoney(0);
        user.setIsfreeze(User.ISFREEZE_NORMAL);
        user.setHead("default.png");

        String inviteid = null;
        if (request.getSession().getAttribute("inviteid") != null) {
            inviteid = request.getSession().getAttribute("inviteid").toString();
        }

        User referrerUser1 = null;
        if (inviteid != null && !inviteid.isEmpty()) {
            int referrerid = Lang.stringToInt(inviteid, 0);
            referrerUser1 = sqlDAO.findById(User.class, referrerid);    //一级下线
            if (referrerUser1 != null) {
                user.setReferrerid(referrerid);
            }
        }


        if (user.getUsername().length() > 40) {
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_userNameToLong"));
        }

        Random random = new Random();
        user.setSalt(random.nextInt(10) + "" + random.nextInt(10) + random.nextInt(10) + random.nextInt(10));
        String md5Password = PasswordUtil.hashPassword(user.getPassword(), user.getSalt(), OLD_ALGORITHM_NAME, Global.USER_PASSWORD_SALT_NUMBER);
        user.setPassword(md5Password);

        sqlDAO.save(user);
        if (user.getId() > 0) {
            //已注册成功，自动登录成用户
            UsernamePasswordToken token = new UsernamePasswordToken(user.getUsername(), user.getUsername());
            token.setRememberMe(false);
            Subject currentUser = SecurityUtils.getSubject();

            try {
                currentUser.login(token);
            } catch (AuthenticationException ignored) {
            }

            //赋予该用户系统设置的默认角色
            UserRole userRole = new UserRole();
            userRole.setRoleid(Lang.stringToInt(Global.system.get("USER_REG_ROLE"), 0));
            userRole.setUserid(user.getId());
            sqlDAO.save(userRole);

            //推荐人增加奖励
            if (user.getReferrerid() > 0) {    //是否有直接推荐人
                referrerAddAward(referrerUser1, Global.system.get("INVITEREG_AWARD_ONE"), user);

                if (referrerUser1 != null && referrerUser1.getReferrerid() > 0) {    //一级下线有上级推荐人，拿到二级下线
                    User referrerUser2 = sqlDAO.findById(User.class, referrerUser1.getReferrerid());
                    if (referrerUser2 != null) {
                        referrerAddAward(referrerUser2, Global.system.get("INVITEREG_AWARD_TWO"), user);

                        if (referrerUser2.getReferrerid() > 0) {    //二级下线有上级推荐人，拿到三级下线
                            User referrerUser3 = sqlDAO.findById(User.class, referrerUser2.getReferrerid());
                            if (referrerUser3 != null) {
                                referrerAddAward(referrerUser3, Global.system.get("INVITEREG_AWARD_THREE"), user);

                                if (referrerUser3.getReferrerid() > 0) {    //三级下线有上级推荐人，拿到四级下线
                                    User referrerUser4 = sqlDAO.findById(User.class, referrerUser3.getReferrerid());
                                    if (referrerUser4 != null) {
                                        referrerAddAward(referrerUser4, Global.system.get("INVITEREG_AWARD_FOUR"), user);
                                    }
                                }
                            }
                        }
                    }
                }
            }

//				logDao.insert("USER_REGISTER_SUCCESS");
            baseVO.setBaseVO(BaseVO.SUCCESS, user.getId() + "");
        } else {
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_regFailure"));
        }

        return baseVO;
    }

    /**
     * 注册成功后下线充值奖励
     *
     * @param user         要充值的下线user
     * @param addCurrency_ 增加的货币值
     * @param regUser      注册的用户的用户名
     */
    private void referrerAddAward(User user, String addCurrency_, User regUser) {
        int addCurrency = Lang.stringToInt(addCurrency_, 0);
        if (addCurrency > 0) {
            user.setCurrency(user.getCurrency() + addCurrency);
            sqlDAO.save(user);
//			logDao.insert(regUser.getId(), "USER_INVITEREG_AWARD", addCurrency+"");
        }
    }

    /**
     * 注册
     * <br/>表单的用户名(username)、 密码(password)为必填项
     *
     * @param request {@link javax.servlet.http.HttpServletRequest}
     * @return {@link cn.edu.sjtu.gateway.vm.vo.BaseVO}
     */
    @Override
    public void regInit(HttpServletRequest request) {
        String inviteid_ = request.getParameter("inviteid");
        if (inviteid_ != null && !inviteid_.isEmpty()) {
            int inviteid = Lang.stringToInt(inviteid_, 0);

            User user = sqlDAO.findById(User.class, inviteid);
            if (user != null) {
                request.getSession().setAttribute("inviteid", inviteid);    //邀请人id
            }
        }
    }

    /**
     * 手机号登陆，会自动检测上次登陆的ip，若上次登陆的ip跟当前的ip一样，则这个手机用户登陆成功
     *
     * @param request {@link javax.servlet.http.HttpServletRequest}
     *                <br/>登陆时form表单需提交两个参数：phone(手机号)、code(手机收到的动态验证码)
     * @return {@link cn.edu.sjtu.gateway.vm.vo.BaseVO}
     */
    @Override
    public BaseVO loginByPhone(HttpServletRequest request) {
        BaseVO baseVO = new BaseVO();
        String phone = SafetyUtil.xssFilter(request.getParameter("phone"));
        if (phone == null) {
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_loginByPhonePhoneFailure"));
            return baseVO;
        } else {
            phone = phone.replaceAll(" ", "");
            if (phone.length() != 11) {
                baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_loginByPhonePhoneFailure"));
                return baseVO;
            }
        }

        User user = findByPhone(phone);
        if (user == null) {
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_loginByPhoneUserNotFind"));
            return baseVO;
        }

        //ip检测
        String ip = IpUtils.getIpAddress(request);
        if (!(user.getLastip().equals(ip) || user.getRegip().equals(ip))) {
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_loginByPhoneIpFailure"));
            return baseVO;
        }

        //检验此用户状态是否正常，是否被冻结
        if (user.getIsfreeze().equals(User.ISFREEZE_FREEZE)) {
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_loginUserFreeze"));
            return baseVO;
        }
        log.debug("检验此用户状态是否正常，是否被冻结，未冻结，正常");

        /*******更改User状态******/
        ip = SafetyUtil.filter(ip);
        sqlDAO.executeSql("UPDATE user SET lasttime = " + DateUtil.timeForUnix10() + ", lastip = '" + ip + "' WHERE id = " + user.getId());

        UsernamePasswordToken token = new UsernamePasswordToken(user.getUsername(), user.getUsername());
        token.setRememberMe(false);
        Subject currentUser = SecurityUtils.getSubject();

        try {
            currentUser.login(token);
        } catch (AuthenticationException ae) {
            log.error("错误信息：------>" + ae);
        }

//		logDao.insert("USER_LOGIN_SUCCESS");
        baseVO.setBaseVO(BaseVO.SUCCESS, LanguageUtil.show("user_loginSuccess"));
        return baseVO;
    }

    @Override
    public void logout() {
        SessionUtil.logout();
    }

    @Override
    public BaseVO freezeUser(int id) {
        BaseVO baseVO = new BaseVO();
        if (id > 0) {
            User user = sqlDAO.findById(User.class, id);
            if (user == null) {
                baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_freezeUserIsNotFind"));
            } else {
                user.setIsfreeze(User.ISFREEZE_FREEZE);
                sqlDAO.save(user);
            }
        } else {
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_freezeUserPleaseEntryId"));
        }

        return baseVO;
    }

    @Override
    public BaseVO unfreezeUser(int id) {
        BaseVO baseVO = new BaseVO();
        if (id > 0) {
            User user = sqlDAO.findById(User.class, id);
            if (user == null) {
                baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_unfreezeUserIsNotFind"));
            } else {
                user.setIsfreeze(User.ISFREEZE_NORMAL);
                sqlDAO.save(user);
            }
        } else {
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_unfreezeUserPleaseEntryId"));
        }
        return baseVO;
    }

    @Override
    public UploadFileVO updateHeadByOSS(MultipartFile head) {
        UploadFileVO uploadFileVO = new UploadFileVO();
        if (head == null || head.isEmpty()) {
            uploadFileVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_uploadHeadImageNotFind"));
            return uploadFileVO;
        }

        User user = ShiroFunc.getUser();
        String fileSuffix = "png";
        fileSuffix = Lang.findFileSuffix(SafetyUtil.filter(head.getOriginalFilename()));
        String newHead = Lang.uuid() + "." + fileSuffix;
        try {
            uploadFileVO = AttachmentUtil.uploadFile(SystemUtil.get("USER_HEAD_PATH") + newHead, head.getInputStream());
        } catch (IOException e) {
            log.error("错误信息：------>" + e);
            uploadFileVO.setBaseVO(BaseVO.FAILURE, e.getMessage());
            return uploadFileVO;
        }

        User u = sqlDAO.findById(User.class, user.getId());
        //删除之前的头像
        if (u.getHead() != null && !u.getHead().isEmpty() && !"default.png".equals(u.getHead())) {
            AttachmentUtil.deleteObject("image/head/" + u.getHead());
        }

        u.setHead(newHead);
        sqlDAO.save(u);
        Objects.requireNonNull(ShiroFunc.getUser()).setHead(newHead);

//		logDao.insert("USER_UPDATEHEAD");

        return uploadFileVO;
    }

    @Override
    public BaseVO updateSex(HttpServletRequest request) {
        BaseVO baseVO = new BaseVO();
        String sex = SafetyUtil.filter(request.getParameter("sex"));
        if (sex == null) {
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_updateSexSexNotIsNull"));
            return baseVO;
        }
        if (!("男".equals(sex) || "女".equals(sex))) {
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_updateSexEntryFailure"));
            return baseVO;
        }
        User u = sqlDAO.findById(User.class, ShiroFunc.getUser().getId());
        u.setSex(sex);
        sqlDAO.save(u);
//		logDao.insert("USER_UPDATE_SEX", ShiroFunc.getUser().getSex()+"修改为"+sex);
        ShiroFunc.getUser().setSex(sex);

        return baseVO;
    }

    @Override
    public BaseVO updateNickname(HttpServletRequest request) {
        BaseVO baseVO = new BaseVO();
        String nickname = StringUtil.filterXss(request.getParameter("nickname"));
        if (nickname == null) {
            nickname = "";
        }
        if (nickname.isEmpty()) {
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_updateNicknameNotNull"));
            return baseVO;
        }
        if (nickname.length() > 15) {
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_updateNicknameSizeFailure"));
            return baseVO;
        }

        User u = sqlDAO.findById(User.class, ShiroFunc.getUser().getId());
        u.setNickname(nickname);
        sqlDAO.save(u);
        ShiroFunc.getUser().setNickname(nickname);
        baseVO.setInfo(nickname);

        return baseVO;
    }

    @Override
    public BaseVO updateSign(HttpServletRequest request) {
        BaseVO baseVO = new BaseVO();
        String sign = request.getParameter("sign");
        if (sign == null) {
            sign = "";
        }
        //过滤html标签、sql注入、xss
        sign = SafetyUtil.filter(StringUtil.filterHtmlTag(sign));
        if (sign.length() > 40) {
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_updateSignSizeFailure"));
            return baseVO;
        }

        User u = sqlDAO.findById(User.class, ShiroFunc.getUser().getId());
        u.setSign(sign);
        sqlDAO.save(u);
        ShiroFunc.getUser().setSign(sign);

        return baseVO;
    }

    @Override
    public UploadFileVO updateHeadByOSS(HttpServletRequest request, String formFileName) {
        return updateHeadByOSS(request, formFileName, 0);
    }

    @Override
    public UploadFileVO updateHeadByOSS(HttpServletRequest request,
                                        String formFileName, int maxWidth) {
        UploadFileVO uploadFileVO = new UploadFileVO();
        MultipartFile multipartFile = null;
        if (request instanceof MultipartHttpServletRequest multipartRequest) {
            List<MultipartFile> imageList = multipartRequest.getFiles(formFileName);
            if (imageList.isEmpty()) {
                log.debug("上传头像时，未发现头像 ------" + LanguageUtil.show("user_uploadHeadImageNotFind"));
                uploadFileVO.setResult(UploadFileVO.NOTFILE);
                uploadFileVO.setInfo(LanguageUtil.show("user_uploadHeadImageNotFind"));
                return uploadFileVO;
            } else {
                log.debug("上传头像，已发现头像的multipartFile");
                multipartFile = imageList.get(0);
            }
        }

        if (multipartFile == null || multipartFile.isEmpty()) {
            uploadFileVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_uploadHeadImageNotFind"));
            log.debug("上传头像的multipartFile为空，不存在上传的头像 ------" + LanguageUtil.show("user_uploadHeadImageNotFind"));
            return uploadFileVO;
        }

        User user = ShiroFunc.getUser();
        uploadFileVO = AttachmentUtil.uploadFile(SystemUtil.get("USER_HEAD_PATH"), multipartFile);
        if (uploadFileVO.getResult() - UploadFileVO.FAILURE == 0) {
            return uploadFileVO;
        }

        User u = sqlDAO.findById(User.class, user.getId());
        //删除之前的头像
        if (u.getHead() != null && !u.getHead().isEmpty() && !"default.png".equals(u.getHead())) {
            AttachmentUtil.deleteObject(SystemUtil.get("USER_HEAD_PATH") + u.getHead());
        }

        u.setHead(uploadFileVO.getFileName());
        sqlDAO.save(u);
        ShiroFunc.getUser().setHead(uploadFileVO.getFileName());

        return uploadFileVO;
    }

    @Override
    public BaseVO loginByUserid(HttpServletRequest request, int userid) {
        BaseVO baseVO = new BaseVO();

        User user = sqlDAO.findById(User.class, userid);
        if (user == null) {
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_loginByPhoneUserNotFind"));
            return baseVO;
        }

        //ip检测
        String ip = IpUtils.getIpAddress(request);
        if (!(user.getLastip().equals(ip) || user.getRegip().equals(ip))) {
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_loginByPhoneIpFailure"));
            return baseVO;
        }

        //检验此用户状态是否正常，是否被冻结
        if (user.getIsfreeze() == User.ISFREEZE_FREEZE) {
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_loginUserFreeze"));
            return baseVO;
        }

        /*******更改User状态******/
        user.setLasttime(DateUtil.timeForUnix10());
        user.setLastip(IpUtils.getIpAddress(request));
        sqlDAO.save(user);

        UsernamePasswordToken token = new UsernamePasswordToken(user.getUsername(), user.getUsername());
        token.setRememberMe(false);
        Subject currentUser = SecurityUtils.getSubject();

        try {
            currentUser.login(token);
        } catch (AuthenticationException ae) {
            log.error("错误信息：------>" + ae);
        }

//		logDao.insert("USER_LOGIN_SUCCESS");
        baseVO.setBaseVO(BaseVO.SUCCESS, LanguageUtil.show("user_loginSuccess"));
        return baseVO;
    }

    @Override
    public BaseVO loginForUserId(HttpServletRequest request, int userId) {
        BaseVO baseVO = new BaseVO();
        User user = sqlDAO.findById(User.class, userId);
        if (user == null) {
            log.debug("用户不存在");
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_loginByPhoneUserNotFind"));
            return baseVO;
        }

        //检验此用户状态是否正常，是否被冻结
        if (user.getIsfreeze() == User.ISFREEZE_FREEZE) {
            log.debug("此用户被冻结，无法设置为登陆用户");
            baseVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_loginUserFreeze"));
            return baseVO;
        }

        /*******更改User最后一次登录的ip、登录时间******/
        String ip = SafetyUtil.filter(IpUtils.getIpAddress(request));
        sqlDAO.executeSql("UPDATE user SET lasttime = " + DateUtil.timeForUnix10() + ", lastip = '" + ip + "' WHERE id = " + user.getId());

        UsernamePasswordToken token = new UsernamePasswordToken(user.getUsername(), user.getUsername());
        token.setRememberMe(false);
        Subject currentUser = SecurityUtils.getSubject();

        try {
            currentUser.login(token);
        } catch (AuthenticationException ae) {
            log.error("错误信息：------>" + ae);
        }

//		logDao.insert("USER_LOGIN_SUCCESS");
        baseVO.setBaseVO(BaseVO.SUCCESS, LanguageUtil.show("user_loginSuccess"));
        return baseVO;
    }

    @Override
    public BaseVO updatePassword(int userid, String newPassword) {
        BaseVO baseVO = new BaseVO();
        if (!(userid > 0)) {
            return BaseVO.failure("userid is null");
        }
        if (newPassword == null || newPassword.isEmpty()) {
            return BaseVO.failure("新密码不能为空");
        }
        User user = sqlDAO.findById(User.class, userid);

        String md5Password = PasswordUtil.hashPassword(newPassword, user.getSalt(), OLD_ALGORITHM_NAME, Global.USER_PASSWORD_SALT_NUMBER);
        user.setPassword(md5Password);
        sqlDAO.save(user);

        return baseVO;
    }

    @Override
    public BaseVO createUser(User user, HttpServletRequest request) {
        //用户名、密码进行xss、sql防注入
        user.setUsername(SafetyUtil.xssFilter(user.getUsername()));
        user.setPassword(user.getPassword());

        //既然是注册新用户，那么用户名、密码一定是不能为空的
        if (user.getUsername() == null || "".equals(user.getUsername())) {
            return BaseVO.failure("用户名不能为空！");
        }
        if (user.getPassword() == null || "".equals(user.getPassword())) {
            return BaseVO.failure("密码不能为空！");
        }
        //用户名长度判断
        if (user.getUsername().length() > 40) {
            return BaseVO.failure(LanguageUtil.show("user_userNameToLong"));
        }

        //判断用户名、邮箱、手机号是否有其中已经注册了，唯一性
        //邮箱的唯一，仅当邮箱设置了之后，才会判断邮箱的唯一性
        if (user.getEmail() != null && !user.getEmail().isEmpty()) {
            if (!sqlDAO.findByProperty(User.class, "email", user.getEmail()).isEmpty()) {
                return BaseVO.failure(LanguageUtil.show("user_regFailureForEmailAlreadyExist"));
            }
        }
        //判断用户名唯一性
        if (!sqlDAO.findByProperty(User.class, "username", user.getUsername()).isEmpty()) {
            return BaseVO.failure(LanguageUtil.show("user_regFailureForUsernameAlreadyExist"));
        }
        //判断手机号唯一性
        if (user.getPhone() != null && !user.getPhone().isEmpty()) {
            if (findByPhone(user.getUsername()) != null) {
                return BaseVO.failure(LanguageUtil.show("user_regFailureForPhoneAlreadyExist"));
            }
        }

        if (user.getRegip() == null) {
            user.setRegip(IpUtils.getIpAddress(request));
        }
        if (user.getLastip() == null) {
            user.setLastip(IpUtils.getIpAddress(request));
        }
        if (user.getRegtime() == null) {
            user.setRegtime(DateUtil.timeForUnix10());
        }
        if (user.getLasttime() == null) {
            user.setLasttime(DateUtil.timeForUnix10());
        }
        if (user.getNickname() == null) {
            user.setNickname(user.getUsername());
        } else {
            user.setNickname(StringUtil.filterXss(Sql.filter(user.getNickname())));
        }
        if (user.getAuthority() == null) {
            user.setAuthority(SystemUtil.get("USER_REG_ROLE"));
        }
        if (user.getCurrency() == null) {
            user.setCurrency(0);
        }
        if (user.getReferrerid() == null) {
            //当前登录的用户id
            user.setReferrerid(ShiroFunc.getUserId());
        }
        if (user.getIsfreeze() == null) {
            user.setIsfreeze(User.ISFREEZE_NORMAL);
        }
        if (user.getHead() == null) {
            user.setHead("default.png");
        } else {
            user.setHead(SafetyUtil.filter(user.getHead()));
        }
        if (user.getId() != null) {
            user.setId(null);
        }

        /* 密码加密，保存 */
        Random random = new Random();
        user.setSalt(random.nextInt(10) + "" + random.nextInt(10) + random.nextInt(10) + random.nextInt(10) + "");
        String md5Password = generateMd5Password(user.getPassword(), user.getSalt());
        user.setPassword(md5Password);
        sqlDAO.save(user);

        if (user.getId() > 0) {
            //已注册成功

            //赋予该用户系统设置的默认角色
            UserRole userRole = new UserRole();
            userRole.setRoleid(Lang.stringToInt(user.getAuthority(), SystemUtil.getInt("USER_REG_ROLE")));
            userRole.setUserid(user.getId());
            sqlDAO.save(userRole);

            BaseVO vo = new BaseVO();
            vo.setBaseVO(BaseVO.SUCCESS, user.getId() + "");
            return vo;
        } else {
            return BaseVO.failure(LanguageUtil.show("user_regFailure"));
        }
    }

    @Override
    public String getHead(String defaultHead) {
        String head = null;

        User user = ShiroFunc.getUser();
        if (user == null) {
            head = defaultHead;
        } else {
            if (user.getHead() != null && user.getHead().length() > 10) {
                //判断头像是绝对路径还是相对路径的
                if (user.getHead().indexOf("http:") == 0 || user.getHead().indexOf("https:") == 0 || user.getHead().indexOf("//") == 0) {
                    //如果发现头像是绝对路径，直接将其赋予head，原样返回
                    head = user.getHead();
                } else {
                    //是相对路径，那就要增加前缀了
                    if ("default.png".equals(user.getHead())) {
                        head = defaultHead;
                    } else {
                        head = AttachmentUtil.netUrl() + SystemUtil.get("USER_HEAD_PATH") + user.getHead();
                    }
                }
            } else {
                head = defaultHead;
            }
        }

        return head;
    }

    @Override
    public String generateMd5Password(String originalPassword, String salt) {
        return PasswordUtil.hashPassword(originalPassword, salt, OLD_ALGORITHM_NAME, Global.USER_PASSWORD_SALT_NUMBER);
    }

    @Override
    public UploadFileVO updateHead(MultipartFile head) {
        UploadFileVO uploadFileVO = new UploadFileVO();
        if (head == null || head.isEmpty()) {
            uploadFileVO.setBaseVO(BaseVO.FAILURE, LanguageUtil.show("user_uploadHeadImageNotFind"));
            return uploadFileVO;
        }

        User user = SessionUtil.getUser();
        String fileSuffix = "png";
        fileSuffix = Lang.findFileSuffix(SafetyUtil.filter(head.getOriginalFilename()));
        String newHead = Lang.uuid() + "." + fileSuffix;
        try {
            uploadFileVO = AttachmentUtil.uploadFile(SystemUtil.get("USER_HEAD_PATH") + newHead, head.getInputStream());
        } catch (IOException e) {
            log.error("错误信息：------>" + e);
            uploadFileVO.setBaseVO(BaseVO.FAILURE, e.getMessage());
            return uploadFileVO;
        }

        User u = null;
        if (user != null) {
            u = sqlDAO.findById(User.class, user.getId());
        }
        //删除之前的头像
        if (u != null && u.getHead() != null && !u.getHead().isEmpty() && !"default.png".equals(u.getHead())) {
            AttachmentUtil.deleteObject("image/head/" + u.getHead());
        }

        if (u != null) {
            u.setHead(newHead);
        }
        sqlDAO.save(u);
        SessionUtil.setUser(u);
        return uploadFileVO;
    }

    @Override
    public String getHead(User user) {
        String head = null;
        if (user != null) {
            head = user.getHead();
        }
        if (head == null || head.isEmpty()) {
            head = "default.png";
        }

        //判断头像是绝对路径还是相对路径的
        if (head.indexOf("http:") == 0 || head.indexOf("https:") == 0 || head.indexOf("//") == 0) {
            //如果发现头像是绝对路径，直接将其赋予head，原样返回
            return head;
        } else {
            //是相对路径，那就要增加前缀了
            return AttachmentUtil.netUrl() + SystemUtil.get("USER_HEAD_PATH") + head;
        }
    }
}
