package cn.edu.sjtu.gateway.fileupload.bean;

import lombok.Data;

/**
 * 子文件信息
 *
 * <AUTHOR>
 */
@Data
public class NaiiSubFileBean {
    //文件路径，相对路径，如 site/219/index.html
    public String path;
    //文件大小，单位B
    public long size;
    //上次修改日期，单位是毫秒
    public long lastModified;
    //是否是文件夹？如果是，则是true
    public boolean folder;

    public NaiiSubFileBean() {
        size = 0;
        folder = false;
    }

    public void setPath(String path) {
        if (path == null) {
            this.path = null;
        }
        //obs场景下会多出一个来，所以进行减去
        if (path != null) {
            path = path.replaceAll("//", "/");
        }
        this.path = path;
    }
}
