package cn.edu.sjtu.gateway.fileupload.local;

import cn.edu.sjtu.gateway.fileupload.NaiiStorageInterface;
import cn.edu.sjtu.gateway.fileupload.bean.NaiiSubFileBean;
import cn.edu.sjtu.gateway.fileupload.vo.NaiiUploadFileVO;
import cn.edu.sjtu.gateway.tools.BaseVO;
import cn.edu.sjtu.gateway.tools.StringUtil;
import cn.edu.sjtu.gateway.tools.file.FileUtil;
import cn.edu.sjtu.gateway.vm.util.SystemUtil;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * 附件上传之 服务器本身存储，服务器本地存储，附件存储到服务器硬盘上
 *
 * <AUTHOR>
 */
@Slf4j
public class NaiiLocalStorage implements NaiiStorageInterface {

    //附件保存在JAR包所在目录
    private String localFilePath = SystemUtil.getJarDirPath() + "uploads/";

    /**
     * 获取本地上传的文件，始终基于JAR包所在目录
     *
     * @return 获取到的格式如 D:/fileupload/
     */
    public String getLocalFilePath() {
        return SystemUtil.getJarDirPath() + "uploads/";
    }
    
    /**
     * 不允许外部修改存储路径，确保所有文件存储在JAR包所在目录下的uploads文件夹
     */
    public void setLocalFilePath(String localFilePath) {
        // do nothing, 路径由系统自动管理
    }
    
    public NaiiLocalStorage() {
        // 确保基础上传目录存在
        File baseDir = new File(this.getLocalFilePath());
        if (!baseDir.exists()) {
            baseDir.mkdirs();
        }
    }

    /**
     * 构造函数忽略map中的路径设置，始终使用JAR包所在目录
     */
    public NaiiLocalStorage(Map<String, String> map) {
        // 确保基础上传目录存在
        File baseDir = new File(this.getLocalFilePath());
        if (!baseDir.exists()) {
            baseDir.mkdirs();
        }
    }

    @Override
    public NaiiUploadFileVO upload(String path, InputStream inputStream) {
        NaiiUploadFileVO vo = new NaiiUploadFileVO();

        directoryInit(path);
        File file = new File(this.getLocalFilePath() + path);
        OutputStream os;
        try {
            os = new FileOutputStream(file);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = inputStream.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            inputStream.close();

            vo.setName(file.getName());
            vo.setInfo("success");
            vo.setPath(path);
        } catch (IOException e) {
            vo.setBaseVO(BaseVO.FAILURE, e.getMessage());
            e.printStackTrace();
        }

        return vo;
    }

    @Override
    public BaseVO delete(String path) {
        try {
            FileUtil.deleteFile(this.getLocalFilePath() + path);
            return BaseVO.success();
        } catch (Exception e) {
            return BaseVO.failure(e.getMessage());
        }
    }

    @Override
    public void copyFile(String originalFilePath, String newFilePath) {
        directoryInit(newFilePath);
        FileUtil.copyFile(
            new File(this.getLocalFilePath(), originalFilePath).getPath(),
            new File(this.getLocalFilePath(), newFilePath).getPath()
        );
    }


    /**
     * 目录检测，检测是否存在。若不存在，则自动创建目录。适用于使用本地磁盘进行存储，在本身tomcat中创建目录.有一下两种情况:
     * <ul>
     * 		<li>在线上的tomcat项目中，创建的目录是在 tomcat/webapps/ROOT/ 目录下</li>
     * 		<li>在开发环境Eclipse中，创建的目录是在 target/classes/ 目录下</li>
     * </ul>
     *
     * @param path 要检测的目录，相对路径，如 jar/file/  创建到file文件，末尾一定加/     或者jar/file/a.jar创建到file文件夹
     */
    public void directoryInit(String path) {
        if (path == null) {
            return;
        }

        //windows取的路径是\，所以要将\替换为/
        if (path.indexOf("\\") > 1) {
            path = StringUtil.replaceAll(path, "\\\\", "/");
        }

        if (path.length() - path.lastIndexOf("/") > 1) {
            //path最后是带了具体文件名的，把具体文件名过滤掉，只留文件/结尾
            path = path.substring(0, path.lastIndexOf("/") + 1);
        }

        //如果目录或文件不存在，再进行创建目录的判断
        String fullPath = this.getLocalFilePath() + path;
        if (!FileUtil.exists(fullPath)) {
            String[] ps = path.split("/");

            String xiangdui = "";
            //length-1，/最后面应该就是文件名了，所以要忽略最后一个
            for (String p : ps) {
                if (!p.isEmpty()) {
                    xiangdui = xiangdui + p + "/";
                    String currentPath = this.getLocalFilePath() + xiangdui;
                    if (!FileUtil.exists(currentPath)) {
                        File file = new File(currentPath);
                        file.mkdirs(); // 使用mkdirs确保创建所有必要的父目录
                    }
                }
            }
        }
    }

    @Override
    public List<NaiiSubFileBean> getSubFileList(String path) {
        List<NaiiSubFileBean> list = new ArrayList<NaiiSubFileBean>();
        if (path == null || path.isEmpty()) {
            return list;
        }

        File file = new File(this.getLocalFilePath() + path);
        if (!file.exists()) {
            //文件夹不存在，也返回空
            return list;
        }

        File[] subFiles = file.listFiles();
        for (int i = 0; i < subFiles.length; i++) {
            File subFile = subFiles[i];
            NaiiSubFileBean bean = new NaiiSubFileBean();
            bean.setPath(subFile.getPath().replace(this.localFilePath + path, ""));
            bean.setSize(subFile.length());
            bean.setLastModified(subFile.lastModified());
            bean.setFolder(subFile.isDirectory());
            list.add(bean);
        }

        return list;
    }

    @Override
    public long getSize(String path) {
        File file = new File(this.getLocalFilePath() + path);
        if (!file.exists()) {
            //文件不存在
            return -1;
        }

        if (file.isDirectory()) {
            //是目录，那获取这个目录的大小
            directoryInit(path);
            return sizeOfDirectory(new File(this.getLocalFilePath() + path));
        } else {
            //不是目录，那就是具体文件了，返回这个文件的大小
            return file.length();
        }
    }

    @Override
    public BaseVO createFolder(String path) {
        directoryInit(path);
        return BaseVO.success();
    }

    @Override
    public InputStream get(String path) {
        File file = new File(this.getLocalFilePath() + path);
        if (!file.exists()) {
            return null;
        }

        try {
            InputStream in = new FileInputStream(file);
            return in;
        } catch (FileNotFoundException e) {
            log.debug(e.getMessage());
            e.printStackTrace();
            return null;
        }
    }


    /**** 下面的复制与 common-fileupload ，因为用的不多，就不额外多引入jar包了 ****/

    /**
     * Counts the size of a directory recursively (sum of the length of all files).
     *
     * @param directory directory to inspect, must not be {@code null}
     * @return size of directory in bytes, 0 if directory is security restricted, a negative number when the real total
     * is greater than {@link Long#MAX_VALUE}.
     * @throws NullPointerException if the directory is {@code null}
     */
    public static long sizeOfDirectory(File directory) {
        checkDirectory(directory);

        final File[] files = directory.listFiles();
        if (files == null) {  // null if security restricted
            return 0L;
        }
        long size = 0;

        for (final File file : files) {
            try {
                if (!isSymlink(file)) {
                    size += sizeOf(file);
                    if (size < 0) {
                        break;
                    }
                }
            } catch (IOException ioe) {
                // Ignore exceptions caught when asking if a File is a symlink.
            }
        }

        return size;
    }


    /**
     * Checks that the given {@code File} exists and is a directory.
     *
     * @param directory The {@code File} to check.
     * @throws IllegalArgumentException if the given {@code File} does not exist or is not a directory.
     */
    private static void checkDirectory(File directory) {
        if (!directory.exists()) {
            throw new IllegalArgumentException(directory + " does not exist");
        }
        if (!directory.isDirectory()) {
            throw new IllegalArgumentException(directory + " is not a directory");
        }
    }

    /**
     * Determines whether the specified file is a Symbolic Link rather than an actual file.
     * <p>
     * Will not return true if there is a Symbolic Link anywhere in the path,
     * only if the specific file is.
     * <p>
     * <b>Note:</b> the current implementation always returns {@code false} if the system
     *
     * @param file the file to check
     * @return true if the file is a Symbolic Link
     * @throws java.io.IOException if an IO error occurs while checking the file
     * @since 2.0
     */
    public static boolean isSymlink(File file) throws IOException {
        if (file == null) {
            throw new NullPointerException("File must not be null");
        }

        /**
         * The Windows separator character.
         */
        char WINDOWS_SEPARATOR = '\\';
        /**
         * The system separator character.
         */
        char SYSTEM_SEPARATOR = File.separatorChar;
        if (SYSTEM_SEPARATOR == WINDOWS_SEPARATOR) {
            return false;
        }

        File fileInCanonicalDir = null;
        if (file.getParent() == null) {
            fileInCanonicalDir = file;
        } else {
            File canonicalDir = file.getParentFile().getCanonicalFile();
            fileInCanonicalDir = new File(canonicalDir, file.getName());
        }

        if (fileInCanonicalDir.getCanonicalFile().equals(fileInCanonicalDir.getAbsoluteFile())) {
            return false;
        } else {
            return true;
        }
    }


    //-----------------------------------------------------------------------

    /**
     * Returns the size of the specified file or directory. If the provided
     * {@link java.io.File} is a regular file, then the file's length is returned.
     * If the argument is a directory, then the size of the directory is
     * calculated recursively. If a directory or subdirectory is security
     * restricted, its size will not be included.
     *
     * @param file the regular file or directory to return the size
     *             of (must not be {@code null}).
     * @return the length of the file, or recursive size of the directory,
     * provided (in bytes).
     * @throws NullPointerException     if the file is {@code null}
     * @throws IllegalArgumentException if the file does not exist.
     * @since 2.0
     */
    public static long sizeOf(File file) {

        if (!file.exists()) {
            String message = file + " does not exist";
            throw new IllegalArgumentException(message);
        }

        if (file.isDirectory()) {
            return sizeOfDirectory(file);
        } else {
            return file.length();
        }

    }

}
