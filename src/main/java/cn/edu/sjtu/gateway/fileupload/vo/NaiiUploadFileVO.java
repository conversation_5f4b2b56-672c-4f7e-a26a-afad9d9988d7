package cn.edu.sjtu.gateway.fileupload.vo;


import cn.edu.sjtu.gateway.tools.BaseVO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 文件上传相关（最初为aliyun oss所写）
 *
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
public class NaiiUploadFileVO extends BaseVO {

    /**
     * 无文件
     */
    public final static int NOTFILE = 2;

    /**
     * -- GETTER --
     * 上传成功后的文件名，如 "xnx3.jar"
     * <p>
     * <p>
     * -- SETTER --
     * 上传成功后的文件名，如 "xnx3.jar"
     *
     * @return
     * @param fileName
     */
    public String name;
    /**
     * -- GETTER --
     * 上传成功后的路径，如 "/jar/file/xnx3.jar"
     * <p>
     * <p>
     * -- SETTER --
     * 上传成功后的路径，如 "/jar/file/xnx3.jar"
     *
     * @return
     * @param path
     */
    public String path;
    /**
     * -- GETTER --
     * 文件上传成功后，外网访问的url
     * <p>
     * <p>
     * -- SETTER --
     * 文件上传成功后，外网访问的url
     *
     * @return 返回如  http://test.zvo.cn/jar/file/xnx3.jar
     * @param url
     */
    public String url;
    public long size;

    public NaiiUploadFileVO() {

    }

    /**
     * 这是OSS上传成功后的返回值
     *
     * @param path     上传成功后的路径，如 "/jar/file/xnx3.jar"
     * @param url      文件上传成功后，外网访问的url
     */
    public NaiiUploadFileVO(String name, String path, String url) {
        this.name = name;
        this.path = path;
    }

}
