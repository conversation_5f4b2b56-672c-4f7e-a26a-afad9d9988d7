package cn.edu.sjtu.gateway.plugin.markdown.controller;

import cn.edu.sjtu.gateway.manager.entity.Site;
import cn.edu.sjtu.gateway.vm.util.SessionUtil;
import cn.edu.sjtu.gateway.vm.controller.BaseController;
import cn.edu.sjtu.gateway.vm.pluginManage.controller.BasePluginController;
import cn.edu.sjtu.gateway.vm.util.AttachmentUtil;

import cn.edu.sjtu.gateway.vm.vo.UploadFileVO;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * markdown上传，v4.3版本转移至plugin下
 * <AUTHOR>
 */
@Controller
@RequestMapping("/plugin/markdown/")
@Slf4j
public class MarkDownEditPluginController extends BasePluginController {
	
	/**
	 * 上传图片接口
	 */
	@RequestMapping("uploadImage${url.suffix}")
	public void uploadImage(Model model,HttpServletRequest request, HttpServletResponse response){
		JSONObject json = new JSONObject();
		UploadFileVO uploadFileVO = new UploadFileVO();
		Site site = SessionUtil.getSite();
		if(site == null){
			json.put("success", 0);
			json.put("success", "请先登录");
		}else{
			uploadFileVO = AttachmentUtil.uploadImage("site/"+site.getId()+"/news/", request, "editormd-image-file", 0);
			if(uploadFileVO.getResult() == UploadFileVO.SUCCESS){
				json.put("success", 1);
				json.put("message", "上传成功");
				json.put("url", uploadFileVO.getUrl());
				//上传成功，写日志
				log.info("CMS模式下，模版页自由上传图片成功："+uploadFileVO.getFileName());
			}else{
				json.put("success", 0);
				json.put("message", uploadFileVO.getInfo());
			}
		}
		
		response.setCharacterEncoding("UTF-8");
		response.setContentType("text/html; charset=utf-8");
        BaseController.writeData(response, json);
    }
	

	
}