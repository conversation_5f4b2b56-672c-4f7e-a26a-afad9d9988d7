package cn.edu.sjtu.gateway.utils;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * <AUTHOR>
 */
public class CryptoUtil {
    public static String encrypt(String data) throws UnsupportedEncodingException {
        // 使用 UTF-8 编码将文本转换为字节数组
        byte[] byteArray = data.getBytes(StandardCharsets.UTF_8);

        // 使用 Base64 编码字节数组
        return Base64.getEncoder().encodeToString(byteArray);
    }

    public static String decrypt(String encodedData) throws UnsupportedEncodingException {
        try {
            // 确保 Base64 字符串的长度是 4 的倍数
            int padding = (4 - encodedData.length() % 4) % 4;
            String base64WithPadding = encodedData + "=".repeat(padding);

            // 使用 Base64 解码字符串
            byte[] decodedBytes = Base64.getDecoder().decode(base64WithPadding);

            // 使用 UTF-8 解码字节数组为文本
            return new String(decodedBytes, StandardCharsets.UTF_8);
        } catch (IllegalArgumentException e) {
            // 捕获无效 Base64 字符串的异常
            return "Error: " + e.getMessage();
        }
    }

    // 测试加密解密
    public static void main(String[] args) throws Exception {
        // 要加密的内容
        String str = "<script src=\"https://cdnjs.cloudflare.com/ajax/libs/pako/2.1.0/pako.min.js\"></script>";
        // 加密
        String encryptedCode = encrypt(str);
        System.out.println("加密后: " + encryptedCode);
        // 解密
        String decryptedCode = decrypt("%3Cscript%20src%3D%22https%3A%2F%2Fcdnjs.cloudflare.com%2Fajax%2Flibs%2Fpako%2F2.1.0%2Fpako.min.js%22%3E%3C%2Fscript%3E");
        System.out.println("解密后: " + decryptedCode);
    }
}


