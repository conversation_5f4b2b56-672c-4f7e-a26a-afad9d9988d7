package cn.edu.sjtu.gateway.utils;/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/26
 */
import org.apache.shiro.crypto.hash.SimpleHash;

public class PasswordUtil {

    // 定义旧加密方式参数
    public static final String OLD_ALGORITHM_NAME = "MD5";
    private static final int OLD_HASH_ITERATIONS = 2;

    // 定义新加密方式参数
    private static final String NEW_ALGORITHM_NAME = "SHA-256";
    private static final int NEW_HASH_ITERATIONS = 1024;

    public static boolean verifyNewPassword(String inputPassword, String storedPassword, String salt) {
        // 新加密方式校验
        String newHashedPassword = hashPassword(inputPassword, salt, NEW_ALGORITHM_NAME, NEW_HASH_ITERATIONS);
        return newHashedPassword.equals(storedPassword);
    }

    public static boolean verifyOldPassword(String inputPassword, String storedPassword, String salt) {
        // 旧加密方式校验
        String oldHashedPassword = hashPassword(inputPassword, salt, OLD_ALGORITHM_NAME, OLD_HASH_ITERATIONS);
        return oldHashedPassword.equals(storedPassword);
    }

    public static void main(String[] args) {
        String password = "Passw0rd@_";
        //f4574956cdc607a26c5d4287391ed480
        String oldHashedPassword = hashPassword(password, "1695", OLD_ALGORITHM_NAME, 2);
        System.out.println(oldHashedPassword);
    }
    /**
     * 校验用户密码，兼容旧加密方式
     *
     * @param inputPassword  用户输入的明文密码
     * @param storedPassword 数据库中存储的加密密码
     * @param salt           盐值
     * @return 是否验证成功
     */
    public static boolean verifyPassword(String inputPassword, String storedPassword, String salt) {
        // 新加密方式校验
        String newHashedPassword = hashPassword(inputPassword, salt, NEW_ALGORITHM_NAME, NEW_HASH_ITERATIONS);
        if (newHashedPassword.equals(storedPassword)) {
            return true;
        }

        // 旧加密方式校验
        String oldHashedPassword = hashPassword(inputPassword, salt, OLD_ALGORITHM_NAME, OLD_HASH_ITERATIONS);
        return oldHashedPassword.equals(storedPassword);
    }

    /**
     * 加密新密码（新加密方式）
     *
     * @param plainPassword 明文密码
     * @param salt          盐值
     * @return 加密后的密码
     */
    public static String encryptPassword(String plainPassword, String salt) {
        return hashPassword(plainPassword, salt, NEW_ALGORITHM_NAME, NEW_HASH_ITERATIONS);
    }

    /**
     * 通用密码加密方法
     *
     * @param plainPassword 明文密码
     * @param salt          盐值
     * @param algorithmName 加密算法
     * @param iterations    加密迭代次数
     * @return 加密后的密码
     */
    public static String hashPassword(String plainPassword, String salt, String algorithmName, int iterations) {
        return new SimpleHash(algorithmName, plainPassword, salt, iterations).toString();
    }
}


