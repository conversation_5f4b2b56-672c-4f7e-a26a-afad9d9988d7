package cn.edu.sjtu.gateway.utils;/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/26
 */

import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;

/**
 * @FileName ImageCompressor
 * @Description
 * <AUTHOR>
 * @date 2024-11-26
 **/
public class ImageCompressor {
    private static final Logger log = LoggerFactory.getLogger(ImageCompressor.class);

    /**
     * 压缩图片方法，使用 Thumbnailator 进行高质量压缩。
     *
     * @param inputFile 输入图片文件路径
     * @param outputDir 输出图片文件路径
     * @throws IOException 如果文件操作发生错误
     */
    public static void compressImage(String inputFile, String outputDir) throws IOException {
        File input = new File(inputFile);
        if (!input.exists()) {
            throw new IOException("输入文件不存在: " + inputFile);
        }

        // 获取原图的宽高
        java.awt.image.BufferedImage originalImage = javax.imageio.ImageIO.read(input);
        int originalWidth = originalImage.getWidth();
        int originalHeight = originalImage.getHeight();

        // 如果图片尺寸不超过1000像素，直接返回，不需要压缩
        if (originalWidth <= 1000 && originalHeight <= 1000) {
            log.info("图片尺寸不超过1000像素，无需压缩。");
            return;
        }

        // 获取输入文件的扩展名
        String fileName = input.getName();
        String fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);

        // 确保输出目录存在
        File outputDirectory = new File(outputDir);
        if (!outputDirectory.exists() && !outputDirectory.mkdirs()) {
            throw new IOException("无法创建输出目录: " + outputDir);
        }

        // 生成压缩后文件的路径，保持原文件名
        File outputFile = new File(outputDirectory, fileName);

        // 使用 Thumbnailator 压缩图片，自动进行高质量缩放
        Thumbnails.of(input)
                // 最大宽高为1000，保持比例缩放
                .size(1024, 1024)
                // 使用原始文件扩展名
                .outputFormat(fileExtension)
                // 输出到指定文件
                .toFile(outputFile);
        log.info("图片已压缩并保存到: {}", outputFile.getAbsolutePath());
    }

    public static MultipartFile compressImage(MultipartFile file) throws IOException {
        // 获取原文件名和扩展名
        String fileName = file.getOriginalFilename();
        String fileExtension = null;
        if (fileName != null) {
            fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
        }

        // 读取原图片并获取其宽度和高度
        BufferedImage originalImage = ImageIO.read(file.getInputStream());
        int originalWidth = originalImage.getWidth();
        int originalHeight = originalImage.getHeight();

        // 输出原始图片的尺寸
        log.info("原始图片尺寸: {}" ,  originalWidth + "x" + originalHeight);
        // 将 MultipartFile 转换为 ByteArrayOutputStream 来保存压缩后的图像
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        // 使用 Thumbnailator 压缩图片
        Thumbnails.of(file.getInputStream())
                // 最大宽高为1024，保持比例缩放
                .size(1024, 1024)
                // 使用原始文件扩展名
                .outputFormat(fileExtension)
                // 输出到 ByteArrayOutputStream
                .toOutputStream(baos);

        // 将 ByteArrayOutputStream 转换为字节数组
        byte[] compressedBytes = baos.toByteArray();

        // 计算压缩后的文件大小
        long compressedSize = compressedBytes.length;
        log.info("压缩后图片文件大小: {}",  compressedSize + " 字节");

        // 获取压缩后图片的尺寸
        BufferedImage compressedImage = ImageIO.read(new java.io.ByteArrayInputStream(compressedBytes));
        int compressedWidth = compressedImage.getWidth();
        int compressedHeight = compressedImage.getHeight();
        log.info("压缩后图片尺寸: {}", compressedWidth + "x" + compressedHeight);
        // 如果文件没有明显压缩，提醒用户
        if (compressedWidth == originalWidth && compressedHeight == originalHeight) {
            log.info("警告: 图片尺寸未发生明显变化，可能图片尺寸已在范围内。");
        }

        // 创建一个新的 MultipartFile（可以使用 CommonsMultipartFile）
        DiskFileItem fileItem = new DiskFileItem("file", "image/" + fileExtension, false, fileName, compressedBytes.length, new java.io.File(System.getProperty("java.io.tmpdir")));
        fileItem.getOutputStream().write(compressedBytes);

        // 创建并返回压缩后的 MultipartFile
        return new CommonsMultipartFile(fileItem);
    }

    public static void main(String[] args) {
        try {
            // 输入图片文件路径
            String inputFile = "D:\\code\\v5\\naii-gateway\\site\\344\\news\\07cc0d7516c7479fa8a8e245ff8bdb9c.png";
            // 输出图片目录
            String outputDir = "D:\\";
            compressImage(inputFile, outputDir);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
