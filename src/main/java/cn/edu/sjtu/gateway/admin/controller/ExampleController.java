package cn.edu.sjtu.gateway.admin.controller;

import cn.edu.sjtu.gateway.common.controller.BaseController;
import cn.edu.sjtu.gateway.common.exception.BusinessException;
import cn.edu.sjtu.gateway.common.exception.SystemException;
import cn.edu.sjtu.gateway.common.response.Result;
import cn.edu.sjtu.gateway.common.response.PageResult;
import cn.edu.sjtu.gateway.common.entity.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * 示例控制器 - 展示如何使用新的统一框架
 *
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping("/admin/example")
public class ExampleController extends BaseController {

    /**
     * 示例页面
     */
    @GetMapping("/index")
    public String index(Model model) {
        // 使用基类的消息设置方法
        setInfoMessage(model, "这是一个使用新框架的示例页面");

        // 基类会自动设置通用属性
        return "admin/example/index";
    }

    /**
     * 示例API - 成功响应
     */
    @GetMapping("/api/success")
    @ResponseBody
    public Result<String> apiSuccess() {
        // 使用统一的成功响应
        return success("操作成功", "这是成功的数据");
    }

    /**
     * 示例API - 业务异常
     */
    @GetMapping("/api/business-error")
    @ResponseBody
    public Result<Object> apiBusinessError() {
        // 抛出业务异常，会被全局异常处理器捕获
        throw BusinessException.paramError("参数验证失败");
    }

    /**
     * 示例API - 系统异常
     */
    @GetMapping("/api/system-error")
    @ResponseBody
    public Result<Object> apiSystemError() {
        // 模拟系统异常
        try {
            // 模拟数据库操作失败
            throw SystemException.databaseOperationError("数据库操作失败");
        } catch (Exception e) {
            // 抛出系统异常
            throw SystemException.databaseOperationError(e.getMessage());
        }
    }

    /**
     * 示例API - 分页数据
     */
    @GetMapping("/api/page-data")
    @ResponseBody
    public PageResult<User> apiPageData(HttpServletRequest request) {
        // 使用基类的分页参数获取方法
        int page = getPageNumber(request);
        int size = getPageSize(request);

        // 模拟数据
        List<User> users = Arrays.asList(
            createMockUser(1, "admin"),
            createMockUser(2, "user1"),
            createMockUser(3, "user2")
        );

        // 使用统一的分页响应
        return PageResult.of(users, 100, page, size);
    }

    /**
     * 示例表单处理 - 使用业务异常
     */
    @PostMapping("/form/submit")
    public String formSubmit(@RequestParam String username,
                           @RequestParam String email,
                           Model model) {
        try {
            // 参数验证
            if (username == null || username.trim().isEmpty()) {
                throw BusinessException.paramError("用户名不能为空");
            }

            if (email == null || !email.contains("@")) {
                throw BusinessException.paramError("邮箱格式不正确");
            }

            // 模拟业务逻辑
            if ("admin".equals(username)) {
                throw BusinessException.dataExists("用户名已存在");
            }

            // 成功处理
            return success(model, "用户创建成功", "/admin/example/index");

        } catch (BusinessException e) {
            // 业务异常直接显示给用户
            return error(model, e.getMessage());
        } catch (Exception e) {
            // 系统异常记录日志，显示通用错误信息
            log.error("创建用户失败", e);
            return error(model, "系统异常，请稍后重试");
        }
    }

    /**
     * 示例Ajax请求处理
     */
    @PostMapping("/ajax/validate")
    @ResponseBody
    public Result<Object> ajaxValidate(@RequestParam String data) {
        // 使用基类的参数验证
        if (data == null || data.trim().isEmpty()) {
            return failure("数据不能为空");
        }

        // 模拟验证逻辑
        if (data.length() < 3) {
            return failure(400, "数据长度不能少于3位");
        }

        return success("验证通过");
    }

    /**
     * 示例文件上传处理
     */
    @PostMapping("/upload")
    @ResponseBody
    public Result<String> upload(@RequestParam("file") org.springframework.web.multipart.MultipartFile file) {
        try {
            // 文件验证
            if (file.isEmpty()) {
                throw BusinessException.paramError("请选择要上传的文件");
            }

            // 文件大小验证
            if (file.getSize() > 10 * 1024 * 1024) { // 10MB
                throw BusinessException.fileSizeExceeded();
            }

            // 文件类型验证
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                throw BusinessException.fileFormatNotSupported();
            }

            // 模拟文件保存
            String fileName = "uploaded_" + System.currentTimeMillis() + "_" + file.getOriginalFilename();

            return success(fileName, "文件上传成功");

        } catch (BusinessException e) {
            // 业务异常会被全局异常处理器处理
            throw e;
        } catch (Exception e) {
            // 系统异常
            throw SystemException.ioError("文件上传失败: " + e.getMessage());
        }
    }


    /**
     * 创建模拟用户
     */
    private User createMockUser(int id, String username) {
        User user = new User();
        user.setId(id);
        user.setUsername(username);
        user.setEmail(username + "@example.com");
        return user;
    }
}
