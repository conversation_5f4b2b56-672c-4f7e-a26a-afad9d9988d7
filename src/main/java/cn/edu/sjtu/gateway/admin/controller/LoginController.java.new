package cn.edu.sjtu.gateway.admin.controller;

import cn.edu.sjtu.gateway.admin.entity.Site;
import cn.edu.sjtu.gateway.admin.entity.SiteUser;
import cn.edu.sjtu.gateway.agencyadmin.entity.Agency;
import cn.edu.sjtu.gateway.agencyadmin.entity.AgencyData;
import cn.edu.sjtu.gateway.common.controller.BaseController;
import cn.edu.sjtu.gateway.common.exception.BusinessException;
import cn.edu.sjtu.gateway.common.exception.SystemException;
import cn.edu.sjtu.gateway.common.response.Result;
import cn.edu.sjtu.gateway.common.util.StringUtils;
import cn.edu.sjtu.gateway.common.util.DateUtils;
import cn.edu.sjtu.gateway.vm.entity.User;
import cn.edu.sjtu.gateway.vm.service.ApiService;
import cn.edu.sjtu.gateway.vm.service.SqlService;
import cn.edu.sjtu.gateway.vm.service.UserService;
import cn.edu.sjtu.gateway.vm.util.AttachmentUtil;
import cn.edu.sjtu.gateway.vm.util.CaptchaUtil;
import cn.edu.sjtu.gateway.vm.util.SystemUtil;
import cn.edu.sjtu.gateway.vm.vo.BaseVO;
import cn.edu.sjtu.gateway.vm.vo.LoginVO;
import cn.edu.sjtu.gateway.vm.vo.UserVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.io.IOException;

/**
 * 登录控制器 - 使用新框架重构
 * 展示如何使用统一的异常处理、响应格式和工具类
 * 
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping("/")
public class LoginController extends BaseController {
    
    private final UserService userService;
    private final SqlService sqlService;
    private final ApiService apiService;

    public LoginController(UserService userService, SqlService sqlService, ApiService apiService) {
        this.userService = userService;
        this.sqlService = sqlService;
        this.apiService = apiService;
    }

    /**
     * 注册页面
     * @deprecated v4.12起，废弃reg，使用 plugin/phoneCreateSite/reg.naii?inviteid=123 自助开通网站
     */
    @GetMapping("/reg${url.suffix}")
    public String reg(HttpServletRequest request, Model model) {
        try {
            // 使用统一的参数获取方法
            if (SystemUtil.getInt("ALLOW_USER_REG") == 0) {
                // 使用统一的错误页面方法
                return error(model, "系统已禁止用户自行注册");
            }
            
            // 使用基类的登录状态检查方法
            if (isLoggedIn()) {
                return error(model, "您已登录，无需注册");
            }
            
            userService.regInit(request);
            
            // 使用统一的参数获取方法
            String inviteId = getParameter(request, "inviteid", "");
            
            log.info("进入注册页面reg.naii，进行redirect至手机号开通网站插件的注册，inviteid={}", inviteId);
            return redirect("plugin/phoneCreateSite/reg.naii?inviteid=" + inviteId);
            
        } catch (Exception e) {
            log.error("注册页面访问异常", e);
            return error(model, "系统异常，请稍后重试");
        }
    }

    /**
     * 通过API接口登录网站
     * @param key 参考 http://api.wscso.com/2779.html
     * @deprecated 请使用 /plugin/api/loginApi.do
     */
    @GetMapping("loginApi${url.suffix}")
    public String loginApi(HttpServletRequest request, Model model,
                          @RequestParam(value = "key", required = false, defaultValue = "") String key) {
        try {
            // 使用基类的登录状态检查
            if (isLoggedIn()) {
                log.info("用户已登录，直接跳转到控制台");
                return redirect(cn.edu.sjtu.gateway.admin.Func.getConsoleRedirectUrl());
            }
            
            // 参数验证
            if (StringUtils.isEmpty(key)) {
                throw BusinessException.paramError("API密钥不能为空");
            }
            
            UserVO vo = apiService.identityVerifyAndSession(key);
            if (vo.getResult() == UserVO.FAILURE) {
                return error(model, vo.getInfo());
            }
            
            log.info("API登录成功，用户ID: {}", vo.getId());
            return redirect(cn.edu.sjtu.gateway.admin.Func.getConsoleRedirectUrl());
            
        } catch (BusinessException e) {
            // 业务异常直接显示给用户
            return error(model, e.getMessage());
        } catch (Exception e) {
            log.error("API登录异常", e);
            return error(model, "登录失败，请稍后重试");
        }
    }

    /**
     * 登录请求验证 - 使用新的响应格式
     */
    @PostMapping(value = "naiiLoginSubmit${url.suffix}")
    @ResponseBody
    public Result<LoginVO> naiiLoginSubmit(HttpServletRequest request) {
        try {
            // 获取并验证参数
            String username = getParameter(request, "username");
            String password = getParameter(request, "password");
            String code = getParameter(request, "code");
            
            // 参数验证
            if (StringUtils.isEmpty(username)) {
                throw BusinessException.paramError("用户名不能为空");
            }
            if (StringUtils.isEmpty(password)) {
                throw BusinessException.paramError("密码不能为空");
            }
            if (StringUtils.isEmpty(code)) {
                throw BusinessException.paramError("验证码不能为空");
            }
            
            // 验证码校验
            BaseVO capVO = CaptchaUtil.compare(code, request);
            if (capVO.getResult() == BaseVO.FAILURE) {
                log.warn("登录失败 - 验证码错误，用户名: {}, 提交的验证码: {}", 
                        StringUtils.filterXss(username), StringUtils.filterXss(code));
                throw BusinessException.captchaError();
            }
            
            // 用户名密码验证
            BaseVO baseVO = userService.loginByUsernameAndPassword(request);
            if (baseVO.getResult() != BaseVO.SUCCESS) {
                log.warn("登录失败 - 用户名密码错误，用户名: {}", StringUtils.filterXss(username));
                throw BusinessException.loginFailed(baseVO.getInfo());
            }
            
            // 登录成功处理
            LoginVO loginVO = processSuccessfulLogin(request, baseVO);
            
            log.info("用户登录成功，用户名: {}", StringUtils.filterXss(username));
            return success(loginVO, "登录成功");
            
        } catch (BusinessException e) {
            // 业务异常会被全局异常处理器处理，这里直接抛出
            throw e;
        } catch (Exception e) {
            log.error("登录处理异常", e);
            throw SystemException.of("登录系统异常", e);
        }
    }
    
    /**
     * 处理登录成功后的逻辑
     */
    private LoginVO processSuccessfulLogin(HttpServletRequest request, BaseVO baseVO) {
        try {
            LoginVO vo = new LoginVO();
            vo.setBaseVO(baseVO);
            
            // 获取当前登录用户信息
            User user = getCurrentUser();
            if (user == null) {
                // 从数据库获取
                int userId = StringUtils.toInt(baseVO.getInfo(), 0);
                if (userId <= 0) {
                    throw BusinessException.of("用户ID无效");
                }
                user = sqlService.findById(User.class, userId);
                if (user == null) {
                    throw BusinessException.userNotFound();
                }
            }
            
            // 获取用户扩展信息
            SiteUser siteUser = sqlService.findById(SiteUser.class, user.getId());
            if (siteUser == null) {
                siteUser = new SiteUser();
            }
            
            // 缓存到session
            cn.edu.sjtu.gateway.vm.util.SessionUtil.setSiteUser(siteUser);
            
            // 检查用户状态
            checkUserStatus(user, siteUser);
            
            // 检查代理状态
            checkAgencyStatus(user);
            
            // 设置跳转URL
            vo.setInfo(cn.edu.sjtu.gateway.admin.Func.getConsoleRedirectUrl());
            
            return vo;
            
        } catch (Exception e) {
            log.error("处理登录成功逻辑异常", e);
            throw SystemException.of("登录后处理异常", e);
        }
    }
    
    /**
     * 检查用户状态
     */
    private void checkUserStatus(User user, SiteUser siteUser) {
        // 检查用户是否被冻结
        if (user.getIsfreeze() != null && user.getIsfreeze() == User.ISFREEZE_FREEZE) {
            throw BusinessException.accountLocked();
        }
        
        // 检查站点状态
        if (siteUser.getSiteid() != null && siteUser.getSiteid() > 0) {
            Site site = sqlService.findById(Site.class, siteUser.getSiteid());
            if (site != null && site.getState() != null && site.getState() == Site.STATE_FREEZE) {
                throw BusinessException.of("您的网站已被冻结，请联系管理员");
            }
        }
    }
    
    /**
     * 检查代理状态
     */
    private void checkAgencyStatus(User user) {
        try {
            // 获取代理信息
            Agency myAgency = sqlService.findById(Agency.class, user.getId());
            if (myAgency != null) {
                // 检查代理是否过期
                int currentTime = DateUtils.currentTimeSeconds();
                if (myAgency.getExpiretime() != null && myAgency.getExpiretime() < currentTime) {
                    // 代理已过期
                    String expireDate = DateUtils.formatUnixTime(myAgency.getExpiretime(), "yyyy-MM-dd");
                    
                    // 获取上级代理信息
                    String contactInfo = getParentAgencyContactInfo();
                    
                    String message = String.format("您的代理资格已于 %s 到期！<br/>若要继续使用，请联系：<br/>%s", 
                            expireDate, contactInfo);
                    
                    // 登出用户
                    cn.edu.sjtu.gateway.vm.util.SessionUtil.logout();
                    
                    throw BusinessException.of(11, message);
                }
                
                log.info("代理用户登录成功，代理ID: {}", myAgency.getId());
            }
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("检查代理状态异常", e);
            // 代理状态检查异常不影响普通用户登录
        }
    }
    
    /**
     * 获取上级代理联系信息
     */
    private String getParentAgencyContactInfo() {
        try {
            Agency parentAgency = cn.edu.sjtu.gateway.agencyadmin.util.SessionUtil.getParentAgency();
            if (parentAgency != null) {
                return String.format("姓名：%s<br/>QQ：%s<br/>电话：%s", 
                        parentAgency.getName(), 
                        parentAgency.getQq(), 
                        parentAgency.getPhone());
            }
        } catch (Exception e) {
            log.warn("获取上级代理信息失败", e);
        }
        return "请联系您的上级代理";
    }

    /**
     * 验证码图片显示
     */
    @GetMapping("/captcha${url.suffix}")
    public void captcha(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            log.debug("生成验证码图片，IP: {}", getClientIpAddress(request));

            cn.edu.sjtu.tools.media.CaptchaUtil captchaUtil = new cn.edu.sjtu.tools.media.CaptchaUtil();
            captchaUtil.setCodeCount(5);
            captchaUtil.setFont(new Font("Fixedsys", Font.BOLD, 21));
            captchaUtil.setHeight(18);
            captchaUtil.setWidth(110);
            
            cn.edu.sjtu.gateway.vm.util.CaptchaUtil.showImage(captchaUtil, request, response);
            
        } catch (Exception e) {
            log.error("生成验证码异常", e);
            throw SystemException.ioError("验证码生成失败");
        }
    }

    /**
     * 登录页面
     */
    @GetMapping("login${url.suffix}")
    public String login(HttpServletRequest request) {
        try {
            // 使用基类的登录状态检查
            if (isLoggedIn()) {
                log.info("用户已登录，直接跳转到控制台，IP: {}", getClientIpAddress(request));
                return redirect(cn.edu.sjtu.gateway.admin.Func.getConsoleRedirectUrl());
            }
            
            // 检查是否需要安装
            if ("true".equals(SystemUtil.get("IW_AUTO_INSTALL_USE"))) {
                log.info("系统尚未安装，跳转到安装页面");
                return redirect("install/index.naii");
            }
            
            log.info("进入登录页面，IP: {}", getClientIpAddress(request));
            return "/wm/login/siteLogin";
            
        } catch (Exception e) {
            log.error("访问登录页面异常", e);
            // 即使异常也要显示登录页面
            return "/wm/login/siteLogin";
        }
    }
    
    /**
     * 登出
     */
    @PostMapping("/logout${url.suffix}")
    @ResponseBody
    public Result<Object> logout(HttpServletRequest request) {
        try {
            String username = getCurrentUser() != null ? getCurrentUser().getUsername() : "unknown";
            
            // 执行登出
            cn.edu.sjtu.gateway.vm.util.SessionUtil.logout();
            
            log.info("用户登出成功，用户名: {}, IP: {}", username, getClientIpAddress(request));
            return success("登出成功");
            
        } catch (Exception e) {
            log.error("登出异常", e);
            return failure("登出失败");
        }
    }
}
