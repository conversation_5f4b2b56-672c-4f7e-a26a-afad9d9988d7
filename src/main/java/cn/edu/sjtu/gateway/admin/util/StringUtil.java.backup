package cn.edu.sjtu.gateway.admin.util;

import cn.edu.sjtu.gateway.common.util.StringUtils;

/**
 * 字符串工具类 - 迁移适配器
 * 
 * @deprecated 请使用 {@link StringUtils}
 * <AUTHOR>
 */
@Deprecated
public class StringUtil {
	
	/**
	 * 判断字符串是否是英文+数字，如果是则返回true，如果还有别的则返回false
	 * @deprecated 请使用 {@link StringUtils#isEnglishAndNumber(String)}
     */
	@Deprecated
	public static boolean isEnglishAndNumber(String str){
		return StringUtils.isEnglishAndNumber(str);
	}
}
