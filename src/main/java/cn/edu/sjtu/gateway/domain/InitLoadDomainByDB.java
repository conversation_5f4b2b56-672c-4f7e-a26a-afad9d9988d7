package cn.edu.sjtu.gateway.domain;

import cn.edu.sjtu.gateway.domain.bean.SimpleSite;

import cn.edu.sjtu.gateway.manager.G;
import cn.edu.sjtu.gateway.vm.util.SpringUtil;
import cn.edu.sjtu.gateway.vm.util.SystemUtil;
import cn.edu.sjtu.gateway.tools.Lang;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 项目启动初始化，从数据库加载域名列表缓存到内存
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class InitLoadDomainByDB {
    //是否已缓存，记录。若为true，则是已缓存
    public static boolean cache = false;

    public InitLoadDomainByDB() {
        new Thread(() -> {
            boolean b = true;
            while (b) {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    log.error("错误信息：------>" + e);
                }
                if (SystemUtil.get("ATTACHMENT_FILE_MODE") == null || SystemUtil.get("ATTACHMENT_FILE_MODE").isEmpty()) {
                    //项目还未启动，system数据都还没有加载，继续等待
                } else {
                    //system数据表数据已加载入内存，可以进行初始化域名数据了，退出等待
                    b = false;
                }
            }

            domainBind();
        }).start();

    }

    /**
     * 启动项目时，进行域名绑定，从数据库中取域名相关数据
     */
    public void domainBind() {
        if (cache) {
            //已缓存，无需再缓存了
            return;
        }
        InitLoadDomainByDB.cache = true;

        List<Map<String, Object>> list = SpringUtil.getSqlService().findMapBySqlQuery("SELECT id,client,domain,bind_domain,state,template_id FROM site");
        for (Map<String, Object> map : list) {
            SimpleSite ss = new SimpleSite();
            ss.setBindDomain(map.get("bind_domain") != null ? map.get("bind_domain").toString() : "");
            ss.setClient(map.get("client") != null ? Lang.stringToInt(map.get("client").toString(), 1) : 1);
            ss.setDomain(map.get("domain") != null ? map.get("domain").toString() : "");
            ss.setSiteid(map.get("id") != null ? Lang.stringToInt(map.get("id").toString(), 0) : 0);
            ss.setState((short) (map.get("state") == null ? 1 : Lang.stringToInt(map.get("state").toString(), 1)));
            Object templateIdObj = map.get("template_id");
            int templateId = 0;
            if (templateIdObj != null) {
                String ti = templateIdObj.toString();
                if (!ti.isEmpty()) {
                    Lang.stringToInt(map.get("template_id").toString(), 0);
                }
            }
            ss.setTemplateId(templateId);

            if (ss.getDomain() != null && !ss.getDomain().isEmpty()) {
                G.putDomain(ss.getDomain(), ss);
            }
            if (ss.getBindDomain() != null && ss.getBindDomain().length() > 2) {
                G.putBindDomain(ss.getBindDomain(), ss);
            }
        }

        log.info("共缓存二级域名：" + G.getDomainSize() + "个， 绑定域名：" + G.getBindDomainSize() + "个");
    }

}
