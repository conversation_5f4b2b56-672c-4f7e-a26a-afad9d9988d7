package cn.edu.sjtu.gateway.domain.controller;

import cn.edu.sjtu.gateway.manager.entity.Site;
import cn.edu.sjtu.gateway.manager.G;
import cn.edu.sjtu.gateway.domain.bean.RequestInfo;
import cn.edu.sjtu.gateway.domain.bean.SimpleSite;
import cn.edu.sjtu.gateway.domain.bean.TextBean;
import cn.edu.sjtu.gateway.domain.util.GainSource;
import cn.edu.sjtu.gateway.domain.vo.SImpleSiteVO;
import cn.edu.sjtu.gateway.utils.CacheUtils;
import cn.edu.sjtu.gateway.utils.IpUtils;
import cn.edu.sjtu.gateway.vm.service.SqlService;
import cn.edu.sjtu.gateway.vm.util.AttachmentUtil;
import cn.edu.sjtu.gateway.utils.IpUtils;
import cn.edu.sjtu.gateway.vm.util.SystemUtil;
import cn.edu.sjtu.gateway.tools.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/")
public class PublicController extends BaseController {
    private final SqlService sqlService;
    private static final Logger log = LoggerFactory.getLogger(PublicController.class);

    public PublicController(SqlService sqlService) {
        this.sqlService = sqlService;
    }

    /**
     * 域名捕获转发
     *
     * @param htmlFile 访问的html文件，如访问c202.html ，则传入c202，会自动拼接上.html。 如果有传递这个参数，优先使用这个参数。如果这个参数没有转入值，那么取实际访问的路径文件
     * @param response 访问的域名，传入如 help.wang.market 如果这个参数有值，则优先使用这个参数。如果这里没有传入值，那么取用户当前访问所使用的的域名
     * @param request  可get传入 domain 模拟访问的域名。可传入自己绑定的域名，也可传入二级域名。如domain=leiwen.wang.market
     */
    @RequestMapping("*.html")
    public String dns(HttpServletRequest request, HttpServletResponse response, Model model,
                      @RequestParam(value = "htmlFile", required = false, defaultValue = "") String htmlFile) {
        if (htmlFile.isEmpty()) {
            htmlFile = request.getServletPath();
            htmlFile = htmlFile.replace("/", "");    //将开头的 /去掉
        } else {
            htmlFile = htmlFile + ".html";
        }

        SImpleSiteVO simpleSiteVO = getCurrentSimpleSiteByCache(request);

        /*
         * 判断，当访问 index.html 有这么几种情况
         * 1. 访问建好的某个网站首页   -- /index.html   有可能用户顶级域名解析过来了，但是还没有在网站后台绑定
         * 2. 访问主站(总管理后台)		/index.html
         * 3. 网站管理后台中，预览网站		/index.html + session|get参数domain,不过既然获取了get传来的参数，那么 simpleSiteVO.result 是 success 的
         *
         */
        if ("index.html".equals(htmlFile)) {
            if (simpleSiteVO.getResult() - SImpleSiteVO.SUCCESS != 0) {
                /*
                 * 不正常，因为没有找到对应的网站，那就应该是有两种情况：
                 * 1. 访问顶级域名，但是域名解析过来了，但是还没有在网站后台绑定，所以找不到网站
                 * 2. 访问主站（管理后台）
                 * 判断一下，到底是1，还是2
                 */
                //如果访问的是首页，且访问的是masterSiteUrl，那么直接到 login.do
                if (SystemUtil.get("MASTER_SITE_URL").indexOf("://" + request.getServerName()) > 0) {
                    // 访问的是 直接跳转到登录页面
                    return redirect("login.naii");
                }
            } else {
                //成功，用户直接访问的某个网站首页，肯定不进入登录页面的
            }
        }

//		htmlFile = htmlFile + ".html";
        //访问日志记录
//		requestLog(request, requestInfo);

        if (simpleSiteVO.getResult() == SImpleSiteVO.FAILURE) {
            //未发现这个域名对应的网站

            //有可能是系统还未安装，判断，如果是，则进行指引安装
            if (isUnInstallRequest()) {
                return "domain/welcome";
            }

            //v4.12增加
            model.addAttribute("managerUrl", getManagerLoginUrl());
            return "domain/notFindDomain";
        }

        //日志
        RequestInfo requestInfo = new RequestInfo();
        requestInfo.setHtmlFile(htmlFile);
        requestInfo.setIp(IpUtils.getIpAddress(request));
        requestInfo.setReferer(request.getHeader("referer"));
        requestInfo.setServerName(simpleSiteVO.getServerName());
        requestInfo.setCrateTime(DateUtil.timeForUnix10());
        requestInfo.setUserAgent(request.getHeader("User-Agent"));
        requestInfo.setCount(1L);
        RequestInfo requestInfo1 = sqlService.findAloneByProperty(RequestInfo.class, "htmlFile", htmlFile);
        if (requestInfo1 == null) {
            sqlService.save(requestInfo);
        } else {
            long count = (requestInfo1.getCount() == null ? 1 : requestInfo1.getCount()) + 1;
            int executeSql = sqlService.executeSql("update request_info set count = " + count + " where html_file = '" + htmlFile + "'");
            log.debug("executeSql: 受影响行：{}", executeSql);
        }
        log.debug("requestInfo: 日志：{}", requestInfo);
        //判断网站的状态，冻结的网站将无法访问
        SimpleSite simpleSite = simpleSiteVO.getSimpleSite();
        if (simpleSite.getState() != null) {
            if (simpleSite.getState() - Site.STATE_FREEZE == 0) {
                //2为冻结，暂停，此时访问网站会直接到冻结的提示页面
                model.addAttribute("url", simpleSiteVO.getServerName() + "/" + htmlFile);
                return "domain/pause";
            }
        }

        String html = GainSource.get(simpleSite, htmlFile).getText();
        if (html == null) {
            if ("index.html".equals(htmlFile)) {
                //如果是首页，但是没有获取到这个页面的数据

                //有可能是系统还未安装，判断，如果是，则进行指引安装
                if (isUnInstallRequest()) {
                    return "domain/welcome";
                }

                //已安装过了，正常访问，那肯定是CMS模式，没有一键部署的缘故，应在404页面中给用户提示
                //v4.12增加
                model.addAttribute("managerUrl", getManagerLoginUrl());
                return "domain/notFindIndexHtml";
            }
            return "domain/404";
        }
        html = replaceHtmlTag(simpleSite, html);

        model.addAttribute("html", html);
        //判断此网站的类型，是PC端还是手机端
        return "domain/displayHtml";
    }

    /**
     * 返回当前网市场云建站系统登录的url网址 (不带 login.naii)
     *
     * @return 登录url地址，如  http://wang.market/
     */
    private String getManagerLoginUrl() {
        return "/";
    }

    /**
     * 判断是否是第一次安装好后访问,判断一下是不是安装之前 install/index.naii 访问的，安装入口还是开启的
     *
     * @return <ul>
     * <li>false： 系统尚未进行安装，会转到 domain/welcome 进行安装指引</li>
     * <li>true： 系统已进行安装了，不需要安装指引</li>
     * </ul>
     */
    private boolean isUnInstallRequest() {
        //可能是第一次安装好后访问,判断一下是不是安装install/index.naii 还是开启的， true：开启的，允许进行安装
        //install/index.naii 还是开启的,那几乎可以肯定，还未安装过，这也就是刚运行来后访问的
        return SystemUtil.get("IW_AUTO_INSTALL_USE") != null && "true".equals(SystemUtil.get("IW_AUTO_INSTALL_USE"));
    }

    /**
     * sitemap.xml展示
     *
     * @param request {@link javax.servlet.http.HttpServletRequest}
     * @param model   {@link org.springframework.ui.Model}
     */
    @RequestMapping(value = "sitemap.xml")
    public String sitemap(HttpServletRequest request, Model model, HttpServletResponse response) {
        SImpleSiteVO simpleSiteVO = getCurrentSimpleSiteByCache(request);

        if (simpleSiteVO.getResult() - SImpleSiteVO.FAILURE == 0) {
            return error404();
        } else {
            //访问日志记录
            alonePageRequestLog(request, "sitemap.xml", simpleSiteVO);
            TextBean textBean = GainSource.get(simpleSiteVO.getSimpleSite(), "sitemap.xml");
            String sitemapXml = textBean.getText();
            if (sitemapXml == null || sitemapXml.isEmpty()) {
                return error404();
            } else {
                model.addAttribute("html", sitemapXml);
                return "domain/displayXml";
            }
        }
    }

    /**
     * robots.txt展示
     *
     * @param request {@link javax.servlet.http.HttpServletRequest}
     * @param model   {@link org.springframework.ui.Model}
     */
    @RequestMapping("robots.txt")
    public String robots(HttpServletRequest request, HttpServletResponse response, Model model) {
        SImpleSiteVO simpleSiteVO = getCurrentSimpleSiteByCache(request);

        if (simpleSiteVO.getResult() - SImpleSiteVO.FAILURE == 0) {
            return error404();
        } else {
            //访问日志记录
            alonePageRequestLog(request, "robots.txt", simpleSiteVO);
            TextBean textBean = GainSource.get(simpleSiteVO.getSimpleSite(), "robots.txt");
            String content = textBean.getText();
            if (content == null || content.isEmpty()) {
                return error404();
            } else {
                model.addAttribute("html", content);
                return "domain/robots";
            }
        }
    }

    /**
     * 单独请求的页面，如 robots.txt 、 sitemap.xml 进行日志记录
     *
     * @param requestFileName 传入如 robots.txt、 sitemap.xml
     * @param simpleSiteVO
     */
    private void alonePageRequestLog(HttpServletRequest request, String requestFileName, SImpleSiteVO simpleSiteVO) {
        RequestInfo requestInfo = new RequestInfo();
        requestInfo.setHtmlFile(requestFileName);
        requestInfo.setIp(IpUtils.getIpAddress(request));
        requestInfo.setReferer(request.getHeader("referer"));
        requestInfo.setServerName(request.getServerName());
        requestInfo.setCrateTime(DateUtil.timeForUnix10());
        requestInfo.setUserAgent(request.getHeader("User-Agent"));

    }

    /**
     * 获取当前用户访问的域名对应的站点
     *
     * @param request 可get传入 domain 模拟访问的域名。可传入自己绑定的域名，也可传入二级域名。如传入leiwen.wang.market
     * @return
     */
    private SImpleSiteVO getCurrentSimpleSite(HttpServletRequest request) {
        //取得要访问的域名
        //get传入的域名，如 pc.wang.market
        String serverName = request.getParameter("domain");
        if (serverName == null || serverName.isEmpty()) {
            //get没传入，那么取当前访问的url的域名
            //访问域名，如 pc.wang.market
            serverName = request.getServerName();
        }

        SImpleSiteVO vo = null;

        //如果是 zvo.cn 或 manager.zvo.cn 这种格式的，那么就是预览网站时用的，是要用到缓存的
        if (serverName.equalsIgnoreCase(G.getAutoAssignDomain()[0]) || serverName.equalsIgnoreCase("manager." + G.getAutoAssignDomain()[0])) {
            //是再预览网站
            if (request.getParameter("domain") != null && request.getParameter("domain").length() > 1) {
                //通过 http://manager.xxx.com/index.html?domain=www.zvo.cn 这样点击预览网站直接预览的。
                //这种方式就是可以理解为正常浏览，走下面
            } else {
                //预览后直接点击某个页面进行查看的，后面已经不带 domain 参数了，那就要从缓存取
                vo = (SImpleSiteVO) request.getSession().getAttribute("SImpleSiteVO");

                //如果session缓存中有，直接将session的返回
                if (vo != null) {
                    vo.setSourceBySession(true);
                } else {
                    vo = new SImpleSiteVO();
                    vo.setBaseVO(SImpleSiteVO.FAILURE, "网站没发现，过会在来看看吧");    //默认是没找到
                }
                return vo;
            }
        }
        /****** session中没有，那么从map中读取 ******/
        vo = new SImpleSiteVO();
        vo.setServerName(serverName);
        SimpleSite simpleSite = null;

        //内部调试使用，本地
        if ("localhost".equals(serverName) || "127.0.0.1".equals(serverName) || IpUtils.getIpaddress().equals(serverName)|| "************".equals(serverName)) {
            vo = (SImpleSiteVO) request.getSession().getAttribute("SImpleSiteVO");

            //如果session缓存中有，直接将session的返回
            if (vo != null) {
                vo.setSourceBySession(true);
            } else {
                vo = new SImpleSiteVO();
                //默认是没找到
                vo.setBaseVO(SImpleSiteVO.FAILURE, "网站没发现，过会在来看看吧");
            }
            return vo;


        } else {
            //正常使用，从域名缓存中找到对应的网站

            //判断当前访问域名是否是使用的二级域名
            String twoDomain = null;
            for (int i = 0; i < G.getAutoAssignDomain().length; i++) {
                if (serverName.contains("." + G.getAutoAssignDomain()[i])) {
                    twoDomain = serverName.replace("." + G.getAutoAssignDomain()[i], "");
                }
            }

            if (twoDomain != null) {
                //用的二级域名
                simpleSite = G.getDomain(twoDomain);
            }
            if (simpleSite == null) {
                //如果没有使用二级域名、或者是二级域名，但在二级域名里面，没找到，那么就从自己绑定的域名里面找
                simpleSite = G.getBindDomain(serverName);
            }
        }

        if (simpleSite == null) {
            vo.setBaseVO(SImpleSiteVO.FAILURE, "网站没发现，过会在来看看吧");
            return vo;
        }
        vo.setSimpleSite(simpleSite);

        //将获取到的加入Session
        request.getSession().setAttribute("SImpleSiteVO", vo);
        return vo;
    }

    private SImpleSiteVO getCurrentSimpleSiteByCache(HttpServletRequest request) {
        //取得要访问的域名
        //get传入的域名，如 pc.wang.market
        String serverName = request.getParameter("domain");
        if (serverName == null || serverName.isEmpty()) {
            //get没传入，那么取当前访问的url的域名
            //访问域名，如 pc.wang.market
            serverName = request.getServerName();
        }

        SImpleSiteVO vo = null;

        //如果是 zvo.cn 或 manager.zvo.cn 这种格式的，那么就是预览网站时用的，是要用到缓存的
        if (serverName.equalsIgnoreCase(G.getAutoAssignDomain()[0]) || serverName.equalsIgnoreCase("manager." + G.getAutoAssignDomain()[0])) {
            //是再预览网站
            if (request.getParameter("domain") != null && request.getParameter("domain").length() > 1) {
                //通过 http://manager.xxx.com/index.html?domain=www.zvo.cn 这样点击预览网站直接预览的。
                //这种方式就是可以理解为正常浏览，走下面
            } else {
                //预览后直接点击某个页面进行查看的，后面已经不带 domain 参数了，那就要从缓存取
                vo = CacheUtils.get("SImpleSiteVO");

                //如果session缓存中有，直接将session的返回
                if (vo != null) {
                    vo.setSourceBySession(true);
                } else {
                    vo = new SImpleSiteVO();
                    vo.setBaseVO(SImpleSiteVO.FAILURE, "网站没发现，过会在来看看吧");    //默认是没找到
                }
                return vo;
            }
        }
        /****** session中没有，那么从map中读取 ******/
        vo = new SImpleSiteVO();
        vo.setServerName(serverName);
        SimpleSite simpleSite = null;

        //内部调试使用，本地
        if ("localhost".equals(serverName) || "127.0.0.1".equals(serverName) || IpUtils.getIpaddress().equals(serverName)|| "************".equals(serverName)) {
            vo = CacheUtils.get("SImpleSiteVO");

            //如果session缓存中有，直接将session的返回
            if (vo != null) {
                vo.setSourceBySession(true);
            } else {
                vo = new SImpleSiteVO();
                //默认是没找到
                vo.setBaseVO(SImpleSiteVO.FAILURE, "网站没发现，过会在来看看吧");
            }
            return vo;


        } else {
            //正常使用，从域名缓存中找到对应的网站

            //判断当前访问域名是否是使用的二级域名
            String twoDomain = null;
            for (int i = 0; i < G.getAutoAssignDomain().length; i++) {
                if (serverName.contains("." + G.getAutoAssignDomain()[i])) {
                    twoDomain = serverName.replace("." + G.getAutoAssignDomain()[i], "");
                }
            }

            if (twoDomain != null) {
                //用的二级域名
                simpleSite = G.getDomain(twoDomain);
            }
            if (simpleSite == null) {
                //如果没有使用二级域名、或者是二级域名，但在二级域名里面，没找到，那么就从自己绑定的域名里面找
                simpleSite = G.getBindDomain(serverName);
            }
        }

        if (simpleSite == null) {
            vo.setBaseVO(SImpleSiteVO.FAILURE, "网站没发现，过会在来看看吧");
            return vo;
        }
        vo.setSimpleSite(simpleSite);
        //将获取到的加入缓存
        CacheUtils.put("SImpleSiteVO", vo);
        return vo;
    }

    /**
     * 替换HTML标签
     *
     * @param simpleSite
     * @param html
     */
    public String replaceHtmlTag(SimpleSite simpleSite, String html) {
        //替换掉 data目录下的缓存js文件
        html = html.replaceAll("src=\"data/", "src=\"" + AttachmentUtil.netUrl() + "site/" + simpleSite.getSiteid() + "/data/");
        //替换图片文件
        html = html.replaceAll("src=\"news/", "src=\"" + AttachmentUtil.netUrl() + "site/" + simpleSite.getSiteid() + "/news/");
        //替换掉HTML的注释 <!-- -->
        //html = html.replaceAll("<!--(.*?)-->", "");
        //替换掉JS的注释 /**/
        html = html.replaceAll("/\\*(.*?)\\*/", "");
        return html;
    }

}
