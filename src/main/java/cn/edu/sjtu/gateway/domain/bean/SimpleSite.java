package cn.edu.sjtu.gateway.domain.bean;

import cn.edu.sjtu.gateway.manager.entity.Site;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * {@link cn.edu.sjtu.gateway.manager.entity.Site} 的简化
 * <AUTHOR>
 */
@Data
public class SimpleSite implements Serializable{
	@Getter
    private String domain;
	@Getter
    private String bindDomain;
	@Setter
    @Getter
    private int client;
	@Setter
    private int templateId;
	@Setter
    @Getter
    private Short state;
    /**
     * -- GETTER --
     *  获取 site.id
     *
     * @return
     */
    @Setter
    @Getter
    private int siteid;
	@Setter
    private Map<String, Map<String,Object>> plugin;
	
	//读数据库的方式载入 SimpleSite ，在系统刚启动时，会自动读数据库中的站点属性，将其载入，分配好域名
	public SimpleSite(Site site) {
		plugin = new HashMap<String, Map<String,Object>>();
		if(site != null){
			domain = site.getDomain();
			client = site.getClient();
			bindDomain = site.getBindDomain();
			if(site.getTemplateId() != null){
				templateId = site.getTemplateId();
			}else{
				templateId = 0;
			}
			state = site.getState();
		}
	}
	
	public SimpleSite() {
		plugin = new HashMap<String, Map<String,Object>>();
	}
	
	/**
	 * 克隆一个 {@link cn.edu.sjtu.gateway.domain.bean.SimpleSite} 对象，将其内容导入到这里面
	 * @param originalSimpleSite 要克隆的对象
	 */
	public void clone(SimpleSite originalSimpleSite){
		this.bindDomain = originalSimpleSite.getBindDomain();
		this.client = originalSimpleSite.getClient();
		this.domain = originalSimpleSite.getDomain();
		this.plugin = originalSimpleSite.getPlugin();
		this.state = originalSimpleSite.getState();
		this.templateId = originalSimpleSite.getTemplateId();
		if(this.plugin == null){
			this.plugin = new HashMap<String, Map<String,Object>>();
		}
	}

    public void setDomain(String domain) {
		if("null".equals(domain)){
			this.domain = "";
		}
		this.domain = domain;
	}

    public void setBindDomain(String bindDomain) {
		if("null".equals(bindDomain)){
			this.bindDomain = "";
		}
		this.bindDomain = bindDomain;
	}
	
	@Column(name = "template_id")
	public int getTemplateId() {
		return templateId;
	}


    /**
	 * 已废弃，使用 PluginCache
	 * 根据 key = plugin.id 来判断是否启用了该插件
	 * @deprecated
	 */
	public Map<String, Map<String,Object>> getPlugin() {
		if(plugin == null){
			return new HashMap<String, Map<String,Object>>();
		}
		return plugin;
	}

    /**
	 * 根据插件id，得到当前网站是否开启了此插件
	 * @param pluginId 插件id，插件的唯一标示。比如 baidushare
	 * @return true: 使用了该插件；  false:未使用此插件
	 */
	public boolean isUsePlugin(String pluginId){
		if(plugin == null){
			return false;
		}
        return plugin.get(pluginId) != null;
    }

    @Override
	public String toString() {
		return "SimpleSite [ domain=" + domain + ", bindDomain=" + bindDomain + ", client=" + client
				+ ", templateId=" + templateId + ", state=" + state + ", plugin=" + plugin + "]";
	}
	
}
