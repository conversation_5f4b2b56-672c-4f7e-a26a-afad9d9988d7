package cn.edu.sjtu.gateway.common.util;

import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.Calendar;
import java.util.Date;

/**
 * 统一日期工具类
 * 整合原有的DateUtil类，提供统一的日期处理功能
 * 
 * <AUTHOR>
 */
@Slf4j
public class DateUtils {
    
    /**
     * 默认日期时间格式
     */
    public static final String DEFAULT_DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    
    /**
     * 默认日期格式
     */
    public static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";
    
    /**
     * 默认时间格式
     */
    public static final String DEFAULT_TIME_FORMAT = "HH:mm:ss";
    
    /**
     * 紧凑日期时间格式
     */
    public static final String COMPACT_DATETIME_FORMAT = "yyyyMMddHHmmss";
    
    /**
     * 紧凑日期格式
     */
    public static final String COMPACT_DATE_FORMAT = "yyyyMMdd";
    
    /**
     * 默认时区
     */
    private static final ZoneId DEFAULT_ZONE_ID = ZoneId.systemDefault();
    
    /**
     * 返回当前13位的Unix时间戳（毫秒）
     * 
     * @return 13位Unix时间戳
     */
    public static long currentTimeMillis() {
        return System.currentTimeMillis();
    }
    
    /**
     * 返回当前10位数的Unix时间戳（秒）
     * 
     * @return 当前时间的10位Unix时间戳
     */
    public static int currentTimeSeconds() {
        return (int) (currentTimeMillis() / 1000);
    }
    
    /**
     * 将10位Unix时间戳转换为格式化日期字符串
     * 
     * @param unixTime 10位Unix时间戳
     * @param format   转换格式，默认为 yyyy-MM-dd HH:mm:ss
     * @return 转换后的日期字符串；若转换失败，返回原时间戳字符串
     */
    public static String formatUnixTime(int unixTime, String format) {
        try {
            // 转为毫秒级时间戳后调用 formatTimestamp
            return formatTimestamp(unixTime * 1000L, format);
        } catch (Exception e) {
            log.error("Unix时间戳格式化失败: {}", unixTime, e);
            return String.valueOf(unixTime);
        }
    }
    
    /**
     * 将10位Unix时间戳转换为默认格式的日期字符串
     * 
     * @param unixTime 10位Unix时间戳
     * @return 转换后的日期字符串
     */
    public static String formatUnixTime(int unixTime) {
        return formatUnixTime(unixTime, DEFAULT_DATETIME_FORMAT);
    }
    
    /**
     * 将 Date 转为10位Unix时间戳
     * 
     * @param date Date 对象
     * @return 10位Unix时间戳；若 Date 为 null，返回 0
     */
    public static int dateToUnixTime(Date date) {
        if (date == null) {
            return 0;
        }
        // 转为秒级时间戳
        return (int) (date.getTime() / 1000);
    }
    
    /**
     * 将Unix时间戳转为文字描述的时间
     * 
     * @param timestamp 时间戳（10位或13位）
     * @param format    转换格式，默认为 {@link #DEFAULT_DATETIME_FORMAT}
     * @return 转换后的日期字符串
     */
    public static String formatTimestamp(long timestamp, String format) {
        if (String.valueOf(timestamp).length() < 10 || String.valueOf(timestamp).length() > 13) {
            throw new IllegalArgumentException("传入的时间戳格式错误：" + timestamp);
        }
        
        format = StringUtils.isEmpty(format) ? DEFAULT_DATETIME_FORMAT : format;
        
        // 转换为13位时间戳
        if (String.valueOf(timestamp).length() == 10) {
            timestamp *= 1000;
        }
        
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format).withZone(DEFAULT_ZONE_ID);
        return formatter.format(Instant.ofEpochMilli(timestamp));
    }
    
    /**
     * 将Unix时间戳转为默认格式的日期字符串
     * 
     * @param timestamp 时间戳（10位或13位）
     * @return 转换后的日期字符串
     */
    public static String formatTimestamp(long timestamp) {
        return formatTimestamp(timestamp, DEFAULT_DATETIME_FORMAT);
    }
    
    /**
     * 获取当前时间，格式化为指定的格式
     * 
     * @param format 时间格式，默认为 {@link #DEFAULT_DATETIME_FORMAT}
     * @return 当前时间字符串
     */
    public static String currentDateTime(String format) {
        format = StringUtils.isEmpty(format) ? DEFAULT_DATETIME_FORMAT : format;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format).withZone(DEFAULT_ZONE_ID);
        return formatter.format(Instant.now());
    }
    
    /**
     * 获取当前时间，使用默认格式
     * 
     * @return 当前时间字符串
     */
    public static String currentDateTime() {
        return currentDateTime(DEFAULT_DATETIME_FORMAT);
    }
    
    /**
     * 获取当前日期，使用默认格式
     * 
     * @return 当前日期字符串
     */
    public static String currentDate() {
        return currentDateTime(DEFAULT_DATE_FORMAT);
    }
    
    /**
     * 获取当前时间，使用默认格式
     * 
     * @return 当前时间字符串
     */
    public static String currentTime() {
        return currentDateTime(DEFAULT_TIME_FORMAT);
    }
    
    /**
     * 获取当前是星期几
     * 
     * @return 星期几，1为周一，7为周日
     */
    public static int currentDayOfWeek() {
        return LocalDate.now(DEFAULT_ZONE_ID).getDayOfWeek().getValue();
    }
    
    /**
     * 解析日期字符串为Date对象
     * 
     * @param dateStr 日期字符串
     * @param format  日期格式
     * @return Date对象，解析失败返回null
     */
    public static Date parseDate(String dateStr, String format) {
        if (StringUtils.isEmpty(dateStr) || StringUtils.isEmpty(format)) {
            return null;
        }
        
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            return sdf.parse(dateStr);
        } catch (ParseException e) {
            log.error("日期解析失败: {} with format: {}", dateStr, format, e);
            return null;
        }
    }
    
    /**
     * 解析日期字符串为Date对象，使用默认格式
     * 
     * @param dateStr 日期字符串
     * @return Date对象，解析失败返回null
     */
    public static Date parseDate(String dateStr) {
        return parseDate(dateStr, DEFAULT_DATETIME_FORMAT);
    }
    
    /**
     * 解析日期字符串为LocalDateTime对象
     * 
     * @param dateStr 日期字符串
     * @param format  日期格式
     * @return LocalDateTime对象，解析失败返回null
     */
    public static LocalDateTime parseLocalDateTime(String dateStr, String format) {
        if (StringUtils.isEmpty(dateStr) || StringUtils.isEmpty(format)) {
            return null;
        }
        
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
            return LocalDateTime.parse(dateStr, formatter);
        } catch (DateTimeParseException e) {
            log.error("LocalDateTime解析失败: {} with format: {}", dateStr, format, e);
            return null;
        }
    }
    
    /**
     * 解析日期字符串为LocalDateTime对象，使用默认格式
     * 
     * @param dateStr 日期字符串
     * @return LocalDateTime对象，解析失败返回null
     */
    public static LocalDateTime parseLocalDateTime(String dateStr) {
        return parseLocalDateTime(dateStr, DEFAULT_DATETIME_FORMAT);
    }
    
    /**
     * 格式化Date对象为字符串
     * 
     * @param date   Date对象
     * @param format 格式
     * @return 格式化后的字符串
     */
    public static String formatDate(Date date, String format) {
        if (date == null) {
            return null;
        }
        
        format = StringUtils.isEmpty(format) ? DEFAULT_DATETIME_FORMAT : format;
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }
    
    /**
     * 格式化Date对象为默认格式字符串
     * 
     * @param date Date对象
     * @return 格式化后的字符串
     */
    public static String formatDate(Date date) {
        return formatDate(date, DEFAULT_DATETIME_FORMAT);
    }
    
    /**
     * 格式化LocalDateTime对象为字符串
     * 
     * @param dateTime LocalDateTime对象
     * @param format   格式
     * @return 格式化后的字符串
     */
    public static String formatLocalDateTime(LocalDateTime dateTime, String format) {
        if (dateTime == null) {
            return null;
        }
        
        format = StringUtils.isEmpty(format) ? DEFAULT_DATETIME_FORMAT : format;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return dateTime.format(formatter);
    }
    
    /**
     * 格式化LocalDateTime对象为默认格式字符串
     * 
     * @param dateTime LocalDateTime对象
     * @return 格式化后的字符串
     */
    public static String formatLocalDateTime(LocalDateTime dateTime) {
        return formatLocalDateTime(dateTime, DEFAULT_DATETIME_FORMAT);
    }
    
    /**
     * 获取指定日期的开始时间（00:00:00）
     * 
     * @param date 指定日期
     * @return 开始时间
     */
    public static Date getStartOfDay(Date date) {
        if (date == null) {
            return null;
        }
        
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }
    
    /**
     * 获取指定日期的结束时间（23:59:59）
     * 
     * @param date 指定日期
     * @return 结束时间
     */
    public static Date getEndOfDay(Date date) {
        if (date == null) {
            return null;
        }
        
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }
    
    /**
     * 计算两个日期之间的天数差
     * 
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 天数差
     */
    public static long daysBetween(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            return 0;
        }
        
        long diffInMillies = Math.abs(endDate.getTime() - startDate.getTime());
        return diffInMillies / (24 * 60 * 60 * 1000);
    }
    
    /**
     * 在指定日期上增加天数
     * 
     * @param date 指定日期
     * @param days 要增加的天数
     * @return 新的日期
     */
    public static Date addDays(Date date, int days) {
        if (date == null) {
            return null;
        }
        
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, days);
        return calendar.getTime();
    }
    
    /**
     * 在指定日期上增加小时
     * 
     * @param date  指定日期
     * @param hours 要增加的小时数
     * @return 新的日期
     */
    public static Date addHours(Date date, int hours) {
        if (date == null) {
            return null;
        }
        
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.HOUR_OF_DAY, hours);
        return calendar.getTime();
    }
    
    /**
     * 在指定日期上增加分钟
     * 
     * @param date    指定日期
     * @param minutes 要增加的分钟数
     * @return 新的日期
     */
    public static Date addMinutes(Date date, int minutes) {
        if (date == null) {
            return null;
        }
        
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, minutes);
        return calendar.getTime();
    }
    
    /**
     * 判断是否是同一天
     * 
     * @param date1 日期1
     * @param date2 日期2
     * @return true:同一天, false:不是同一天
     */
    public static boolean isSameDay(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return false;
        }
        
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
               cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR);
    }
    
    /**
     * 判断日期是否在指定范围内
     * 
     * @param date      要判断的日期
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return true:在范围内, false:不在范围内
     */
    public static boolean isDateInRange(Date date, Date startDate, Date endDate) {
        if (date == null || startDate == null || endDate == null) {
            return false;
        }
        
        return date.compareTo(startDate) >= 0 && date.compareTo(endDate) <= 0;
    }
    
    /**
     * 获取年龄
     * 
     * @param birthDate 出生日期
     * @return 年龄
     */
    public static int getAge(Date birthDate) {
        if (birthDate == null) {
            return 0;
        }
        
        Calendar birth = Calendar.getInstance();
        birth.setTime(birthDate);
        Calendar now = Calendar.getInstance();
        
        int age = now.get(Calendar.YEAR) - birth.get(Calendar.YEAR);
        
        // 如果生日还没过，年龄减1
        if (now.get(Calendar.DAY_OF_YEAR) < birth.get(Calendar.DAY_OF_YEAR)) {
            age--;
        }
        
        return age;
    }
    
    /**
     * 获取本月第一天
     * 
     * @return 本月第一天
     */
    public static Date getFirstDayOfMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return getStartOfDay(calendar.getTime());
    }
    
    /**
     * 获取本月最后一天
     * 
     * @return 本月最后一天
     */
    public static Date getLastDayOfMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        return getEndOfDay(calendar.getTime());
    }
    
    /**
     * 获取本周第一天（周一）
     * 
     * @return 本周第一天
     */
    public static Date getFirstDayOfWeek() {
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        return getStartOfDay(calendar.getTime());
    }
    
    /**
     * 获取本周最后一天（周日）
     * 
     * @return 本周最后一天
     */
    public static Date getLastDayOfWeek() {
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        return getEndOfDay(calendar.getTime());
    }
}
