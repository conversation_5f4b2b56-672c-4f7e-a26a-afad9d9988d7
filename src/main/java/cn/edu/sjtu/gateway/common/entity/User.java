package cn.edu.sjtu.gateway.common.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

/**
 * 用户实体类
 * 临时创建以避免编译错误，实际项目中应该使用现有的User类
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "user")
public class User extends BaseEntity {
    
    /**
     * 用户名
     */
    @Column(name = "username", length = 50, unique = true)
    private String username;
    
    /**
     * 邮箱
     */
    @Column(name = "email", length = 100)
    private String email;
    
    /**
     * 密码（加密后）
     */
    @Column(name = "password", length = 255)
    private String password;
    
    /**
     * 真实姓名
     */
    @Column(name = "real_name", length = 50)
    private String realName;
    
    /**
     * 手机号
     */
    @Column(name = "phone", length = 20)
    private String phone;
    
    /**
     * 用户状态：0-正常，1-禁用，2-冻结
     */
    @Column(name = "status")
    private Integer status = 0;
    
    /**
     * 用户类型：1-普通用户，2-管理员，3-超级管理员
     */
    @Column(name = "user_type")
    private Integer userType = 1;
    
    /**
     * 最后登录时间
     */
    @Column(name = "last_login_time")
    @Temporal(TemporalType.TIMESTAMP)
    private java.util.Date lastLoginTime;
    
    /**
     * 最后登录IP
     */
    @Column(name = "last_login_ip", length = 50)
    private String lastLoginIp;
    
    /**
     * 头像URL
     */
    @Column(name = "avatar_url", length = 200)
    private String avatarUrl;
    
    // 便利方法
    
    /**
     * 判断用户是否正常
     */
    public boolean isNormal() {
        return status != null && status == 0;
    }
    
    /**
     * 判断用户是否被禁用
     */
    public boolean isDisabled() {
        return status != null && status == 1;
    }
    
    /**
     * 判断用户是否被冻结
     */
    public boolean isFrozen() {
        return status != null && status == 2;
    }
    
    /**
     * 判断是否是管理员
     */
    public boolean isAdmin() {
        return userType != null && userType >= 2;
    }
    
    /**
     * 判断是否是超级管理员
     */
    public boolean isSuperAdmin() {
        return userType != null && userType == 3;
    }
}
