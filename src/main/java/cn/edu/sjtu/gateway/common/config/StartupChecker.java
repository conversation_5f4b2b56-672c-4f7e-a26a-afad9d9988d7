package cn.edu.sjtu.gateway.common.config;

import cn.edu.sjtu.gateway.common.util.MigrationChecker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 启动检查器
 * 在应用启动时执行迁移检查
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@Order(1) // 确保在其他组件之前执行
public class StartupChecker implements CommandLineRunner {
    
    @Override
    public void run(String... args) throws Exception {
        log.info("🚀 NAII Gateway 启动检查开始...");
        
        try {
            // 执行完整的迁移检查
            List<MigrationChecker.CheckResult> results = MigrationChecker.runFullCheck();
            
            // 打印检查结果
            MigrationChecker.printResults(results);
            
            // 检查是否有失败项
            boolean hasFailures = results.stream().anyMatch(r -> !r.isSuccess());
            
            if (hasFailures) {
                log.warn("⚠️ 启动检查发现问题，请检查上述失败项");
                // 可以选择是否要阻止应用启动
                // throw new RuntimeException("启动检查失败，应用无法正常启动");
            } else {
                log.info("✅ 启动检查全部通过，应用已成功迁移到新框架！");
            }
            
            // 输出框架信息
            printFrameworkInfo();
            
        } catch (Exception e) {
            log.error("启动检查过程中发生异常", e);
            // 可以选择是否要阻止应用启动
            // throw e;
        }
        
        log.info("🎯 NAII Gateway 启动检查完成");
    }
    
    /**
     * 打印框架信息
     */
    private void printFrameworkInfo() {
        System.out.println("\n" +
                "╔══════════════════════════════════════════════════════════════╗\n" +
                "║                    NAII Gateway Framework                   ║\n" +
                "║                                                              ║\n" +
                "║  🎉 恭喜！您的应用已成功迁移到新的统一框架！                    ║\n" +
                "║                                                              ║\n" +
                "║  新框架特性：                                                  ║\n" +
                "║  ✅ 统一的全局配置管理                                         ║\n" +
                "║  ✅ 统一的工具类库                                            ║\n" +
                "║  ✅ 统一的实体基类                                            ║\n" +
                "║  ✅ 统一的控制器基类                                          ║\n" +
                "║  ✅ 统一的响应格式                                            ║\n" +
                "║  ✅ 统一的异常处理                                            ║\n" +
                "║                                                              ║\n" +
                "║  版本: " + GlobalConfig.Version.CURRENT_VERSION + "                                                   ║\n" +
                "║  VM版本: " + GlobalConfig.Version.VM_VERSION + "                                                ║\n" +
                "║                                                              ║\n" +
                "╚══════════════════════════════════════════════════════════════╝\n");
    }
}
