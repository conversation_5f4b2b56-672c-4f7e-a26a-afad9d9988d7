package cn.edu.sjtu.gateway.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.PostConstruct;

/**
 * Web配置类
 * 配置全局异常处理和其他Web相关设置
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class WebConfig implements WebMvcConfigurer {

    /**
     * 初始化全局配置
     */
    @PostConstruct
    public void initGlobalConfig() {
        log.info("初始化全局配置");
        try {
            GlobalConfig.logConfigInfo();
        } catch (Exception e) {
            log.warn("全局配置初始化失败", e);
        }
    }
}
