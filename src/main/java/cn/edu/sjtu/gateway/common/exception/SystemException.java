package cn.edu.sjtu.gateway.common.exception;

/**
 * 系统异常类
 * 用于处理系统级别的异常情况
 * 
 * <AUTHOR>
 */
public class SystemException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误码
     */
    private int code;
    
    /**
     * 错误消息
     */
    private String message;
    
    /**
     * 构造函数
     */
    public SystemException() {
        super();
        this.code = 500;
        this.message = "系统异常";
    }
    
    /**
     * 构造函数
     */
    public SystemException(String message) {
        super(message);
        this.code = 500;
        this.message = message;
    }
    
    /**
     * 构造函数
     */
    public SystemException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
    
    /**
     * 构造函数
     */
    public SystemException(String message, Throwable cause) {
        super(message, cause);
        this.code = 500;
        this.message = message;
    }
    
    /**
     * 构造函数
     */
    public SystemException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }
    
    /**
     * 构造函数
     */
    public SystemException(Throwable cause) {
        super(cause);
        this.code = 500;
        this.message = "系统异常";
    }
    
    public int getCode() {
        return code;
    }
    
    public void setCode(int code) {
        this.code = code;
    }
    
    @Override
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    /**
     * 创建系统异常
     */
    public static SystemException of(String message) {
        return new SystemException(message);
    }
    
    /**
     * 创建系统异常
     */
    public static SystemException of(int code, String message) {
        return new SystemException(code, message);
    }
    
    /**
     * 创建系统异常
     */
    public static SystemException of(String message, Throwable cause) {
        return new SystemException(message, cause);
    }
    
    /**
     * 创建系统异常
     */
    public static SystemException of(int code, String message, Throwable cause) {
        return new SystemException(code, message, cause);
    }
    
    // 常用的系统异常
    
    /**
     * 数据库连接异常
     */
    public static SystemException databaseConnectionError() {
        return new SystemException(500, "数据库连接异常");
    }
    
    /**
     * 数据库操作异常
     */
    public static SystemException databaseOperationError(String message) {
        return new SystemException(500, "数据库操作异常: " + message);
    }
    
    /**
     * 配置错误
     */
    public static SystemException configError(String message) {
        return new SystemException(500, "配置错误: " + message);
    }
    
    /**
     * 网络异常
     */
    public static SystemException networkError(String message) {
        return new SystemException(500, "网络异常: " + message);
    }
    
    /**
     * IO异常
     */
    public static SystemException ioError(String message) {
        return new SystemException(500, "IO异常: " + message);
    }
    
    /**
     * 序列化异常
     */
    public static SystemException serializationError(String message) {
        return new SystemException(500, "序列化异常: " + message);
    }
    
    /**
     * 反序列化异常
     */
    public static SystemException deserializationError(String message) {
        return new SystemException(500, "反序列化异常: " + message);
    }
    
    /**
     * 缓存异常
     */
    public static SystemException cacheError(String message) {
        return new SystemException(500, "缓存异常: " + message);
    }
    
    /**
     * 消息队列异常
     */
    public static SystemException messageQueueError(String message) {
        return new SystemException(500, "消息队列异常: " + message);
    }
    
    /**
     * 第三方服务异常
     */
    public static SystemException thirdPartyServiceError(String message) {
        return new SystemException(500, "第三方服务异常: " + message);
    }
    
    /**
     * 系统初始化异常
     */
    public static SystemException initializationError(String message) {
        return new SystemException(500, "系统初始化异常: " + message);
    }
    
    /**
     * 系统关闭异常
     */
    public static SystemException shutdownError(String message) {
        return new SystemException(500, "系统关闭异常: " + message);
    }
    
    /**
     * 内存不足异常
     */
    public static SystemException outOfMemoryError() {
        return new SystemException(500, "系统内存不足");
    }
    
    /**
     * 线程池异常
     */
    public static SystemException threadPoolError(String message) {
        return new SystemException(500, "线程池异常: " + message);
    }
    
    /**
     * 定时任务异常
     */
    public static SystemException scheduledTaskError(String message) {
        return new SystemException(500, "定时任务异常: " + message);
    }
}
