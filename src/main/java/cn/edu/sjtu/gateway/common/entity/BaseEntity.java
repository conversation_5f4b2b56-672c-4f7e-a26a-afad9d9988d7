package cn.edu.sjtu.gateway.common.entity;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 实体基类
 * 提供通用的实体字段和方法
 * 
 * <AUTHOR>
 */
@Data
@MappedSuperclass
public abstract class BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    
    /**
     * 创建时间
     */
    @Column(name = "create_time", updatable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;
    
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;
    
    /**
     * 创建者ID
     */
    @Column(name = "create_by", updatable = false)
    private Integer createBy;
    
    /**
     * 更新者ID
     */
    @Column(name = "update_by")
    private Integer updateBy;
    
    /**
     * 版本号（用于乐观锁）
     */
    @Version
    @Column(name = "version")
    private Integer version = 0;
    
    /**
     * 是否删除（逻辑删除标记）
     * 0: 未删除, 1: 已删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted = 0;
    
    /**
     * 备注
     */
    @Column(name = "remark", length = 500)
    private String remark;
    
    /**
     * 持久化前的操作
     */
    @PrePersist
    protected void onCreate() {
        Date now = new Date();
        if (createTime == null) {
            createTime = now;
        }
        if (updateTime == null) {
            updateTime = now;
        }
        if (isDeleted == null) {
            isDeleted = 0;
        }
        if (version == null) {
            version = 0;
        }
    }
    
    /**
     * 更新前的操作
     */
    @PreUpdate
    protected void onUpdate() {
        updateTime = new Date();
    }
    
    /**
     * 判断是否为新实体（ID为空）
     * 
     * @return true: 新实体, false: 已存在的实体
     */
    public boolean isNew() {
        return id == null;
    }
    
    /**
     * 判断是否已删除
     * 
     * @return true: 已删除, false: 未删除
     */
    public boolean isDeleted() {
        return isDeleted != null && isDeleted == 1;
    }
    
    /**
     * 标记为删除
     */
    public void markAsDeleted() {
        this.isDeleted = 1;
        this.updateTime = new Date();
    }
    
    /**
     * 标记为未删除
     */
    public void markAsNotDeleted() {
        this.isDeleted = 0;
        this.updateTime = new Date();
    }
}
