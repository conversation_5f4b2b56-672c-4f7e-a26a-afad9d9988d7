package cn.edu.sjtu.gateway.common.util;

import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 统一字符串工具类
 * 整合原有的多个StringUtil类，提供统一的字符串处理功能
 * 
 * <AUTHOR>
 */
@Slf4j
public class StringUtils {
    
    /**
     * 26个英文字母
     */
    private static final char[] AZCHAR_26 = {'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'};
    
    /**
     * 36个字符：26个英文字母 + 10个数字
     */
    private static final char[] AZ09CHAR_36 = {'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'};
    
    /**
     * 判断字符串是否为空或null
     * 
     * @param str 要判断的字符串
     * @return true:为空, false:不为空
     */
    public static boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 判断字符串是否不为空
     * 
     * @param str 要判断的字符串
     * @return true:不为空, false:为空
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }
    
    /**
     * 判断字符串是否是英文+数字
     * 
     * @param str 要判断的字符串
     * @return true:只包含英文和数字, false:包含其他字符
     */
    public static boolean isEnglishAndNumber(String str) {
        if (isEmpty(str)) {
            return false;
        }
        return str.matches("[a-zA-Z0-9]+");
    }
    
    /**
     * 两个String字符串比较是否相等。可一个为null、一个不为null进行对比
     * 
     * @param s1 要比较的第一个字符串
     * @param s2 要比较的第二个字符串
     * @return true:相等
     */
    public static boolean equals(String s1, String s2) {
        return equals(s1, s2, false);
    }
    
    /**
     * 两个String字符串比较是否相等。可一个为null、一个不为null进行对比
     * 
     * @param s1          要比较的第一个字符串
     * @param s2          要比较的第二个字符串
     * @param removeBlank 是否移除空格、换行、tab缩进等空白符号，true为自动将两个字符串的空符移除掉后在进行比较
     * @return true:相等
     */
    public static boolean equals(String s1, String s2, boolean removeBlank) {
        return equals(s1, s2, removeBlank, false);
    }
    
    /**
     * 两个String字符串比较是否相等。可一个为null、一个不为null进行对比
     * 
     * @param s1          要比较的第一个字符串
     * @param s2          要比较的第二个字符串
     * @param removeBlank 是否移除空格、换行、tab缩进等空白符号，true为自动将两个字符串的空符移除掉后在进行比较
     * @param ignoreNull  忽略空，若为true，即 null 跟 空字符串"" 视为相等的。若是字符串为"null"，也会自动视为空字符串
     * @return true:相等
     */
    public static boolean equals(String s1, String s2, boolean removeBlank, boolean ignoreNull) {
        if (ignoreNull) {
            if (s1 == null || "null".equals(s1)) {
                s1 = "";
            }
            if (s2 == null || "null".equals(s2)) {
                s2 = "";
            }
        } else {
            // 先进行为空判断
            if (s1 == null && s2 != null) {
                return false;
            }
            if (s1 != null && s2 == null) {
                return false;
            }
            if (s1 == null) {
                return true;
            }
        }
        
        if (removeBlank) {
            s1 = s1.replaceAll("\\s", "");
            s2 = s2.replaceAll("\\s", "");
        }
        
        return s1.equals(s2);
    }
    
    /**
     * 生成随机长度的英文（a－z，26个英文字母）
     * 
     * @param length 生成字符串的长度
     * @return 字符串
     */
    public static String getRandomAZ(int length) {
        final int maxNum = 26;
        return getString(length, maxNum, AZCHAR_26);
    }
    
    /**
     * 生成随机长度的英文+数字（0-9 10个数字、a－z，26个英文字母）
     * 
     * @param length 生成字符串的长度
     * @return 字符串
     */
    public static String getRandom09AZ(int length) {
        final int maxNum = 36;
        return getString(length, maxNum, AZ09CHAR_36);
    }
    
    /**
     * 生成随机字符串的通用方法
     */
    private static String getString(int length, int maxNum, char[] charArray) {
        int i;  // 生成的随机数
        int count = 0; // 生成的密码的长度
        
        StringBuilder pwd = new StringBuilder();
        Random r = new Random();
        while (count < length) {
            // 生成随机数，取绝对值，防止生成负数
            i = Math.abs(r.nextInt(maxNum));  // 生成的数最大为maxNum-1
            pwd.append(charArray[i]);
            count++;
        }
        return pwd.toString();
    }
    
    /**
     * 过滤XSS攻击有关的字符。将其转化为无效标签。过滤script、frame、;、等
     * 
     * @param text 要过滤的原始字符
     * @return 生成的无XSS的安全字符
     */
    public static String filterXss(String text) {
        if (text == null) {
            return null;
        }
        
        text = text.replaceAll(";", "；");
        
        // 过滤，忽略大小写
        String[] filterTagArray = {"script", "frame "};
        for (String s : filterTagArray) {
            Pattern p = Pattern.compile(s, Pattern.CASE_INSENSITIVE);
            Matcher m = p.matcher(text);
            text = m.replaceAll("xss_" + s);
        }
        
        return text;
    }
    
    /**
     * 对某个字符串进行位移操作，利用位移来进行简单的加密、解密
     * 
     * @param text     要加密或者解密的字符串
     * @param shiftNum 位移的值
     * @return 加密或者解密的字符串
     */
    public static String encrypt(String text, int shiftNum) {
        if (isEmpty(text)) {
            return text;
        }
        
        char[] array = text.toCharArray(); // 获取字符数组
        for (int i = 0; i < array.length; i++) { // 遍历字符数组
            array[i] = (char) (array[i] ^ 1); // 对每个数组元素进行异或运算
        }
        return new String(array);
    }
    
    /**
     * UTF-8格式汉字转换为%E4%BD%A0形式
     * 
     * @param content 要转换的字符串内容
     * @return String 转换好的字符串
     */
    public static String stringToUrl(String content) {
        if (isEmpty(content)) {
            return content;
        }
        
        String str = URLEncoder.encode(content, StandardCharsets.UTF_8);
        // 空格会被替换为+号，所以将+进行替换（原本+会变为%2B所以不担心替换错）
        str = str.replaceAll("\\+", "%20");
        return str;
    }
    
    /**
     * 将字符串转换为InputStream
     * 
     * @param text   要转换的字符串
     * @param encode 编码格式
     * @return InputStream
     * @throws UnsupportedEncodingException 编码异常
     */
    public static InputStream stringToInputStream(String text, String encode) throws UnsupportedEncodingException {
        if (isEmpty(text)) {
            return new ByteArrayInputStream(new byte[0]);
        }
        return new ByteArrayInputStream(text.getBytes(encode));
    }
    
    /**
     * 将字符串转换为InputStream，使用UTF-8编码
     * 
     * @param text 要转换的字符串
     * @return InputStream
     */
    public static InputStream stringToInputStream(String text) {
        try {
            return stringToInputStream(text, StandardCharsets.UTF_8.name());
        } catch (UnsupportedEncodingException e) {
            log.error("字符串转换为InputStream失败", e);
            return new ByteArrayInputStream(new byte[0]);
        }
    }
    
    /**
     * 截取字符串，如果超过指定长度则截取并添加省略号
     * 
     * @param text      原字符串
     * @param maxLength 最大长度
     * @return 截取后的字符串
     */
    public static String truncate(String text, int maxLength) {
        if (isEmpty(text) || text.length() <= maxLength) {
            return text;
        }
        return text.substring(0, maxLength) + "...";
    }
    
    /**
     * 首字母大写
     * 
     * @param str 要处理的字符串
     * @return 首字母大写的字符串
     */
    public static String capitalize(String str) {
        if (isEmpty(str)) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }
    
    /**
     * 首字母小写
     * 
     * @param str 要处理的字符串
     * @return 首字母小写的字符串
     */
    public static String uncapitalize(String str) {
        if (isEmpty(str)) {
            return str;
        }
        return str.substring(0, 1).toLowerCase() + str.substring(1);
    }
    
    /**
     * 驼峰命名转下划线命名
     * 
     * @param camelCase 驼峰命名字符串
     * @return 下划线命名字符串
     */
    public static String camelToUnderscore(String camelCase) {
        if (isEmpty(camelCase)) {
            return camelCase;
        }
        return camelCase.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }
    
    /**
     * 下划线命名转驼峰命名
     * 
     * @param underscore 下划线命名字符串
     * @return 驼峰命名字符串
     */
    public static String underscoreToCamel(String underscore) {
        if (isEmpty(underscore)) {
            return underscore;
        }
        
        StringBuilder result = new StringBuilder();
        String[] parts = underscore.split("_");
        
        for (int i = 0; i < parts.length; i++) {
            if (i == 0) {
                result.append(parts[i].toLowerCase());
            } else {
                result.append(capitalize(parts[i].toLowerCase()));
            }
        }
        
        return result.toString();
    }
    
    /**
     * 移除字符串中的HTML标签
     * 
     * @param html HTML字符串
     * @return 纯文本字符串
     */
    public static String removeHtmlTags(String html) {
        if (isEmpty(html)) {
            return html;
        }
        return html.replaceAll("<[^>]+>", "");
    }
    
    /**
     * 检查字符串是否包含中文字符
     * 
     * @param str 要检查的字符串
     * @return true:包含中文, false:不包含中文
     */
    public static boolean containsChinese(String str) {
        if (isEmpty(str)) {
            return false;
        }
        Pattern pattern = Pattern.compile("[\u4e00-\u9fa5]");
        return pattern.matcher(str).find();
    }
    
    /**
     * 获取字符串的字节长度（中文字符按2个字节计算）
     * 
     * @param str 要计算的字符串
     * @return 字节长度
     */
    public static int getByteLength(String str) {
        if (isEmpty(str)) {
            return 0;
        }
        
        int length = 0;
        for (char c : str.toCharArray()) {
            if (c >= 0x4e00 && c <= 0x9fa5) {
                // 中文字符
                length += 2;
            } else {
                // 英文字符
                length += 1;
            }
        }
        return length;
    }
    
    /**
     * 安全的字符串转整数
     * 
     * @param str          要转换的字符串
     * @param defaultValue 默认值
     * @return 转换后的整数
     */
    public static int toInt(String str, int defaultValue) {
        if (isEmpty(str)) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(str.trim());
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }
    
    /**
     * 安全的字符串转长整数
     * 
     * @param str          要转换的字符串
     * @param defaultValue 默认值
     * @return 转换后的长整数
     */
    public static long toLong(String str, long defaultValue) {
        if (isEmpty(str)) {
            return defaultValue;
        }
        try {
            return Long.parseLong(str.trim());
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }
    
    /**
     * 安全的字符串转布尔值
     * 
     * @param str          要转换的字符串
     * @param defaultValue 默认值
     * @return 转换后的布尔值
     */
    public static boolean toBoolean(String str, boolean defaultValue) {
        if (isEmpty(str)) {
            return defaultValue;
        }
        String trimmed = str.trim().toLowerCase();
        if ("true".equals(trimmed) || "1".equals(trimmed) || "yes".equals(trimmed)) {
            return true;
        } else if ("false".equals(trimmed) || "0".equals(trimmed) || "no".equals(trimmed)) {
            return false;
        }
        return defaultValue;
    }
}
