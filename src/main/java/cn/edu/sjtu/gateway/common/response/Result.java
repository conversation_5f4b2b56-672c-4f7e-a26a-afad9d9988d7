package cn.edu.sjtu.gateway.common.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

/**
 * 统一响应结果类
 * 提供标准的API响应格式
 * 
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Result<T> implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 成功状态码
     */
    public static final int SUCCESS_CODE = 200;
    
    /**
     * 失败状态码
     */
    public static final int FAILURE_CODE = 500;
    
    /**
     * 参数错误状态码
     */
    public static final int PARAM_ERROR_CODE = 400;
    
    /**
     * 未授权状态码
     */
    public static final int UNAUTHORIZED_CODE = 401;
    
    /**
     * 禁止访问状态码
     */
    public static final int FORBIDDEN_CODE = 403;
    
    /**
     * 资源不存在状态码
     */
    public static final int NOT_FOUND_CODE = 404;
    
    /**
     * 响应状态码
     */
    private int code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 时间戳
     */
    private long timestamp;
    
    /**
     * 请求ID（用于链路追踪）
     */
    private String requestId;
    
    /**
     * 私有构造函数
     */
    private Result() {
        this.timestamp = System.currentTimeMillis();
    }
    
    /**
     * 私有构造函数
     */
    private Result(int code, String message, T data) {
        this();
        this.code = code;
        this.message = message;
        this.data = data;
    }
    
    /**
     * 成功响应（无数据）
     */
    public static <T> Result<T> success() {
        return new Result<>(SUCCESS_CODE, "操作成功", null);
    }
    
    /**
     * 成功响应（带数据）
     */
    public static <T> Result<T> success(T data) {
        return new Result<>(SUCCESS_CODE, "操作成功", data);
    }
    
    /**
     * 成功响应（带消息）
     */
    public static <T> Result<T> success(String message) {
        return new Result<>(SUCCESS_CODE, message, null);
    }
    
    /**
     * 成功响应（带数据和消息）
     */
    public static <T> Result<T> success(T data, String message) {
        return new Result<>(SUCCESS_CODE, message, data);
    }
    
    /**
     * 失败响应
     */
    public static <T> Result<T> failure() {
        return new Result<>(FAILURE_CODE, "操作失败", null);
    }
    
    /**
     * 失败响应（带消息）
     */
    public static <T> Result<T> failure(String message) {
        return new Result<>(FAILURE_CODE, message, null);
    }
    
    /**
     * 失败响应（带状态码和消息）
     */
    public static <T> Result<T> failure(int code, String message) {
        return new Result<>(code, message, null);
    }
    
    /**
     * 失败响应（带状态码、消息和数据）
     */
    public static <T> Result<T> failure(int code, String message, T data) {
        return new Result<>(code, message, data);
    }
    
    /**
     * 参数错误响应
     */
    public static <T> Result<T> paramError() {
        return new Result<>(PARAM_ERROR_CODE, "参数错误", null);
    }
    
    /**
     * 参数错误响应（带消息）
     */
    public static <T> Result<T> paramError(String message) {
        return new Result<>(PARAM_ERROR_CODE, message, null);
    }
    
    /**
     * 未授权响应
     */
    public static <T> Result<T> unauthorized() {
        return new Result<>(UNAUTHORIZED_CODE, "未授权访问", null);
    }
    
    /**
     * 未授权响应（带消息）
     */
    public static <T> Result<T> unauthorized(String message) {
        return new Result<>(UNAUTHORIZED_CODE, message, null);
    }
    
    /**
     * 禁止访问响应
     */
    public static <T> Result<T> forbidden() {
        return new Result<>(FORBIDDEN_CODE, "禁止访问", null);
    }
    
    /**
     * 禁止访问响应（带消息）
     */
    public static <T> Result<T> forbidden(String message) {
        return new Result<>(FORBIDDEN_CODE, message, null);
    }
    
    /**
     * 资源不存在响应
     */
    public static <T> Result<T> notFound() {
        return new Result<>(NOT_FOUND_CODE, "资源不存在", null);
    }
    
    /**
     * 资源不存在响应（带消息）
     */
    public static <T> Result<T> notFound(String message) {
        return new Result<>(NOT_FOUND_CODE, message, null);
    }
    
    /**
     * 自定义响应
     */
    public static <T> Result<T> custom(int code, String message, T data) {
        return new Result<>(code, message, data);
    }
    
    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return this.code == SUCCESS_CODE;
    }
    
    /**
     * 判断是否失败
     */
    public boolean isFailure() {
        return !isSuccess();
    }
    
    /**
     * 设置请求ID
     */
    public Result<T> requestId(String requestId) {
        this.requestId = requestId;
        return this;
    }
    
    /**
     * 设置消息
     */
    public Result<T> message(String message) {
        this.message = message;
        return this;
    }
    
    /**
     * 设置数据
     */
    public Result<T> data(T data) {
        this.data = data;
        return this;
    }
    
    /**
     * 转换为其他类型的Result
     */
    public <R> Result<R> convert() {
        return new Result<>(this.code, this.message, null);
    }
    
    /**
     * 转换为其他类型的Result（带数据）
     */
    public <R> Result<R> convert(R data) {
        return new Result<>(this.code, this.message, data);
    }
    
    @Override
    public String toString() {
        return "Result{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", timestamp=" + timestamp +
                ", requestId='" + requestId + '\'' +
                '}';
    }
}
