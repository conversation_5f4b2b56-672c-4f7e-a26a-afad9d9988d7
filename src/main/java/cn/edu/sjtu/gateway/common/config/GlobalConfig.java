package cn.edu.sjtu.gateway.common.config;

import cn.edu.sjtu.gateway.common.manager.DomainManager;
// import cn.edu.sjtu.gateway.manager.entity.Site; // 如果Site类不存在，请注释掉
// import cn.edu.sjtu.gateway.vm.util.SystemUtil; // 如果SystemUtil不存在，请注释掉
import cn.edu.sjtu.gateway.manager.entity.Site;
import lombok.extern.slf4j.Slf4j;

import java.io.File;

/**
 * 统一全局配置类
 * 整合原有的多个G.java配置类，提供统一的配置管理
 *
 * <AUTHOR>
 */
@Slf4j
public class GlobalConfig {

    /**
     * 系统版本信息
     */
    public static class Version {
        /**
         * 当前的版本号
         * 1.x为通用模版时代； 2.x为cms时代, 3.x 整体架构及云模块使用升级，
         * 4.x整体架构升级，由深度依赖阿里云抽离， 5.x 插件时代，将更多的功能以插件的形式来做
         */
        public static final String CURRENT_VERSION = "5.0.0";

        /**
         * VM框架版本
         */
        public static final String VM_VERSION = "3.12";
    }

    /**
     * 路径配置
     */
    public static class Path {
        /**
         * 站点信息缓存路径
         */
        public static final String CACHE_FILE = "cache/data/";

        /**
         * JS缓存路径
         */
        public static final String CACHE_JS_FILE = "cache" + File.separator + "js" + File.separator;

        /**
         * 输入模型缓存路径
         */
        public static final String CACHE_INPUT_MODEL_FILE = "cache" + File.separator + "inputmodel" + File.separator;

        /**
         * 输入模型图片缓存路径
         */
        public static final String CACHE_INPUT_MODEL_IMAGE = "cache" + File.separator + "naii" + File.separator;

        /**
         * 输入模型模板文件路径
         */
        public static final String INPUT_MODEL_TEMPLATE_FILE = "templates" + File.separator + "inputmodel" + File.separator + "default_template.html";

        /**
         * 默认站点栏目图标URL
         */
        public static final String DEFAULT_SITE_COLUMN_ICON_URL = "res/glyph-icons/world.png";
    }

    /**
     * 模板配置
     */
    public static class Template {
        /**
         * PC端的默认模版编号
         */
        public static final int PC_DEFAULT = 6;

        /**
         * 手机端的默认模版编号
         */
        public static final int WAP_DEFAULT = 1;

        /**
         * 手机版本的新闻、图文列表，每页显示的条数
         */
        public static final int PAGE_WAP_NUM = 12;
    }

    /**
     * 图片尺寸配置
     */
    public static class ImageSize {
        /**
         * siteColumn的icon图标上传后缩放的最大宽度
         */
        public static final int SITECOLUMN_ICON_MAXWIDTH = 100;

        /**
         * 轮播图的最大宽度
         */
        public static final int CAROUSEL_MAXWIDTH = 2600;

        /**
         * 新闻图片的titlepic的最大宽度
         */
        public static final int NEWS_TITLEPIC_MAXWIDTH = 1000;

        /**
         * PC端 siteColumn的icon图标上传后缩放的最大宽度
         */
        public static final int SITECOLUMN_ICON_MAXWIDTH_PC = 600;

        /**
         * PC端轮播图的最大宽度
         */
        public static final int CAROUSEL_MAXWIDTH_PC = 4000;
    }

    /**
     * 业务配置
     */
    public static class Business {
        /**
         * 代理开通下级代理，消耗站币数量
         */
        public static final int AGENCY_ADD_SUB_AGENCY_SITE_SIZE = 20;

        /**
         * 普通注册成为会员后，拥有的存储空间(MB)
         */
        public static final int REG_GENERAL_OSS_HAVE = 1000;

        /**
         * 网站是否是自己使用
         */
        public static final boolean SITE_MYSELF_USED = true;

        /**
         * 是否显示版权信息
         */
        public static boolean copyright = true;
    }

    /**
     * CDN配置
     */
    public static class CDN {
        /**
         * CDN缓存的资源文件，包括框架的js、css文件、模版style.css文件等
         */
        public static final String RES_CDN_DOMAIN = "http://res.weiunity.com/";
    }

    /**
     * 获取系统属性（临时实现）
     * TODO: 需要与现有的SystemUtil集成
     */
    private static String getSystemProperty(String key, String defaultValue) {
        // 优先从系统属性获取
        String value = System.getProperty(key);
        if (value != null) {
            return value;
        }

        // 从环境变量获取
        value = System.getenv(key);
        if (value != null) {
            return value;
        }

        // 返回默认值
        return defaultValue;
    }

    /**
     * 获取主域名，即 AUTO_ASSIGN_DOMAIN 配置的第一个域名
     * 例如，Global.get("AUTO_ASSIGN_DOMAIN") 为 ： wang.market,wscso.com
     *
     * @return 返回如 wang.market
     */
    public static String getFirstAutoAssignDomain() {
        // TODO: 实现获取系统配置的逻辑
        // String autoAssignDomain = SystemUtil.get("AUTO_ASSIGN_DOMAIN");
        String autoAssignDomain = getSystemProperty("AUTO_ASSIGN_DOMAIN", "localhost");
        if (autoAssignDomain != null) {
            if (autoAssignDomain.indexOf(",") > 0) {
                // 如果有多个，那么只取第一个
                String[] domains = autoAssignDomain.split(",");
                return domains[0];
            } else {
                return autoAssignDomain;
            }
        }
        return "请进入总管理后台，系统管理-系统变量下，修改变量名为 AUTO_ASSIGN_DOMAIN 的值";
    }

    /**
     * 获取泛解析的主域名列表，获取到的域名列表便是分配给用户的二级域名。
     * 注意的是，第一个域名会作为网站的官分配的二级域名，在程序里会体现第一个域名。
     * 其他的都是备用的，在程序中不会体现，只有用户使用二级域名访问时才会有效
     */
    public static String[] getAutoAssignDomain() {
        // TODO: 实现获取系统配置的逻辑
        // String autoAssignDomain = SystemUtil.get("AUTO_ASSIGN_DOMAIN");
        String autoAssignDomain = getSystemProperty("AUTO_ASSIGN_DOMAIN", "localhost");
        if (autoAssignDomain != null) {
            return autoAssignDomain.split(",");
        }
        return new String[0];
    }

    /**
     * 轮播图在OSS上的存储路径
     *
     * @param siteId 站点ID
     * @return 存储路径
     */
    public static String getCarouselPath(Site site) {
        return "site/" + site.getId() + "/carousel/";
    }

    /**
     * 获取当前版本信息
     */
    public static String getCurrentVersion() {
        return Version.CURRENT_VERSION;
    }

    /**
     * 获取VM框架版本信息
     */
    public static String getVMVersion() {
        return Version.VM_VERSION;
    }

    /**
     * 获取域名管理器
     */
    public static DomainManager getDomainManager() {
        return new DomainManager();
    }

    /**
     * 日志输出当前配置信息
     */
    public static void logConfigInfo() {
        log.info("=== NAII Gateway 全局配置信息 ===");
        log.info("系统版本: {}", Version.CURRENT_VERSION);
        log.info("VM版本: {}", Version.VM_VERSION);
        log.info("缓存路径: {}", Path.CACHE_FILE);
        log.info("主域名: {}", getFirstAutoAssignDomain());
        log.info("域名缓存: {}", DomainManager.getCacheStats());
        log.info("================================");
    }
}
