package cn.edu.sjtu.gateway.common.exception;

/**
 * 业务异常类
 * 用于处理业务逻辑中的异常情况
 * 
 * <AUTHOR>
 */
public class BusinessException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误码
     */
    private int code;
    
    /**
     * 错误消息
     */
    private String message;
    
    /**
     * 构造函数
     */
    public BusinessException() {
        super();
        this.code = 400;
        this.message = "业务异常";
    }
    
    /**
     * 构造函数
     */
    public BusinessException(String message) {
        super(message);
        this.code = 400;
        this.message = message;
    }
    
    /**
     * 构造函数
     */
    public BusinessException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
    
    /**
     * 构造函数
     */
    public BusinessException(String message, Throwable cause) {
        super(message, cause);
        this.code = 400;
        this.message = message;
    }
    
    /**
     * 构造函数
     */
    public BusinessException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }
    
    /**
     * 构造函数
     */
    public BusinessException(Throwable cause) {
        super(cause);
        this.code = 400;
        this.message = "业务异常";
    }
    
    public int getCode() {
        return code;
    }
    
    public void setCode(int code) {
        this.code = code;
    }
    
    @Override
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    /**
     * 创建业务异常
     */
    public static BusinessException of(String message) {
        return new BusinessException(message);
    }
    
    /**
     * 创建业务异常
     */
    public static BusinessException of(int code, String message) {
        return new BusinessException(code, message);
    }
    
    /**
     * 创建业务异常
     */
    public static BusinessException of(String message, Throwable cause) {
        return new BusinessException(message, cause);
    }
    
    /**
     * 创建业务异常
     */
    public static BusinessException of(int code, String message, Throwable cause) {
        return new BusinessException(code, message, cause);
    }
    
    // 常用的业务异常
    
    /**
     * 参数错误
     */
    public static BusinessException paramError(String message) {
        return new BusinessException(400, message);
    }
    
    /**
     * 数据不存在
     */
    public static BusinessException dataNotFound(String message) {
        return new BusinessException(404, message);
    }
    
    /**
     * 数据已存在
     */
    public static BusinessException dataExists(String message) {
        return new BusinessException(409, message);
    }
    
    /**
     * 操作失败
     */
    public static BusinessException operationFailed(String message) {
        return new BusinessException(500, message);
    }
    
    /**
     * 权限不足
     */
    public static BusinessException permissionDenied(String message) {
        return new BusinessException(403, message);
    }
    
    /**
     * 未登录
     */
    public static BusinessException notLogin() {
        return new BusinessException(401, "用户未登录");
    }
    
    /**
     * 登录失败
     */
    public static BusinessException loginFailed(String message) {
        return new BusinessException(401, message);
    }
    
    /**
     * 验证码错误
     */
    public static BusinessException captchaError() {
        return new BusinessException(400, "验证码错误");
    }
    
    /**
     * 用户不存在
     */
    public static BusinessException userNotFound() {
        return new BusinessException(404, "用户不存在");
    }
    
    /**
     * 用户已存在
     */
    public static BusinessException userExists() {
        return new BusinessException(409, "用户已存在");
    }
    
    /**
     * 密码错误
     */
    public static BusinessException passwordError() {
        return new BusinessException(400, "密码错误");
    }
    
    /**
     * 账户被锁定
     */
    public static BusinessException accountLocked() {
        return new BusinessException(423, "账户被锁定");
    }
    
    /**
     * 账户被禁用
     */
    public static BusinessException accountDisabled() {
        return new BusinessException(423, "账户被禁用");
    }
    
    /**
     * 文件上传失败
     */
    public static BusinessException uploadFailed(String message) {
        return new BusinessException(500, "文件上传失败: " + message);
    }
    
    /**
     * 文件不存在
     */
    public static BusinessException fileNotFound() {
        return new BusinessException(404, "文件不存在");
    }
    
    /**
     * 文件格式不支持
     */
    public static BusinessException fileFormatNotSupported() {
        return new BusinessException(400, "文件格式不支持");
    }
    
    /**
     * 文件大小超限
     */
    public static BusinessException fileSizeExceeded() {
        return new BusinessException(400, "文件大小超过限制");
    }
}
