package cn.edu.sjtu.gateway.common.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * 分页结果类
 * 提供标准的分页响应格式
 * 
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PageResult<T> implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 数据列表
     */
    private List<T> records;
    
    /**
     * 总记录数
     */
    private long total;
    
    /**
     * 当前页码（从1开始）
     */
    private long current;
    
    /**
     * 每页大小
     */
    private long size;
    
    /**
     * 总页数
     */
    private long pages;
    
    /**
     * 是否有上一页
     */
    private boolean hasPrevious;
    
    /**
     * 是否有下一页
     */
    private boolean hasNext;
    
    /**
     * 是否是第一页
     */
    private boolean isFirst;
    
    /**
     * 是否是最后一页
     */
    private boolean isLast;
    
    /**
     * 私有构造函数
     */
    private PageResult() {
    }
    
    /**
     * 构造函数
     */
    public PageResult(List<T> records, long total, long current, long size) {
        this.records = records != null ? records : Collections.emptyList();
        this.total = Math.max(total, 0);
        this.current = Math.max(current, 1);
        this.size = Math.max(size, 1);
        
        // 计算总页数
        this.pages = (this.total + this.size - 1) / this.size;
        
        // 计算分页状态
        this.hasPrevious = this.current > 1;
        this.hasNext = this.current < this.pages;
        this.isFirst = this.current == 1;
        this.isLast = this.current == this.pages || this.pages == 0;
    }
    
    /**
     * 创建空的分页结果
     */
    public static <T> PageResult<T> empty() {
        return new PageResult<>(Collections.emptyList(), 0, 1, 10);
    }
    
    /**
     * 创建空的分页结果（指定页码和大小）
     */
    public static <T> PageResult<T> empty(long current, long size) {
        return new PageResult<>(Collections.emptyList(), 0, current, size);
    }
    
    /**
     * 创建分页结果
     */
    public static <T> PageResult<T> of(List<T> records, long total, long current, long size) {
        return new PageResult<>(records, total, current, size);
    }
    
    /**
     * 创建单页结果（不分页）
     */
    public static <T> PageResult<T> of(List<T> records) {
        int size = records != null ? records.size() : 0;
        return new PageResult<>(records, size, 1, size);
    }
    
    /**
     * 从Spring Data的Page对象创建
     */
    public static <T> PageResult<T> fromPage(org.springframework.data.domain.Page<T> page) {
        return new PageResult<>(
                page.getContent(),
                page.getTotalElements(),
                page.getNumber() + 1, // Spring Data的页码从0开始，我们从1开始
                page.getSize()
        );
    }
    
    /**
     * 转换数据类型
     */
    public <R> PageResult<R> map(List<R> newRecords) {
        return new PageResult<>(newRecords, this.total, this.current, this.size);
    }
    
    /**
     * 获取开始记录号（从1开始）
     */
    public long getStartRecord() {
        if (total == 0) {
            return 0;
        }
        return (current - 1) * size + 1;
    }
    
    /**
     * 获取结束记录号
     */
    public long getEndRecord() {
        if (total == 0) {
            return 0;
        }
        long end = current * size;
        return Math.min(end, total);
    }
    
    /**
     * 获取当前页记录数
     */
    public int getCurrentPageSize() {
        return records != null ? records.size() : 0;
    }
    
    /**
     * 判断是否为空
     */
    public boolean isEmpty() {
        return records == null || records.isEmpty();
    }
    
    /**
     * 判断是否不为空
     */
    public boolean isNotEmpty() {
        return !isEmpty();
    }
    
    /**
     * 获取上一页页码
     */
    public long getPreviousPage() {
        return hasPrevious ? current - 1 : current;
    }
    
    /**
     * 获取下一页页码
     */
    public long getNextPage() {
        return hasNext ? current + 1 : current;
    }
    
    /**
     * 获取分页信息摘要
     */
    public String getSummary() {
        if (total == 0) {
            return "暂无数据";
        }
        return String.format("第 %d-%d 条，共 %d 条记录，第 %d/%d 页",
                getStartRecord(), getEndRecord(), total, current, pages);
    }
    
    /**
     * 创建分页导航信息
     */
    public PageNavigation getNavigation() {
        return new PageNavigation(this);
    }
    
    /**
     * 分页导航信息
     */
    @Data
    public static class PageNavigation implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * 当前页
         */
        private long current;
        
        /**
         * 总页数
         */
        private long pages;
        
        /**
         * 首页
         */
        private long first = 1;
        
        /**
         * 末页
         */
        private long last;
        
        /**
         * 上一页
         */
        private Long previous;
        
        /**
         * 下一页
         */
        private Long next;
        
        /**
         * 页码列表（用于显示页码导航）
         */
        private List<Long> pageNumbers;
        
        public PageNavigation(PageResult<?> pageResult) {
            this.current = pageResult.getCurrent();
            this.pages = pageResult.getPages();
            this.last = this.pages;
            
            if (pageResult.isHasPrevious()) {
                this.previous = this.current - 1;
            }
            
            if (pageResult.isHasNext()) {
                this.next = this.current + 1;
            }
            
            // 生成页码列表（显示当前页前后各5页）
            this.pageNumbers = generatePageNumbers(this.current, this.pages, 5);
        }
        
        /**
         * 生成页码列表
         */
        private List<Long> generatePageNumbers(long current, long total, int range) {
            if (total <= 0) {
                return Collections.emptyList();
            }
            
            long start = Math.max(1, current - range);
            long end = Math.min(total, current + range);
            
            List<Long> pageNumbers = new java.util.ArrayList<>();
            for (long i = start; i <= end; i++) {
                pageNumbers.add(i);
            }
            
            return pageNumbers;
        }
    }
    
    @Override
    public String toString() {
        return "PageResult{" +
                "total=" + total +
                ", current=" + current +
                ", size=" + size +
                ", pages=" + pages +
                ", recordCount=" + getCurrentPageSize() +
                '}';
    }
}
