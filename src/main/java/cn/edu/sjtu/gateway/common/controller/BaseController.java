package cn.edu.sjtu.gateway.common.controller;

import cn.edu.sjtu.gateway.common.response.Result;
import cn.edu.sjtu.gateway.common.util.StringUtils;
import cn.edu.sjtu.gateway.common.entity.User;
// import cn.edu.sjtu.gateway.vm.util.SessionUtil; // 如果SessionUtil不存在，请注释掉
import lombok.extern.slf4j.Slf4j;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ModelAttribute;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

/**
 * 控制器基类
 * 提供通用的控制器功能和方法
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class BaseController {

    /**
     * 成功状态码
     */
    protected static final int SUCCESS = 1;

    /**
     * 失败状态码
     */
    protected static final int FAILURE = 0;

    /**
     * 默认页面大小
     */
    protected static final int DEFAULT_PAGE_SIZE = 20;

    /**
     * 最大页面大小
     */
    protected static final int MAX_PAGE_SIZE = 100;

    /**
     * 在每个请求处理方法执行前执行
     * 可以在子类中重写此方法来添加通用的预处理逻辑
     */
    @ModelAttribute
    public void preHandle(HttpServletRequest request, HttpServletResponse response, Model model) {
        // 设置通用的模型属性
        model.addAttribute("contextPath", request.getContextPath());
        model.addAttribute("requestURI", request.getRequestURI());

        // 设置当前用户信息
        User currentUser = getCurrentUser();
        if (currentUser != null) {
            model.addAttribute("currentUser", currentUser);
            model.addAttribute("currentUserId", currentUser.getId());
            model.addAttribute("currentUserName", currentUser.getUsername());
        }

        // 可以在子类中重写此方法添加更多逻辑
        doPreHandle(request, response, model);
    }

    /**
     * 子类可以重写此方法来添加自定义的预处理逻辑
     */
    protected void doPreHandle(HttpServletRequest request, HttpServletResponse response, Model model) {
        // 默认空实现，子类可以重写
    }

    /**
     * 全局异常处理
     */
    @ExceptionHandler(Exception.class)
    public String handleException(Exception e, Model model) {
        log.error("控制器异常", e);
        return error(model, "系统异常：" + e.getMessage());
    }

    /**
     * 获取当前登录用户
     */
    protected User getCurrentUser() {
        try {
            // TODO: 实现获取当前用户的逻辑
            // return SessionUtil.getUser();
            return null; // 临时返回null，避免编译错误
        } catch (Exception e) {
            log.debug("获取当前用户失败", e);
            return null;
        }
    }

    /**
     * 获取当前用户ID
     */
    protected Integer getCurrentUserId() {
        User user = getCurrentUser();
        return user != null ? user.getId() : null;
    }

    /**
     * 判断是否已登录
     */
    protected boolean isLoggedIn() {
        return getCurrentUser() != null;
    }

    /**
     * 获取请求参数（字符串）
     */
    protected String getParameter(HttpServletRequest request, String name) {
        return getParameter(request, name, null);
    }

    /**
     * 获取请求参数（字符串，带默认值）
     */
    protected String getParameter(HttpServletRequest request, String name, String defaultValue) {
        String value = request.getParameter(name);
        return StringUtils.isEmpty(value) ? defaultValue : value.trim();
    }

    /**
     * 获取请求参数（整数）
     */
    protected Integer getIntParameter(HttpServletRequest request, String name) {
        return getIntParameter(request, name, null);
    }

    /**
     * 获取请求参数（整数，带默认值）
     */
    protected Integer getIntParameter(HttpServletRequest request, String name, Integer defaultValue) {
        String value = getParameter(request, name);
        return StringUtils.toInt(value, defaultValue != null ? defaultValue : 0);
    }

    /**
     * 获取请求参数（长整数）
     */
    protected Long getLongParameter(HttpServletRequest request, String name) {
        return getLongParameter(request, name, null);
    }

    /**
     * 获取请求参数（长整数，带默认值）
     */
    protected Long getLongParameter(HttpServletRequest request, String name, Long defaultValue) {
        String value = getParameter(request, name);
        return StringUtils.toLong(value, defaultValue != null ? defaultValue : 0L);
    }

    /**
     * 获取请求参数（布尔值）
     */
    protected Boolean getBooleanParameter(HttpServletRequest request, String name) {
        return getBooleanParameter(request, name, false);
    }

    /**
     * 获取请求参数（布尔值，带默认值）
     */
    protected Boolean getBooleanParameter(HttpServletRequest request, String name, Boolean defaultValue) {
        String value = getParameter(request, name);
        return StringUtils.toBoolean(value, defaultValue != null ? defaultValue : false);
    }

    /**
     * 获取分页参数 - 页码
     */
    protected int getPageNumber(HttpServletRequest request) {
        Integer page = getIntParameter(request, "page", 1);
        return Math.max(page, 1);
    }

    /**
     * 获取分页参数 - 页面大小
     */
    protected int getPageSize(HttpServletRequest request) {
        Integer size = getIntParameter(request, "size", DEFAULT_PAGE_SIZE);
        return Math.min(Math.max(size, 1), MAX_PAGE_SIZE);
    }

    /**
     * 设置成功消息
     */
    protected void setSuccessMessage(Model model, String message) {
        model.addAttribute("successMessage", message);
        model.addAttribute("messageType", "success");
    }

    /**
     * 设置错误消息
     */
    protected void setErrorMessage(Model model, String message) {
        model.addAttribute("errorMessage", message);
        model.addAttribute("messageType", "error");
    }

    /**
     * 设置警告消息
     */
    protected void setWarningMessage(Model model, String message) {
        model.addAttribute("warningMessage", message);
        model.addAttribute("messageType", "warning");
    }

    /**
     * 设置信息消息
     */
    protected void setInfoMessage(Model model, String message) {
        model.addAttribute("infoMessage", message);
        model.addAttribute("messageType", "info");
    }

    /**
     * 返回成功结果
     */
    protected Result<Object> success() {
        return Result.success();
    }

    /**
     * 返回成功结果（带数据）
     */
    protected <T> Result<T> success(T data) {
        return Result.success(data);
    }

    /**
     * 返回成功结果（带消息）
     */
    protected Result<Object> success(String message) {
        return Result.success(message);
    }

    /**
     * 返回成功结果（带数据和消息）
     */
    protected <T> Result<T> success(T data, String message) {
        return Result.success(data, message);
    }

    /**
     * 返回失败结果
     */
    protected Result<Object> failure() {
        return Result.failure();
    }

    /**
     * 返回失败结果（带消息）
     */
    protected Result<Object> failure(String message) {
        return Result.failure(message);
    }

    /**
     * 返回失败结果（带错误码和消息）
     */
    protected Result<Object> failure(int code, String message) {
        return Result.failure(code, message);
    }

    /**
     * 跳转到成功页面
     */
    protected String success(Model model, String message, String redirectUrl) {
        model.addAttribute("state", SUCCESS);
        model.addAttribute("info", message);
        if (StringUtils.isNotEmpty(redirectUrl)) {
            model.addAttribute("redirectUrl", redirectUrl);
        }
        return "common/prompt";
    }

    /**
     * 跳转到错误页面
     */
    protected String error(Model model, String message) {
        return error(model, message, null);
    }

    /**
     * 跳转到错误页面（带返回URL）
     */
    protected String error(Model model, String message, String backUrl) {
        model.addAttribute("state", FAILURE);
        model.addAttribute("info", message);
        if (StringUtils.isNotEmpty(backUrl)) {
            model.addAttribute("backUrl", backUrl);
        }
        return "common/prompt";
    }

    /**
     * 重定向
     */
    protected String redirect(String url) {
        return "redirect:" + url;
    }

    /**
     * 转发
     */
    protected String forward(String url) {
        return "forward:" + url;
    }

    /**
     * 获取客户端IP地址
     */
    protected String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.isNotEmpty(xForwardedFor) && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            // 多次反向代理后会有多个IP值，第一个为真实IP
            int index = xForwardedFor.indexOf(',');
            if (index != -1) {
                return xForwardedFor.substring(0, index);
            } else {
                return xForwardedFor;
            }
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (StringUtils.isNotEmpty(xRealIp) && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        String proxyClientIp = request.getHeader("Proxy-Client-IP");
        if (StringUtils.isNotEmpty(proxyClientIp) && !"unknown".equalsIgnoreCase(proxyClientIp)) {
            return proxyClientIp;
        }

        String wlProxyClientIp = request.getHeader("WL-Proxy-Client-IP");
        if (StringUtils.isNotEmpty(wlProxyClientIp) && !"unknown".equalsIgnoreCase(wlProxyClientIp)) {
            return wlProxyClientIp;
        }

        String httpClientIp = request.getHeader("HTTP_CLIENT_IP");
        if (StringUtils.isNotEmpty(httpClientIp) && !"unknown".equalsIgnoreCase(httpClientIp)) {
            return httpClientIp;
        }

        String httpXForwardedFor = request.getHeader("HTTP_X_FORWARDED_FOR");
        if (StringUtils.isNotEmpty(httpXForwardedFor) && !"unknown".equalsIgnoreCase(httpXForwardedFor)) {
            return httpXForwardedFor;
        }

        return request.getRemoteAddr();
    }

    /**
     * 获取用户代理字符串
     */
    protected String getUserAgent(HttpServletRequest request) {
        return request.getHeader("User-Agent");
    }

    /**
     * 判断是否是Ajax请求
     */
    protected boolean isAjaxRequest(HttpServletRequest request) {
        String requestedWith = request.getHeader("X-Requested-With");
        return "XMLHttpRequest".equals(requestedWith);
    }

    /**
     * 判断是否是移动设备
     */
    protected boolean isMobileDevice(HttpServletRequest request) {
        String userAgent = getUserAgent(request);
        if (StringUtils.isEmpty(userAgent)) {
            return false;
        }

        userAgent = userAgent.toLowerCase();
        return userAgent.contains("mobile") ||
               userAgent.contains("android") ||
               userAgent.contains("iphone") ||
               userAgent.contains("ipad") ||
               userAgent.contains("windows phone");
    }

    /**
     * 设置会话属性
     */
    protected void setSessionAttribute(HttpServletRequest request, String name, Object value) {
        HttpSession session = request.getSession();
        session.setAttribute(name, value);
    }

    /**
     * 获取会话属性
     */
    protected Object getSessionAttribute(HttpServletRequest request, String name) {
        HttpSession session = request.getSession(false);
        return session != null ? session.getAttribute(name) : null;
    }

    /**
     * 移除会话属性
     */
    protected void removeSessionAttribute(HttpServletRequest request, String name) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            session.removeAttribute(name);
        }
    }

    /**
     * 清空会话
     */
    protected void clearSession(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            session.invalidate();
        }
    }
}
