package cn.edu.sjtu.gateway.common.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;

/**
 * 统一应用插件实体类
 * 整合原有的多个Application类，提供统一的应用插件管理
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "application")
public class Application extends BaseEntity {
    
    /**
     * 该插件的唯一标识。如自定义表单插件，唯一标识便是 formManage 。注意不能与其他插件重名
     */
    @Column(name = "app_id", length = 50, unique = true, nullable = false)
    private String appId;
    
    /**
     * 在网站管理后台中，功能插件下，显示的菜单项的标题文字，也就是插件的名字
     */
    @Column(name = "menu_title", length = 100)
    private String menuTitle;
    
    /**
     * 是否在CMS模式网站管理后台的功能插件中显示， 1：是, 0：不显示
     */
    @Column(name = "apply_to_cms")
    private Short applyToCMS = 0;
    
    /**
     * 是否在电脑(pc)模式网站管理后台的功能插件中显示， 1：是, 0：不显示
     */
    @Column(name = "apply_to_pc")
    private Short applyToPC = 0;
    
    /**
     * 是否在手机(wap)模式网站管理后台的功能插件中显示， 1：是, 0：不显示
     */
    @Column(name = "apply_to_wap")
    private Short applyToWAP = 0;
    
    /**
     * 是否在代理后台的功能插件中显示， 1：是, 0：不显示
     */
    @Column(name = "apply_to_agency")
    private Short applyToAgency = 0;
    
    /**
     * 是否在超级管理后台的功能插件中显示， 1：是, 0：不显示
     */
    @Column(name = "apply_to_super_manager")
    private Short applyToSuperManager = 0;
    
    /**
     * 是否在超级管理员后台的功能插件中显示， 1：是, 0：不显示
     */
    @Column(name = "apply_to_super_admin")
    private Short applyToSuperAdmin = 0;
    
    /**
     * 该插件的简介说明
     */
    @Column(name = "intro", length = 200)
    private String intro;
    
    /**
     * 当前插件的版本号 ， 如  1.0  则是 100000000; 1.2.1 则是 100200100; 2.13.3则是 200130300
     */
    @Column(name = "version")
    private Integer version;
    
    /**
     * 支持的网市场最低版本，规则也是同上，如4.7.1则是 400700100
     */
    @Column(name = "naii_version_min")
    private Integer naiiVersionMin;
    
    /**
     * 应用添加时间（Unix时间戳）
     */
    @Column(name = "addtime")
    private Integer addtime;
    
    /**
     * 应用最后改动时间（Unix时间戳）
     */
    @Column(name = "updatetime")
    private Integer updatetime;
    
    /**
     * 作者名字
     */
    @Column(name = "author_name", length = 50)
    private String authorName;
    
    /**
     * 若naii使用的OSS，是否支持该插件运行。 1支持，0或者其他是不支持
     */
    @Column(name = "support_oss_storage")
    private Short supportOssStorage = 0;
    
    /**
     * 若naii使用的服务器本身进行的文件存储，是否支持该插件运行。 1支持，0或者其他是不支持
     */
    @Column(name = "support_local_storage")
    private Short supportLocalStorage = 0;
    
    /**
     * 若naii使用的SLS，是否支持该插件运行。 1支持，0或者其他是不支持
     */
    @Column(name = "support_sls")
    private Short supportSls = 0;
    
    /**
     * 若naii使用的Mysql数据库，是否支持该插件运行。 1支持，0或者其他是不支持
     */
    @Column(name = "support_mysql")
    private Short supportMysql = 0;
    
    /**
     * 若naii使用的Sqlite数据库，是否支持该插件运行。 1支持，0或者其他是不支持
     */
    @Column(name = "support_sqlite")
    private Short supportSqlite = 0;
    
    /**
     * 若naii使用的是免费开源版本，是否支持该插件运行。 1支持，0或者其他是不支持
     */
    @Column(name = "support_free_version")
    private Short supportFreeVersion = 0;
    
    /**
     * 若naii使用的是授权版本，是否支持该插件运行。 1支持，0或者其他是不支持
     */
    @Column(name = "support_license_version")
    private Short supportLicenseVersion = 0;
    
    /**
     * 插件状态：0-禁用，1-启用
     */
    @Column(name = "status")
    private Short status = 1;
    
    /**
     * 插件类型：1-系统插件，2-用户插件
     */
    @Column(name = "type")
    private Short type = 2;
    
    /**
     * 插件配置信息（JSON格式）
     */
    @Column(name = "config", columnDefinition = "TEXT")
    private String config;
    
    /**
     * 插件图标URL
     */
    @Column(name = "icon_url", length = 200)
    private String iconUrl;
    
    /**
     * 插件主页URL
     */
    @Column(name = "home_url", length = 200)
    private String homeUrl;
    
    /**
     * 插件下载URL
     */
    @Column(name = "download_url", length = 200)
    private String downloadUrl;
    
    /**
     * 插件文档URL
     */
    @Column(name = "doc_url", length = 200)
    private String docUrl;
    
    /**
     * 排序权重，数字越小越靠前
     */
    @Column(name = "sort_order")
    private Integer sortOrder = 0;
    
    // 便利方法
    
    /**
     * 判断是否支持CMS模式
     */
    public boolean supportsCMS() {
        return applyToCMS != null && applyToCMS == 1;
    }
    
    /**
     * 判断是否支持PC模式
     */
    public boolean supportsPC() {
        return applyToPC != null && applyToPC == 1;
    }
    
    /**
     * 判断是否支持WAP模式
     */
    public boolean supportsWAP() {
        return applyToWAP != null && applyToWAP == 1;
    }
    
    /**
     * 判断是否支持代理后台
     */
    public boolean supportsAgency() {
        return applyToAgency != null && applyToAgency == 1;
    }
    
    /**
     * 判断是否支持超级管理后台
     */
    public boolean supportsSuperManager() {
        return applyToSuperManager != null && applyToSuperManager == 1;
    }
    
    /**
     * 判断是否支持超级管理员后台
     */
    public boolean supportsSuperAdmin() {
        return applyToSuperAdmin != null && applyToSuperAdmin == 1;
    }
    
    /**
     * 判断是否启用
     */
    public boolean isEnabled() {
        return status != null && status == 1;
    }
    
    /**
     * 判断是否是系统插件
     */
    public boolean isSystemPlugin() {
        return type != null && type == 1;
    }
    
    /**
     * 判断是否支持OSS存储
     */
    public boolean supportsOSS() {
        return supportOssStorage != null && supportOssStorage == 1;
    }
    
    /**
     * 判断是否支持本地存储
     */
    public boolean supportsLocalStorage() {
        return supportLocalStorage != null && supportLocalStorage == 1;
    }
    
    /**
     * 判断是否支持MySQL数据库
     */
    public boolean supportsMySQL() {
        return supportMysql != null && supportMysql == 1;
    }
    
    /**
     * 判断是否支持SQLite数据库
     */
    public boolean supportsSQLite() {
        return supportSqlite != null && supportSqlite == 1;
    }
    
    /**
     * 判断是否支持免费版本
     */
    public boolean supportsFreeVersion() {
        return supportFreeVersion != null && supportFreeVersion == 1;
    }
    
    /**
     * 判断是否支持授权版本
     */
    public boolean supportsLicenseVersion() {
        return supportLicenseVersion != null && supportLicenseVersion == 1;
    }
    
    /**
     * 获取版本号字符串
     * 将数字版本号转换为字符串格式，如 100200100 -> "1.2.1"
     */
    public String getVersionString() {
        if (version == null) {
            return "0.0.0";
        }
        
        int major = version / 100000000;
        int minor = (version % 100000000) / 1000000;
        int patch = (version % 1000000) / 10000;
        
        return String.format("%d.%d.%d", major, minor, patch);
    }
    
    /**
     * 设置版本号字符串
     * 将字符串版本号转换为数字格式，如 "1.2.1" -> 100200100
     */
    public void setVersionString(String versionStr) {
        if (versionStr == null || versionStr.trim().isEmpty()) {
            this.version = 0;
            return;
        }
        
        String[] parts = versionStr.split("\\.");
        int major = 0, minor = 0, patch = 0;
        
        try {
            if (parts.length > 0) major = Integer.parseInt(parts[0]);
            if (parts.length > 1) minor = Integer.parseInt(parts[1]);
            if (parts.length > 2) patch = Integer.parseInt(parts[2]);
            
            this.version = major * 100000000 + minor * 1000000 + patch * 10000;
        } catch (NumberFormatException e) {
            this.version = 0;
        }
    }
}
