package cn.edu.sjtu.gateway.common.config;

import cn.edu.sjtu.gateway.vm.Global;
import cn.edu.sjtu.gateway.vm.util.SystemUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.File;

/**
 * 静态资源配置检查器
 * 在应用启动时检查静态资源配置是否正确
 */
@Component
@Order(1) // 确保在其他组件之前执行
@Slf4j
public class StaticResourceChecker implements CommandLineRunner {

    @Override
    public void run(String... args) throws Exception {
        log.info("🔍 开始检查静态资源配置...");
        
        try {
            // 检查运行模式
            log.info("当前运行模式: {}", Global.isJarRun ? "JAR包模式" : "WAR包/开发模式");
            
            // 检查上传目录
            String basePath;
            if (Global.isJarRun) {
                basePath = SystemUtil.getJarDirPath();
                log.info("JAR包所在目录: {}", basePath);
            } else {
                basePath = SystemUtil.getProjectPath();
                log.info("项目路径: {}", basePath);
            }
            
            // 确保uploads目录存在
            String uploadsPath = basePath + "uploads/";
            File uploadsDir = new File(uploadsPath);
            if (!uploadsDir.exists()) {
                boolean created = uploadsDir.mkdirs();
                if (created) {
                    log.info("✅ 创建上传目录: {}", uploadsPath);
                } else {
                    log.warn("⚠️ 无法创建上传目录: {}", uploadsPath);
                }
            } else {
                log.info("✅ 上传目录已存在: {}", uploadsPath);
            }
            
            // 检查site子目录
            File siteDir = new File(uploadsDir, "site");
            if (!siteDir.exists()) {
                boolean created = siteDir.mkdirs();
                if (created) {
                    log.info("✅ 创建site目录: {}", siteDir.getAbsolutePath());
                } else {
                    log.warn("⚠️ 无法创建site目录: {}", siteDir.getAbsolutePath());
                }
            }
            
            // 输出映射信息
            log.info("📁 静态资源映射配置:");
            log.info("   /uploads/** -> file:{}", uploadsPath);
            log.info("   /site/** -> file:{}site/", uploadsPath);
            
            log.info("✅ 静态资源配置检查完成");
            
        } catch (Exception e) {
            log.error("❌ 静态资源配置检查失败", e);
        }
    }
}
