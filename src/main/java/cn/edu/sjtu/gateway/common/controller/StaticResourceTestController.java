package cn.edu.sjtu.gateway.common.controller;

import cn.edu.sjtu.gateway.vm.Global;
import cn.edu.sjtu.gateway.vm.util.SystemUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * 静态资源测试控制器
 * 用于测试和诊断静态资源映射配置
 */
@Controller
@RequestMapping("/test")
@Slf4j
public class StaticResourceTestController {

    /**
     * 测试静态资源配置信息
     */
    @RequestMapping("/static-config")
    @ResponseBody
    public Map<String, Object> testStaticConfig() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 运行模式信息
            result.put("isJarRun", Global.isJarRun);
            result.put("runMode", Global.isJarRun ? "JAR包模式" : "WAR包/开发模式");
            
            // 路径信息
            if (Global.isJarRun) {
                String jarDirPath = SystemUtil.getJarDirPath();
                result.put("jarDirPath", jarDirPath);
                result.put("uploadsPath", jarDirPath + "uploads/");
                
                // 检查目录是否存在
                File uploadsDir = new File(jarDirPath + "uploads/");
                result.put("uploadsDirectoryExists", uploadsDir.exists());
                if (!uploadsDir.exists()) {
                    result.put("uploadsDirectoryCreated", uploadsDir.mkdirs());
                }
            } else {
                String projectPath = SystemUtil.getProjectPath();
                result.put("projectPath", projectPath);
                result.put("uploadsPath", projectPath + "uploads/");
                
                // 检查目录是否存在
                File uploadsDir = new File(projectPath + "uploads/");
                result.put("uploadsDirectoryExists", uploadsDir.exists());
                if (!uploadsDir.exists()) {
                    result.put("uploadsDirectoryCreated", uploadsDir.mkdirs());
                }
            }
            
            // 系统属性
            result.put("userDir", System.getProperty("user.dir"));
            result.put("classPath", this.getClass().getProtectionDomain().getCodeSource().getLocation().toString());
            
            result.put("success", true);
            result.put("message", "静态资源配置检查完成");
            
        } catch (Exception e) {
            log.error("检查静态资源配置时发生错误", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 测试上传目录访问
     */
    @RequestMapping("/upload-access")
    @ResponseBody
    public Map<String, Object> testUploadAccess() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String basePath = Global.isJarRun ? SystemUtil.getJarDirPath() : SystemUtil.getProjectPath();
            String uploadsPath = basePath + "uploads/";
            
            File uploadsDir = new File(uploadsPath);
            if (!uploadsDir.exists()) {
                uploadsDir.mkdirs();
            }
            
            // 创建测试文件
            File testFile = new File(uploadsDir, "test.txt");
            if (!testFile.exists()) {
                testFile.createNewFile();
                java.nio.file.Files.write(testFile.toPath(), "This is a test file for upload access verification.".getBytes());
            }
            
            result.put("uploadsPath", uploadsPath);
            result.put("testFileExists", testFile.exists());
            result.put("testFileUrl", "/uploads/test.txt");
            result.put("message", "可以通过 /uploads/test.txt 访问测试文件");
            result.put("success", true);
            
        } catch (Exception e) {
            log.error("测试上传目录访问时发生错误", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
}
