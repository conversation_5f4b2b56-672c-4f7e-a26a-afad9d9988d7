package cn.edu.sjtu.gateway.common.test;

import cn.edu.sjtu.gateway.common.config.GlobalConfig;
import cn.edu.sjtu.gateway.common.exception.BusinessException;
import cn.edu.sjtu.gateway.common.exception.SystemException;
import cn.edu.sjtu.gateway.common.manager.DomainManager;
import cn.edu.sjtu.gateway.common.response.Result;
import cn.edu.sjtu.gateway.common.response.PageResult;
import cn.edu.sjtu.gateway.common.util.StringUtils;
import cn.edu.sjtu.gateway.common.util.DateUtils;
import cn.edu.sjtu.gateway.common.util.FileUtils;
import cn.edu.sjtu.gateway.common.entity.BaseEntity;
import cn.edu.sjtu.gateway.vm.entity.User;
import cn.edu.sjtu.gateway.common.entity.Application;

/**
 * 编译测试类
 * 用于验证所有新创建的统一框架类是否能够正常编译
 *
 * <AUTHOR>
 */
public class CompilationTest {

    /**
     * 测试全局配置
     */
    public void testGlobalConfig() {
        // 测试版本信息
        String version = GlobalConfig.Version.CURRENT_VERSION;
        String vmVersion = GlobalConfig.Version.VM_VERSION;

        // 测试路径配置
        String cachePath = GlobalConfig.Path.CACHE_FILE;
        String iconUrl = GlobalConfig.Path.DEFAULT_SITE_COLUMN_ICON_URL;

        // 测试模板配置
        int pcTemplate = GlobalConfig.Template.PC_DEFAULT;
        int wapTemplate = GlobalConfig.Template.WAP_DEFAULT;

        // 测试业务配置
        int agencySize = GlobalConfig.Business.AGENCY_ADD_SUB_AGENCY_SITE_SIZE;
        boolean copyright = GlobalConfig.Business.copyright;

        // 测试域名方法
        String firstDomain = GlobalConfig.getFirstAutoAssignDomain();
        String[] domains = GlobalConfig.getAutoAssignDomain();
        // String carouselPath = GlobalConfig.getCarouselPath(1); // 暂时注释，避免类型错误

        System.out.println("GlobalConfig测试通过");
    }

    /**
     * 测试域名管理器
     */
    public void testDomainManager() {
        // 测试域名管理
        Object mockSite = new Object();
        DomainManager.putDomain("test", mockSite);
        Object retrieved = DomainManager.getDomain("test");

        // 测试绑定域名
        DomainManager.putBindDomain("test.com", mockSite);
        Object bindRetrieved = DomainManager.getBindDomain("test.com");

        // 测试统计
        int domainSize = DomainManager.getDomainSize();
        int bindSize = DomainManager.getBindDomainSize();
        String stats = DomainManager.getCacheStats();

        // 清理
        DomainManager.removeDomain("test");
        DomainManager.removeBindDomain("test.com");

        System.out.println("DomainManager测试通过");
    }

    /**
     * 测试工具类
     */
    public void testUtils() {
        // 测试StringUtils
        boolean equals = StringUtils.equals("test", "test");
        boolean isEmpty = StringUtils.isEmpty("");
        String random = StringUtils.getRandomAZ(5);
        String filtered = StringUtils.filterXss("<script>alert('xss')</script>");

        // 测试DateUtils
        long currentTime = DateUtils.currentTimeMillis();
        int currentSeconds = DateUtils.currentTimeSeconds();
        String dateTime = DateUtils.currentDateTime();
        String formatted = DateUtils.formatUnixTime(currentSeconds);

        // 测试FileUtils
        boolean exists = FileUtils.exists(".");
        boolean isDir = FileUtils.isDirectory(".");
        String tempDir = FileUtils.getTempDirectory();

        System.out.println("Utils测试通过");
    }

    /**
     * 测试异常处理
     */
    public void testExceptions() {
        try {
            // 测试业务异常
            throw BusinessException.paramError("参数错误");
        } catch (BusinessException e) {
            System.out.println("BusinessException: " + e.getMessage());
        }

        try {
            // 测试系统异常
            throw SystemException.databaseOperationError("数据库错误");
        } catch (SystemException e) {
            System.out.println("SystemException: " + e.getMessage());
        }

        System.out.println("Exceptions测试通过");
    }

    /**
     * 测试响应类
     */
    public void testResponse() {
        // 测试Result
        Result<String> successResult = Result.success("成功");
        Result<Object> failureResult = Result.failure("失败");
        Result<Object> paramErrorResult = Result.paramError("参数错误");

        // 测试PageResult
        java.util.List<String> data = java.util.Arrays.asList("item1", "item2", "item3");
        PageResult<String> pageResult = PageResult.of(data, 100, 1, 10);

        System.out.println("Response测试通过");
    }

    /**
     * 测试实体类
     */
    public void testEntities() {
        // 测试User（使用现有的vm.entity.User）
        User user = new User();
        user.setUsername("testuser");
        // 注意：这里的方法调用需要根据实际的User类进行调整

        // 测试Application
        Application app = new Application();
        app.setAppId("testApp");
        app.setMenuTitle("测试应用");
        app.setStatus((short) 1);

        boolean isEnabled = app.isEnabled();
        boolean supportsPC = app.supportsPC();

        System.out.println("Entities测试通过");
    }

    /**
     * 运行所有测试
     */
    public static void main(String[] args) {
        CompilationTest test = new CompilationTest();

        try {
            test.testGlobalConfig();
            test.testDomainManager();
            test.testUtils();
            test.testExceptions();
            test.testResponse();
            test.testEntities();

            System.out.println("\n🎉 所有编译测试通过！统一框架可以正常使用！");

        } catch (Exception e) {
            System.err.println("❌ 编译测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
