package cn.edu.sjtu.gateway.common.manager;

import cn.edu.sjtu.gateway.domain.bean.SimpleSite;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * 域名管理器
 * 统一管理站点域名缓存
 *
 * <AUTHOR>
 */
@Slf4j
public class DomainManager {

    /**
     * 使用系统赠送的二级域名访问，只要是建立过的网站，都会加入此,持久缓存
     * key: domain 二级域名的名字，不含 .wang.market
     */
    private static final Map<String, SimpleSite> domainSiteMap = new ConcurrentHashMap<>();

    /**
     * 使用绑定后的域名访问，这里只有绑定域名后才会加入此处,持久缓存
     */
    private static final Map<String, SimpleSite> bindDomainSiteMap = new ConcurrentHashMap<>();

    /**
     * 更新站点的二级域名缓存，二级域名是系统自己分配的
     *
     * @param domain 二级域名的名字，不含 .wang.market
     * @param site   站点对象
     */
    public static void putDomain(String domain, SimpleSite site) {
        if (domain != null && site != null) {
            domainSiteMap.put(domain, site);
            log.debug("更新域名缓存: {} -> {}", domain, site.toString());
        }
    }

    /**
     * 更新站点自己绑定的域名缓存
     *
     * @param bindDomain 绑定的域名
     * @param site       站点对象
     */
    public static void putBindDomain(String bindDomain, SimpleSite site) {
        if (bindDomain != null && site != null) {
            bindDomainSiteMap.put(bindDomain, site);
            log.debug("更新绑定域名缓存: {} -> {}", bindDomain, site.toString());
        }
    }

    /**
     * 通过站点自动分配的二级域名（不包含.wang.market ,仅仅只是二级域名的名字）获取站点信息
     *
     * @param domain 二级域名（不包含.wang.market ,仅仅只是二级域名的名字）
     * @return 站点对象
     */
    public static SimpleSite getDomain(String domain) {
        return domain != null ? domainSiteMap.get(domain) : null;
    }

    /**
     * 通过站点的绑定的域名获取站点信息
     *
     * @param bindDomain 绑定的域名
     * @return 站点对象
     */
    public static SimpleSite getBindDomain(String bindDomain) {
        return bindDomain != null ? bindDomainSiteMap.get(bindDomain) : null;
    }

    /**
     * 获取自动分配的二级域名的个数
     *
     * @return 域名数量
     */
    public static int getDomainSize() {
        return domainSiteMap.size();
    }

    /**
     * 获取绑定的域名的个数
     *
     * @return 绑定域名数量
     */
    public static int getBindDomainSize() {
        return bindDomainSiteMap.size();
    }

    /**
     * 移除域名缓存
     *
     * @param domain 要移除的域名
     * @return 被移除的站点信息
     */
    public static Object removeDomain(String domain) {
        if (domain != null) {
            Object removed = domainSiteMap.remove(domain);
            if (removed != null) {
                log.debug("移除域名缓存: {}", domain);
            }
            return removed;
        }
        return null;
    }

    /**
     * 移除绑定域名缓存
     *
     * @param bindDomain 要移除的绑定域名
     * @return 被移除的站点信息
     */
    public static Object removeBindDomain(String bindDomain) {
        if (bindDomain != null) {
            Object removed = bindDomainSiteMap.remove(bindDomain);
            if (removed != null) {
                log.debug("移除绑定域名缓存: {}", bindDomain);
            }
            return removed;
        }
        return null;
    }

    /**
     * 清空所有域名缓存
     */
    public static void clearAll() {
        domainSiteMap.clear();
        bindDomainSiteMap.clear();
        log.info("清空所有域名缓存");
    }

    /**
     * 清空二级域名缓存
     */
    public static void clearDomains() {
        domainSiteMap.clear();
        log.info("清空二级域名缓存");
    }

    /**
     * 清空绑定域名缓存
     */
    public static void clearBindDomains() {
        bindDomainSiteMap.clear();
        log.info("清空绑定域名缓存");
    }

    /**
     * 检查域名是否存在
     *
     * @param domain 域名
     * @return true: 存在, false: 不存在
     */
    public static boolean containsDomain(String domain) {
        return domain != null && domainSiteMap.containsKey(domain);
    }

    /**
     * 检查绑定域名是否存在
     *
     * @param bindDomain 绑定域名
     * @return true: 存在, false: 不存在
     */
    public static boolean containsBindDomain(String bindDomain) {
        return bindDomain != null && bindDomainSiteMap.containsKey(bindDomain);
    }

    /**
     * 获取所有域名缓存的副本
     *
     * @return 域名缓存副本
     */
    public static Map<String, Object> getAllDomains() {
        return new ConcurrentHashMap<>(domainSiteMap);
    }

    /**
     * 获取所有绑定域名缓存的副本
     *
     * @return 绑定域名缓存副本
     */
    public static Map<String, Object> getAllBindDomains() {
        return new ConcurrentHashMap<>(bindDomainSiteMap);
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    public static String getCacheStats() {
        return String.format("域名缓存统计 - 二级域名: %d, 绑定域名: %d, 总计: %d",
                domainSiteMap.size(), bindDomainSiteMap.size(),
                domainSiteMap.size() + bindDomainSiteMap.size());
    }
}
