package cn.edu.sjtu.gateway.common.util;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

/**
 * 统一文件工具类
 * 整合原有的FileUtil类，提供统一的文件处理功能
 * 
 * <AUTHOR>
 */
@Slf4j
public class FileUtils {
    
    /**
     * 默认编码UTF-8
     */
    public static final String DEFAULT_ENCODING = StandardCharsets.UTF_8.name();
    
    /**
     * GBK编码
     */
    public static final String GBK_ENCODING = "GBK";
    
    /**
     * 读取文件内容，使用UTF-8编码
     * 
     * @param filePath 文件路径
     * @return 文件内容
     */
    public static String readFile(String filePath) {
        return readFile(filePath, DEFAULT_ENCODING);
    }
    
    /**
     * 读取文件内容
     * 
     * @param filePath 文件路径
     * @param encoding 编码格式
     * @return 文件内容
     */
    public static String readFile(String filePath, String encoding) {
        if (StringUtils.isEmpty(filePath)) {
            return null;
        }
        
        try {
            return readFile(new File(filePath), encoding);
        } catch (Exception e) {
            log.error("读取文件失败: {}", filePath, e);
            return null;
        }
    }
    
    /**
     * 读取文件内容
     * 
     * @param file     文件对象
     * @param encoding 编码格式
     * @return 文件内容
     */
    public static String readFile(File file, String encoding) {
        if (file == null || !file.exists() || !file.isFile()) {
            return null;
        }
        
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(file), encoding))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        } catch (Exception e) {
            log.error("读取文件失败: {}", file.getAbsolutePath(), e);
            return null;
        }
        
        return content.toString();
    }
    
    /**
     * 写入文件内容，使用UTF-8编码
     * 
     * @param filePath 文件路径
     * @param content  文件内容
     * @return 是否成功
     */
    public static boolean writeFile(String filePath, String content) {
        return writeFile(filePath, content, DEFAULT_ENCODING);
    }
    
    /**
     * 写入文件内容
     * 
     * @param filePath 文件路径
     * @param content  文件内容
     * @param encoding 编码格式
     * @return 是否成功
     */
    public static boolean writeFile(String filePath, String content, String encoding) {
        if (StringUtils.isEmpty(filePath) || content == null) {
            return false;
        }
        
        try {
            return writeFile(new File(filePath), content, encoding);
        } catch (Exception e) {
            log.error("写入文件失败: {}", filePath, e);
            return false;
        }
    }
    
    /**
     * 写入文件内容
     * 
     * @param file     文件对象
     * @param content  文件内容
     * @param encoding 编码格式
     * @return 是否成功
     */
    public static boolean writeFile(File file, String content, String encoding) {
        if (file == null || content == null) {
            return false;
        }
        
        // 确保父目录存在
        File parentDir = file.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            if (!parentDir.mkdirs()) {
                log.error("创建目录失败: {}", parentDir.getAbsolutePath());
                return false;
            }
        }
        
        try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(new FileOutputStream(file), encoding))) {
            writer.write(content);
            return true;
        } catch (Exception e) {
            log.error("写入文件失败: {}", file.getAbsolutePath(), e);
            return false;
        }
    }
    
    /**
     * 追加内容到文件
     * 
     * @param filePath 文件路径
     * @param content  要追加的内容
     * @return 是否成功
     */
    public static boolean appendToFile(String filePath, String content) {
        return appendToFile(filePath, content, DEFAULT_ENCODING);
    }
    
    /**
     * 追加内容到文件
     * 
     * @param filePath 文件路径
     * @param content  要追加的内容
     * @param encoding 编码格式
     * @return 是否成功
     */
    public static boolean appendToFile(String filePath, String content, String encoding) {
        if (StringUtils.isEmpty(filePath) || content == null) {
            return false;
        }
        
        try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(new FileOutputStream(filePath, true), encoding))) {
            writer.write(content);
            return true;
        } catch (Exception e) {
            log.error("追加文件失败: {}", filePath, e);
            return false;
        }
    }
    
    /**
     * 判断文件是否存在
     * 
     * @param filePath 文件路径
     * @return 是否存在
     */
    public static boolean exists(String filePath) {
        if (StringUtils.isEmpty(filePath)) {
            return false;
        }
        return new File(filePath).exists();
    }
    
    /**
     * 判断是否是文件
     * 
     * @param filePath 文件路径
     * @return 是否是文件
     */
    public static boolean isFile(String filePath) {
        if (StringUtils.isEmpty(filePath)) {
            return false;
        }
        File file = new File(filePath);
        return file.exists() && file.isFile();
    }
    
    /**
     * 判断是否是目录
     * 
     * @param dirPath 目录路径
     * @return 是否是目录
     */
    public static boolean isDirectory(String dirPath) {
        if (StringUtils.isEmpty(dirPath)) {
            return false;
        }
        File dir = new File(dirPath);
        return dir.exists() && dir.isDirectory();
    }
    
    /**
     * 创建目录
     * 
     * @param dirPath 目录路径
     * @return 是否成功
     */
    public static boolean createDirectory(String dirPath) {
        if (StringUtils.isEmpty(dirPath)) {
            return false;
        }
        
        File dir = new File(dirPath);
        if (dir.exists()) {
            return dir.isDirectory();
        }
        
        return dir.mkdirs();
    }
    
    /**
     * 删除文件或目录
     * 
     * @param path 文件或目录路径
     * @return 是否成功
     */
    public static boolean delete(String path) {
        if (StringUtils.isEmpty(path)) {
            return false;
        }
        
        try {
            Path filePath = Paths.get(path);
            if (Files.exists(filePath)) {
                if (Files.isDirectory(filePath)) {
                    // 递归删除目录
                    Files.walkFileTree(filePath, new SimpleFileVisitor<Path>() {
                        @Override
                        public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                            Files.delete(file);
                            return FileVisitResult.CONTINUE;
                        }
                        
                        @Override
                        public FileVisitResult postVisitDirectory(Path dir, IOException exc) throws IOException {
                            Files.delete(dir);
                            return FileVisitResult.CONTINUE;
                        }
                    });
                } else {
                    Files.delete(filePath);
                }
                return true;
            }
        } catch (Exception e) {
            log.error("删除文件失败: {}", path, e);
        }
        
        return false;
    }
    
    /**
     * 复制文件
     * 
     * @param sourcePath 源文件路径
     * @param targetPath 目标文件路径
     * @return 是否成功
     */
    public static boolean copyFile(String sourcePath, String targetPath) {
        if (StringUtils.isEmpty(sourcePath) || StringUtils.isEmpty(targetPath)) {
            return false;
        }
        
        try {
            Path source = Paths.get(sourcePath);
            Path target = Paths.get(targetPath);
            
            // 确保目标目录存在
            Files.createDirectories(target.getParent());
            
            Files.copy(source, target, StandardCopyOption.REPLACE_EXISTING);
            return true;
        } catch (Exception e) {
            log.error("复制文件失败: {} -> {}", sourcePath, targetPath, e);
            return false;
        }
    }
    
    /**
     * 移动文件
     * 
     * @param sourcePath 源文件路径
     * @param targetPath 目标文件路径
     * @return 是否成功
     */
    public static boolean moveFile(String sourcePath, String targetPath) {
        if (StringUtils.isEmpty(sourcePath) || StringUtils.isEmpty(targetPath)) {
            return false;
        }
        
        try {
            Path source = Paths.get(sourcePath);
            Path target = Paths.get(targetPath);
            
            // 确保目标目录存在
            Files.createDirectories(target.getParent());
            
            Files.move(source, target, StandardCopyOption.REPLACE_EXISTING);
            return true;
        } catch (Exception e) {
            log.error("移动文件失败: {} -> {}", sourcePath, targetPath, e);
            return false;
        }
    }
    
    /**
     * 获取文件大小
     * 
     * @param filePath 文件路径
     * @return 文件大小（字节），文件不存在返回-1
     */
    public static long getFileSize(String filePath) {
        if (StringUtils.isEmpty(filePath)) {
            return -1;
        }
        
        try {
            return Files.size(Paths.get(filePath));
        } catch (Exception e) {
            log.error("获取文件大小失败: {}", filePath, e);
            return -1;
        }
    }
    
    /**
     * 获取文件扩展名
     * 
     * @param fileName 文件名
     * @return 扩展名（不包含点号）
     */
    public static String getFileExtension(String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        
        return "";
    }
    
    /**
     * 获取不带扩展名的文件名
     * 
     * @param fileName 文件名
     * @return 不带扩展名的文件名
     */
    public static String getFileNameWithoutExtension(String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return fileName.substring(0, lastDotIndex);
        }
        
        return fileName;
    }
    
    /**
     * 列出目录下的所有文件
     * 
     * @param dirPath 目录路径
     * @return 文件列表
     */
    public static List<File> listFiles(String dirPath) {
        List<File> fileList = new ArrayList<>();
        
        if (StringUtils.isEmpty(dirPath)) {
            return fileList;
        }
        
        File dir = new File(dirPath);
        if (!dir.exists() || !dir.isDirectory()) {
            return fileList;
        }
        
        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isFile()) {
                    fileList.add(file);
                }
            }
        }
        
        return fileList;
    }
    
    /**
     * 递归列出目录下的所有文件
     * 
     * @param dirPath 目录路径
     * @return 文件列表
     */
    public static List<File> listFilesRecursively(String dirPath) {
        List<File> fileList = new ArrayList<>();
        
        if (StringUtils.isEmpty(dirPath)) {
            return fileList;
        }
        
        try {
            Files.walkFileTree(Paths.get(dirPath), new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) {
                    fileList.add(file.toFile());
                    return FileVisitResult.CONTINUE;
                }
            });
        } catch (Exception e) {
            log.error("递归列出文件失败: {}", dirPath, e);
        }
        
        return fileList;
    }
    
    /**
     * 从URL下载文件
     * 
     * @param url      下载URL
     * @param savePath 保存路径
     * @return 是否成功
     */
    public static boolean downloadFile(String url, String savePath) {
        if (StringUtils.isEmpty(url) || StringUtils.isEmpty(savePath)) {
            return false;
        }
        
        try {
            URL downloadUrl = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) downloadUrl.openConnection();
            connection.setRequestProperty("User-Agent", "Mozilla/5.0");
            
            // 确保目标目录存在
            File saveFile = new File(savePath);
            File parentDir = saveFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                if (!parentDir.mkdirs()) {
                    log.error("创建目录失败: {}", parentDir.getAbsolutePath());
                    return false;
                }
            }
            
            try (InputStream inputStream = connection.getInputStream();
                 FileOutputStream outputStream = new FileOutputStream(savePath)) {
                
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                
                return true;
            }
        } catch (Exception e) {
            log.error("下载文件失败: {} -> {}", url, savePath, e);
            return false;
        }
    }
    
    /**
     * 压缩文件或目录为ZIP
     * 
     * @param sourcePath 源路径
     * @param zipPath    ZIP文件路径
     * @return 是否成功
     */
    public static boolean zipFile(String sourcePath, String zipPath) {
        if (StringUtils.isEmpty(sourcePath) || StringUtils.isEmpty(zipPath)) {
            return false;
        }
        
        try (ZipOutputStream zipOut = new ZipOutputStream(new FileOutputStream(zipPath))) {
            File sourceFile = new File(sourcePath);
            
            if (sourceFile.isFile()) {
                zipSingleFile(sourceFile, sourceFile.getName(), zipOut);
            } else if (sourceFile.isDirectory()) {
                zipDirectory(sourceFile, sourceFile.getName(), zipOut);
            }
            
            return true;
        } catch (Exception e) {
            log.error("压缩文件失败: {} -> {}", sourcePath, zipPath, e);
            return false;
        }
    }
    
    /**
     * 压缩单个文件
     */
    private static void zipSingleFile(File file, String fileName, ZipOutputStream zipOut) throws IOException {
        try (FileInputStream fis = new FileInputStream(file)) {
            ZipEntry zipEntry = new ZipEntry(fileName);
            zipOut.putNextEntry(zipEntry);
            
            byte[] buffer = new byte[4096];
            int length;
            while ((length = fis.read(buffer)) >= 0) {
                zipOut.write(buffer, 0, length);
            }
        }
    }
    
    /**
     * 压缩目录
     */
    private static void zipDirectory(File folder, String parentFolder, ZipOutputStream zipOut) throws IOException {
        File[] files = folder.listFiles();
        if (files == null) {
            return;
        }
        
        for (File file : files) {
            if (file.isDirectory()) {
                zipDirectory(file, parentFolder + "/" + file.getName(), zipOut);
            } else {
                zipSingleFile(file, parentFolder + "/" + file.getName(), zipOut);
            }
        }
    }
    
    /**
     * 解压ZIP文件
     * 
     * @param zipPath   ZIP文件路径
     * @param targetDir 解压目标目录
     * @return 是否成功
     */
    public static boolean unzipFile(String zipPath, String targetDir) {
        if (StringUtils.isEmpty(zipPath) || StringUtils.isEmpty(targetDir)) {
            return false;
        }
        
        try (ZipInputStream zipIn = new ZipInputStream(new FileInputStream(zipPath))) {
            ZipEntry entry = zipIn.getNextEntry();
            
            while (entry != null) {
                String filePath = targetDir + File.separator + entry.getName();
                
                if (!entry.isDirectory()) {
                    // 确保父目录存在
                    File parentDir = new File(filePath).getParentFile();
                    if (parentDir != null && !parentDir.exists()) {
                        parentDir.mkdirs();
                    }
                    
                    // 解压文件
                    try (BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(filePath))) {
                        byte[] buffer = new byte[4096];
                        int read;
                        while ((read = zipIn.read(buffer)) != -1) {
                            bos.write(buffer, 0, read);
                        }
                    }
                } else {
                    // 创建目录
                    File dir = new File(filePath);
                    dir.mkdirs();
                }
                
                zipIn.closeEntry();
                entry = zipIn.getNextEntry();
            }
            
            return true;
        } catch (Exception e) {
            log.error("解压文件失败: {} -> {}", zipPath, targetDir, e);
            return false;
        }
    }
    
    /**
     * 格式化文件大小
     * 
     * @param size 文件大小（字节）
     * @return 格式化后的大小字符串
     */
    public static String formatFileSize(long size) {
        if (size < 0) {
            return "0 B";
        }
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double fileSize = size;
        
        while (fileSize >= 1024 && unitIndex < units.length - 1) {
            fileSize /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", fileSize, units[unitIndex]);
    }
    
    /**
     * 获取临时目录路径
     * 
     * @return 临时目录路径
     */
    public static String getTempDirectory() {
        return System.getProperty("java.io.tmpdir");
    }
    
    /**
     * 创建临时文件
     * 
     * @param prefix 文件名前缀
     * @param suffix 文件名后缀
     * @return 临时文件
     */
    public static File createTempFile(String prefix, String suffix) {
        try {
            return File.createTempFile(prefix, suffix);
        } catch (IOException e) {
            log.error("创建临时文件失败", e);
            return null;
        }
    }
}
