package cn.edu.sjtu.gateway.supermanager.controller;

import cn.edu.sjtu.gateway.manager.G;
import cn.edu.sjtu.gateway.manager.entity.Site;
import cn.edu.sjtu.gateway.vm.controller.BaseController;
import cn.edu.sjtu.gateway.vm.entity.User;
import cn.edu.sjtu.gateway.vm.service.SqlService;
import cn.edu.sjtu.gateway.vm.service.UserService;
import cn.edu.sjtu.gateway.vm.util.ActionLogUtil;

import cn.edu.sjtu.gateway.vm.util.Page;
import cn.edu.sjtu.gateway.vm.util.SafetyUtil;
import cn.edu.sjtu.gateway.vm.util.Sql;
import cn.edu.sjtu.gateway.vm.vo.BaseVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 网站管理
 * <AUTHOR>
 */
@Controller
@RequestMapping("/manager/site")
@Slf4j
public class ManagerSiteController extends BaseController {
	private final SqlService sqlService;
	private final UserService userService;

	public ManagerSiteController(SqlService sqlService, UserService userService
			) {
		this.sqlService = sqlService;
		this.userService = userService;
	}


	/**
	 * 网站列表
	 */
	@RequiresPermissions("managerSiteList")
	@RequestMapping("list${url.suffix}")
	public String list(HttpServletRequest request, Model model){
		Sql sql = new Sql(request);
		sql.setSearchTable("site");
		sql.setSearchColumn(new String[]{"userid=","state=","name","phone","client=","bind_domain","domain"});
		int count = sqlService.count("site", sql.getWhere());
		Page page = new Page(count, G.PAGE_WAP_NUM, request);
		sql.setSelectFromAndPage("SELECT * FROM site", page);
		sql.setOrderByField(new String[]{"id","expiretime","addtime"});
		sql.setOrderBy("site.id DESC");
		List<Site> list = sqlService.findBySql(sql, Site.class);
		
		log.info("总管理后台，网站管理,网站列表");
		model.addAttribute("list", list);
		model.addAttribute("page", page);
		return "/supermanager/site/list";
	}
	

	/**
	 * 网站详情
	 * @param id News.id
	 */
	@RequiresPermissions("managerSiteView")
	@RequestMapping("view${url.suffix}")
	public String view(HttpServletRequest request, Model model,
			@RequestParam(value = "id", required = true , defaultValue="") int id){
		Site site = sqlService.findById(Site.class, id);
		log.info("总管理后台，{}网站管理,网站详情", site.getName());
		model.addAttribute("site", site);
		return "/supermanager/site/view";
	}
	

	/**
	 * 暂停网站，冻结网站。冻结后，site、user数据表都会记录
	 * 暂停后，网站依旧正常计费！
	 * @param siteid 要暂停的网站的site.id
	 */
	@RequiresPermissions("managerSiteList")
	@RequestMapping(value="siteFreeze${api.suffix}", method= {RequestMethod.POST})
	@ResponseBody
	public BaseVO sitePause(HttpServletRequest request,
			@RequestParam(value = "siteid", required = true) int siteid){
		Site site = sqlService.findById(Site.class, siteid);
		User user = sqlService.findById(User.class, site.getUserid());
		if(user == null){
			return error("用户不存在！");
		}
		
		//判断网站状态是否符合，只有当网站状态为正常时，才可以对网站进行暂停冻结操作
		if(site.getState() - Site.STATE_NORMAL != 0){
			return error("当前网站的状态不符，暂停失败");
		}
		
		site.setState(Site.STATE_FREEZE);
		sqlService.save(site);
		userService.freezeUser(site.getUserid());
		
		//记录操作日志
		log.info("userid:"+getUser().getId()+",将网站"+site.getName()+"暂停");
		
		return success();
	}
	

	/**
	 * 解除暂停网站，将暂停的网站恢复正常
	 * @param siteid 要暂停的网站的site.id
	 */
	@RequiresPermissions("managerSiteList")
	@RequestMapping(value="siteUnFreeze${api.suffix}", method= {RequestMethod.POST})
	@ResponseBody
	public BaseVO siteRemovePause(HttpServletRequest request,
			@RequestParam(value = "siteid", required = true) int siteid){
		Site site = sqlService.findById(Site.class, siteid);
		User user = sqlService.findById(User.class, site.getUserid());
		if(user == null){
			return error("用户不存在！");
		}
		
		//判断网站状态是否符合，只有当网站状态为正常时，才可以对网站进行暂停冻结操作
		if(site.getState() - Site.STATE_FREEZE != 0){
			return error("当前网站的状态不符，暂停失败");
		}
		
		site.setState(Site.STATE_NORMAL);
		sqlService.save(site);
		userService.unfreezeUser(site.getUserid());
		
		//记录操作日志
		log.info("userid:"+getUser().getId()+",将暂停的网站"+site.getName()+"恢复正常");

		return success();
	}
	
	/**
	 * 总后台给某个站点更改备注
	 * @param siteid 要更改备注的site.id
	 * @param remark 要更改的备注
	 * <AUTHOR>
	 */
	@RequiresPermissions("managerSiteList")
	@RequestMapping("siteUpdateRemark${api.suffix}")
	@ResponseBody
	public BaseVO siteUpdateRemark(HttpServletRequest request,
			@RequestParam(value = "siteid", required = true) int siteid,
			@RequestParam(value = "remark", required = true) String remark){
		//通过siteid查询网站的信息
		Site site = sqlService.findById(Site.class, siteid);
		if(site == null){
			return error("网站不存在");
		}
		site.setRemark(SafetyUtil.xssFilter(remark));
		sqlService.save(site);
		ActionLogUtil.insertUpdateDatabase(request, siteid, "总后台给某个站点更改备注", remark);
		return success();
	}
}
