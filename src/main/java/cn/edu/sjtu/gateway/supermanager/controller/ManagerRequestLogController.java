package cn.edu.sjtu.gateway.supermanager.controller;

import cn.edu.sjtu.gateway.manager.controller.BaseController;
import cn.edu.sjtu.gateway.manager.vo.RequestLogDayLineVO;
import cn.edu.sjtu.gateway.tools.BaseVO;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

/**
 * 访问统计
 * <AUTHOR>
 */
@Controller
@RequestMapping("/manager/requestLog/")
public class ManagerRequestLogController extends BaseController {
	
	//爬虫的useragent
	public static String[] spiderNameArray = {		"Baiduspider",	"Sogou web spider",	"Googlebot",			"YisouSpider"	,	"bingbot",		"spiderman",			"AhrefsBot"}; 
	//爬虫的描述说明
	public static String[] spiderExplainArray = {	"百度搜索爬虫",	"搜狗搜索爬虫",		"Google搜索爬虫",		"神马搜索爬虫",	"必应搜索爬虫",	"Java爬虫 Spiderman","AhrefsBot爬虫"}; 

	/**
	 * 访问统计，折线图
	 */
	@RequiresPermissions("managerRequestLogFangWen")
	@RequestMapping("fangwentongji${url.suffix}")
	public String fangwentongji(HttpServletRequest request, Model model){
		return error(model, "功能过时已废弃");
	}
	
	/**
	 * 折线图，当天、昨天，24小时，每小时的访问情况
	 */
	@RequiresPermissions("managerRequestLogFangWen")
	@RequestMapping("dayLineForCurrentDay${url.suffix}")
	@ResponseBody
	public RequestLogDayLineVO dayLineForCurrentDay(HttpServletRequest request){
		RequestLogDayLineVO vo = new RequestLogDayLineVO();
		vo.setBaseVO(BaseVO.FAILURE, "功能过时已废弃");
		return vo;
	}
	
	/**
	 * 折线图，当月(最近30天)，每天的访问情况
	 */
	@RequiresPermissions("managerRequestLogFangWen")
	@RequestMapping("dayLineForCurrentMonth${url.suffix}")
	@ResponseBody
	public RequestLogDayLineVO dayLineForCurrentMonth(HttpServletRequest request){
		RequestLogDayLineVO vo = new RequestLogDayLineVO();
		vo.setBaseVO(BaseVO.FAILURE, "功能过时已废弃");
		return vo;
	}
	

	/**
	 * 网站访问记录日志列表
	 */
	@RequiresPermissions("managerLogList")
	@RequestMapping("fangwenList${url.suffix}")
	public String fangwenList(HttpServletRequest request,Model model){
		return error(model, "功能过时已废弃");
	}
	
}