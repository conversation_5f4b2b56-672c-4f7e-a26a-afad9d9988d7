package cn.edu.sjtu.gateway.supermanager.bean;


import lombok.Getter;
import lombok.Setter;

/**
 * 应用插件
 * <AUTHOR>
 *
 */
@Setter
@Getter
public class Application {
	/**
	 * 该插件的唯一标识。如自定义表单插件，唯一标识便是 formManage 。注意不能与其他插件重名
	 */
	private String id;
	/**
	 * 在网站管理后台中，功能插件下，显示的菜单项的标题文字，也就是插件的名字
	 */
	private String menuTitle;
	/**
	 * 是否在CMS模式网站管理后台的功能插件中显示， 1：是, 不填、0则是不显示
	 */
	private Short applyToCMS;
	/**
	 * 是否在电脑(pc)模式网站管理后台的功能插件中显示， 1：是, 不填、0则是不显示
	 */
	private Short applyToPC;
	/**
	 * 是否在手机(wap)模式网站管理后台的功能插件中显示， 1：是, 不填、0则是不显示
	 */
	private Short applyToWAP;
	/**
	 * 是否在代理后台的功能插件中显示， 1：是, 不填、0则是不显示
	 */
	private Short applyToAgency;
	/**
	 * 是否在总管理后台的功能插件中显示， 1：是, 不填、0则是不显示
	 */
	private Short applyToSuperManager;
	/**
	 * 该插件的简介说明,char(200)
	 */
	private String intro;
	/**
	 * 当前插件的版本号 ， 如  1.0  则是 100000000; 1.2.1 则是 100200100; 2.13.3则是 200130300
	 */
	private Integer version;
	/**
	 * 支持的网市场最低版本，规则也是同上，如4.7.1则是 400700100
	 */
	private Integer naiiVersionMin;
	/**
	 * 应用添加时间
	 */
	private Integer addtime;
	/**
	 * 应用最后改动时间
	 */
	private Integer updatetime;
	/**
	 * 作者名字
	 */
	private String authorName;
	/**
	 * 若naii使用的OSS，是否支持该插件运行。 1支持，0或者其他是不支持
	 */
	private Short supportOssStorage;
	/**
	 * 若naii使用的服务器本身进行的文件存储，是否支持该插件运行。 1支持，0或者其他是不支持
	 */
	private Short supportLocalStorage;
	/**
	 * 若naii使用的SLS，是否支持该插件运行。 1支持，0或者其他是不支持
	 */
	private Short supportSls;
	/**
	 * 若naii使用的Mysql数据库，是否支持该插件运行。 1支持，0或者其他是不支持
	 */
	private Short supportMysql;
	/**
	 * 若naii使用的Sqlite数据库，是否支持该插件运行。 1支持，0或者其他是不支持
	 */
	private Short supportSqlite;
	/**
	 * 若naii使用的是免费开源版本，是否支持该插件运行。 1支持，0或者其他是不支持
	 */
	private Short supportFreeVersion;
	/**
	 * 若naii使用的是授权版本，是否支持该插件运行。 1支持，0或者其他是不支持
	 */
	private Short supportAuthorizeVersion;
	/**
	 * 插件下载的url
	 */
	private String downUrl;
	/**
	 * 插件是否被安装 0：未安装  1：已安装
	 */
	private Short installState;


}
