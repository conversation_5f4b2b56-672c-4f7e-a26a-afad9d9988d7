package cn.edu.sjtu.gateway.supermanager.controller;

import cn.edu.sjtu.gateway.manager.G;
import cn.edu.sjtu.gateway.vm.controller.BaseController;
import cn.edu.sjtu.gateway.vm.entity.User;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;

/**
 * 管理后台首页
 * <AUTHOR>
 */
@Controller(value = "SupermanagerIndexController")
@RequestMapping("/supermanager/index")
@Slf4j
public class ManagerIndexController extends BaseController{
	
	/**
	 * 总管理后台，登录成功后的欢迎页面
	 */
	@RequestMapping("welcome${url.suffix}")
	public String welcome(HttpServletRequest request, Model model){
		User user = getUser();
		log.info("总管理后台，登录成功后的欢迎页面");
		
		model.addAttribute("user", user);
		//版本号
		model.addAttribute("version", G.VERSION);
		return "/supermanager/index/welcome";
	}
	
}
