/* 科技风格主题样式 - 适用于人工智能研究院管理系统 */
:root {
  /* 主要颜色变量 */
  --tech-primary: #0066CC; /* 科技蓝 */
  --tech-primary-light: #3399FF;
  --tech-primary-dark: #003366;
  --tech-secondary: #00CC99; /* 科技绿 */
  --tech-accent: #FF6600; /* 橙色强调色 */
  --tech-background: #F5F7FA;
  --tech-surface: #FFFFFF;
  --tech-text-primary: #212121;
  --tech-text-secondary: #757575;
  --tech-border: #E0E0E0;
  --tech-success: #00CC99;
  --tech-warning: #FFCC00;
  --tech-error: #FF3333;
  
  /* 阴影 */
  --tech-shadow-1: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  --tech-shadow-2: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
  --tech-shadow-3: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
  
  /* 圆角 */
  --tech-radius: 4px;
  --tech-radius-lg: 8px;
}

/* 重写layui按钮样式 */
.layui-btn {
  background: linear-gradient(135deg, var(--tech-primary), var(--tech-primary-light));
  border: none;
  border-radius: var(--tech-radius);
  color: white;
  font-weight: 500;
  box-shadow: var(--tech-shadow-1);
  transition: all 0.3s ease;
}

.layui-btn:hover {
  background: linear-gradient(135deg, var(--tech-primary-light), var(--tech-primary));
  box-shadow: var(--tech-shadow-2);
  opacity: 1;
}

.layui-btn:active {
  box-shadow: var(--tech-shadow-1);
  transform: translateY(1px);
}

/* 主要按钮 */
.layui-btn-normal {
  background: linear-gradient(135deg, var(--tech-secondary), #00B88D);
}

/* 警告按钮 */
.layui-btn-warm {
  background: linear-gradient(135deg, var(--tech-warning), #FFB800);
}

/* 危险按钮 */
.layui-btn-danger {
  background: linear-gradient(135deg, var(--tech-error), #FF5252);
}

/* 次要按钮 */
.layui-btn-primary {
  background: var(--tech-surface);
  border: 1px solid var(--tech-border);
  color: var(--tech-text-primary);
}

.layui-btn-primary:hover {
  border-color: var(--tech-primary);
  color: var(--tech-primary);
}

/* 小按钮 */
.layui-btn-sm {
  height: 30px;
  line-height: 30px;
  padding: 0 12px;
  font-size: 12px;
}

/* 超小按钮 */
.layui-btn-xs {
  height: 22px;
  line-height: 22px;
  padding: 0 6px;
  font-size: 12px;
}

/* 圆角按钮 */
.layui-btn-radius {
  border-radius: 100px;
}

/* 禁用按钮 */
.layui-btn-disabled, 
.layui-btn-disabled:hover, 
.layui-btn-disabled:active {
  background: #F5F5F5;
  color: #CCCCCC;
  box-shadow: none;
  cursor: not-allowed;
}

/* 按钮组 */
.layui-btn-group .layui-btn {
  margin: 0;
  border-radius: 0;
  box-shadow: none;
}

.layui-btn-group .layui-btn:first-child {
  border-radius: var(--tech-radius) 0 0 var(--tech-radius);
}

.layui-btn-group .layui-btn:last-child {
  border-radius: 0 var(--tech-radius) var(--tech-radius) 0;
}

/* 表单元素 */
.layui-input, .layui-textarea, .layui-select {
  border: 1px solid var(--tech-border);
  border-radius: var(--tech-radius);
  transition: all 0.3s ease;
}

.layui-input:hover, .layui-textarea:hover, .layui-select:hover {
  border-color: var(--tech-primary-light);
}

.layui-input:focus, .layui-textarea:focus, .layui-select:focus {
  border-color: var(--tech-primary);
  box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.1);
}

/* 表格样式 */
.layui-table {
  background-color: var(--tech-surface);
  color: var(--tech-text-primary);
  border: 1px solid var(--tech-border);
  box-shadow: var(--tech-shadow-1);
  border-radius: var(--tech-radius);
}

.layui-table th {
  background-color: var(--tech-primary);
  color: white;
  font-weight: 500;
}

.layui-table td, .layui-table th {
  border-color: var(--tech-border);
}

.layui-table tr:hover {
  background-color: rgba(0, 102, 204, 0.05);
}

/* 导航菜单 */
.layui-nav {
  background-color: var(--tech-primary-dark);
}

.layui-nav-item a {
  color: rgba(255, 255, 255, 0.8);
}

.layui-nav-item a:hover, 
.layui-nav-itemed > a, 
.layui-nav-itemed > a:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
}

.layui-nav .layui-nav-child {
  background-color: var(--tech-primary);
}

.layui-nav .layui-nav-child dd.layui-this a, 
.layui-nav-child dd.layui-this {
  background-color: var(--tech-primary-light);
}

/* 面板 */
.layui-card {
  box-shadow: var(--tech-shadow-1);
  border-radius: var(--tech-radius);
  border: none;
}

.layui-card-header {
  background-color: var(--tech-surface);
  border-bottom: 1px solid var(--tech-border);
  color: var(--tech-text-primary);
  font-weight: 500;
}

/* 标签页 */
.layui-tab-title li {
  color: var(--tech-text-secondary);
}

.layui-tab-title .layui-this {
  color: var(--tech-primary);
}

.layui-tab-bar {
  border-radius: var(--tech-radius);
}

/* 表单标签 */
.layui-form-label {
  color: var(--tech-text-primary);
  font-weight: 500;
}

/* 复选框和单选框 */
.layui-form-checkbox[lay-skin=primary] i {
  border-color: var(--tech-border);
}

.layui-form-checkbox[lay-skin=primary]:hover i {
  border-color: var(--tech-primary);
}

.layui-form-checked[lay-skin=primary] i {
  background-color: var(--tech-primary);
  border-color: var(--tech-primary);
}

/* 加载动画 */
.layui-layer-loading {
  background: transparent;
}

.layui-layer-loading .layui-layer-content {
  width: 60px;
  height: 60px;
  background: url(/static/image/loading-tech.gif) no-repeat center;
  background-size: contain;
}

/* 消息提示 */
.layui-layer-dialog .layui-layer-content .layui-layer-ico {
  background: url(/static/image/dialog-icons-tech.png) no-repeat;
}

/* 分页 */
.layui-laypage a, .layui-laypage span {
  color: var(--tech-text-primary);
  background: var(--tech-surface);
  border: 1px solid var(--tech-border);
}

.layui-laypage .layui-laypage-curr em {
  background-color: var(--tech-primary);
  color: white;
}

.layui-laypage a:hover {
  color: var(--tech-primary);
}

/* 搜索框 */
.toubu_xnx3_search_form .layui-input {
  height: 36px;
}

/* 操作按钮 */
.layui-btn-sm i.layui-icon {
  font-size: 14px;
}

/* 悬停提示 */
.layui-layer-tips .layui-layer-content {
  background-color: var(--tech-primary-dark);
  border-radius: var(--tech-radius);
}

/* 侧边栏 */
.layui-nav-tree {
  background-color: var(--tech-primary-dark);
  border-radius: 0;
  width: 170px;
}

.layui-nav-tree .layui-nav-item a {
  height: 40px;
  line-height: 40px;
  padding: 0 15px;
}

.layui-nav-tree .layui-nav-item a:hover {
  background-color: var(--tech-primary);
}

.layui-nav-tree .layui-nav-child dd.layui-this, 
.layui-nav-tree .layui-nav-child dd.layui-this a, 
.layui-nav-tree .layui-this, 
.layui-nav-tree .layui-this > a, 
.layui-nav-tree .layui-this > a:hover {
  background-color: var(--tech-primary-light);
  color: white;
}

/* 响应式优化 */
@media screen and (max-width: 750px) {
  .layui-nav-tree {
    width: 60px;
  }
  
  .layui-nav-item a span {
    display: none;
  }
  
  .layui-nav-tree .layui-nav-item a {
    padding: 0;
    text-align: center;
  }
  
  .layui-nav-tree .layui-nav-child {
    left: 60px;
    top: 0;
  }
}

/* 滚动条优化 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 102, 204, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.05);
}