/*
科技风格登录页面样式
适用于人工智能研究院管理系统
*/

:root {
  /* 科技风格颜色变量 */
  --tech-primary: #0066CC;
  --tech-primary-light: #3399FF;
  --tech-primary-dark: #003366;
  --tech-secondary: #00CC99;
  --tech-background: #f0f2f5;
  --tech-surface: #ffffff;
  --tech-text-primary: #212121;
  --tech-text-secondary: #757575;
  --tech-border: #e0e0e0;
  --tech-error: #f44336;
}

/* 整体背景 */
.w3lvide-content {
  background-size: cover !important;
  background-position: center center !important;
  background-repeat: no-repeat !important;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  position: relative;
}

.w3lvide-content::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1;
}

/* 登录表单容器 */
.workinghny-form-grid {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 400px;
  margin: 20px;
  background: var(--tech-surface);
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
  padding: 30px;
  box-sizing: border-box;
}

/* 登录logo */
.login-logo {
  margin: 0 auto 15px;
  display: block;
  max-width: 180px;
  height: auto;
}

/* 表单区域 */
.content-wthree {
  margin: 15px 0 !important;
}

.content-wthree h1 {
  color: var(--tech-primary-dark);
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 25px;
  text-align: center;
}

/* 输入框样式 - 保留原有基础样式并添加科技风格 */
.content-wthree input {
  outline: none;
  margin-bottom: 20px;
  font-size: 16px;
  color: var(--tech-text-primary);
  text-align: left;
  padding: 14px 20px;
  width: 100%;
  display: inline-block;
  box-sizing: border-box;
  border: 1px solid var(--tech-border);
  transition: all 0.3s ease;
  border-radius: 36px;
  background: #f7fafc;
}

.content-wthree input:focus {
  background: transparent;
  border: 1px solid var(--tech-primary);
  box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
}

.content-wthree label {
  width: 100%;
  display: block;
  position: relative;
}

/* 验证码区域 */
.codeBox {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 30%;
  background-color: #f7fafc;
  border-radius: 0 36px 36px 0;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

#code {
  max-width: 100%;
  max-height: 100%;
}

/* 登录按钮 */
.content-wthree button {
  font-size: 18px;
  color: white;
  width: 100%;
  background: linear-gradient(135deg, var(--tech-primary), var(--tech-primary-light));
  border: none;
  padding: 16px 15px;
  font-weight: 700;
  transition: all 0.3s ease;
  border-radius: 36px;
  display: inline-block;
  cursor: pointer;
  box-shadow: 0 4px 6px rgba(0, 102, 204, 0.2);
  margin-top: 10px;
}

.content-wthree button:hover {
  background: linear-gradient(135deg, var(--tech-primary-light), var(--tech-primary));
  box-shadow: 0 6px 12px rgba(0, 102, 204, 0.3);
  transform: translateY(-2px);
}

.content-wthree button:active {
  transform: translateY(0);
}

/* 版权信息 */
.copyright {
  margin-top: 20px;
  padding: 2em 0;
}

.copy-footer-29 {
  text-align: center;
  font-size: 17px;
  line-height: 26px;
  color: #ffffff;
  opacity: 1;
}

.copy-footer-29 a {
  color: #ffffff;
  text-decoration: none;
}

.copy-footer-29 a:hover {
  color: var(--tech-secondary);
  transition: 0.5s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .workinghny-form-grid {
    top: 14%;
    margin: 0 auto;
  }
  
  .copyright p {
    padding: 0 10px;
  }
}

@media (max-width: 568px) {
  .workinghny-form-grid {
    top: 10%;
    margin: 0 auto;
  }
  
  .workinghny-form-grid .main-hotair {
    max-width: 300px;
    margin: 0 auto;
  }
  
  .login-logo {
    max-width: 160px;
  }
}

@media (max-width: 415px) {
}

@media (max-width: 384px) {
}

/* 复选框样式调整 */
.layui-form-item {
  margin-bottom: 20px;
}

.layui-form-item .layui-input-block {
  margin-left: 0;
  text-align: left;
}

.layui-form-item .layui-form-label {
  float: none;
  display: block;
  text-align: left;
  padding: 0 0 5px 0;
  color: var(--tech-text-primary);
  font-weight: 500;
  font-size: 14px;
}

.layui-form-switch {
  background-color: var(--tech-border);
  border: 1px solid var(--tech-border);
  margin-top: 0;
  width: 45px;
  height: 24px;
}

.layui-form-switch.layui-form-onswitch {
  border-color: var(--tech-primary);
  background-color: var(--tech-primary);
}

.layui-form-switch:before {
  width: 22px;
  height: 22px;
}

.layui-form-switch.layui-form-onswitch:before {
  left: 22px;
}

/* 错误提示样式 */
.layui-layer-dialog {
  border-radius: 8px;
}

.layui-layer-dialog .layui-layer-content {
  padding: 20px;
  font-size: 14px;
  color: var(--tech-text-primary);
}

.layui-layer-dialog .layui-layer-ico {
  display: inline-block;
  width: 24px;
  height: 24px;
  margin-right: 10px;
  vertical-align: middle;
  background-color: var(--tech-error);
  border-radius: 50%;
  color: #fff;
  font-weight: bold;
  text-align: center;
  line-height: 24px;
}