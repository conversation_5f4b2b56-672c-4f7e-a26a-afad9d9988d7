!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.Driver=e():t.Driver=e()}(window,function(){return function(t){var e={};function n(o){if(e[o])return e[o].exports;var i=e[o]={i:o,l:!1,exports:{}};return t[o].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=t,n.c=e,n.d=function(t,e,o){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:o})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)n.d(o,i,function(e){return t[e]}.bind(null,i));return o},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="/dist/",n(n.s=57)}([function(t,e,n){var o=n(2),i=n(11),r=n(4),s=n(13),a=n(22),c=function(t,e,n){var u,l,h,f,p=t&c.F,d=t&c.G,v=t&c.S,y=t&c.P,g=t&c.B,m=d?o:v?o[e]||(o[e]={}):(o[e]||{}).prototype,b=d?i:i[e]||(i[e]={}),w=b.prototype||(b.prototype={});for(u in d&&(n=e),n)h=((l=!p&&m&&void 0!==m[u])?m:n)[u],f=g&&l?a(h,o):y&&"function"==typeof h?a(Function.call,h):h,m&&s(m,u,h,t&c.U),b[u]!=h&&r(b,u,f),y&&w[u]!=h&&(w[u]=h)};o.core=i,c.F=1,c.G=2,c.S=4,c.P=8,c.B=16,c.W=32,c.U=64,c.R=128,t.exports=c},function(t,e,n){var o=n(17)("wks"),i=n(14),r=n(2).Symbol,s="function"==typeof r;(t.exports=function(t){return o[t]||(o[t]=s&&r[t]||(s?r:i)("Symbol."+t))}).store=o},function(t,e){var n=t.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},function(t,e){t.exports=function(t){return"object"==typeof t?null!==t:"function"==typeof t}},function(t,e,n){var o=n(5),i=n(16);t.exports=n(6)?function(t,e,n){return o.f(t,e,i(1,n))}:function(t,e,n){return t[e]=n,t}},function(t,e,n){var o=n(12),i=n(35),r=n(21),s=Object.defineProperty;e.f=n(6)?Object.defineProperty:function(t,e,n){if(o(t),e=r(e,!0),o(n),i)try{return s(t,e,n)}catch(t){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(t[e]=n.value),t}},function(t,e,n){t.exports=!n(7)(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},function(t,e){t.exports=function(t){try{return!!t()}catch(t){return!0}}},function(t,e){var n={}.hasOwnProperty;t.exports=function(t,e){return n.call(t,e)}},function(t,e,n){var o=n(39),i=n(24);t.exports=function(t){return o(i(t))}},function(t,e,n){var o=n(0);o(o.S+o.F*!n(6),"Object",{defineProperty:n(5).f})},function(t,e){var n=t.exports={version:"2.6.9"};"number"==typeof __e&&(__e=n)},function(t,e,n){var o=n(3);t.exports=function(t){if(!o(t))throw TypeError(t+" is not an object!");return t}},function(t,e,n){var o=n(2),i=n(4),r=n(8),s=n(14)("src"),a=n(59),c=(""+a).split("toString");n(11).inspectSource=function(t){return a.call(t)},(t.exports=function(t,e,n,a){var u="function"==typeof n;u&&(r(n,"name")||i(n,"name",e)),t[e]!==n&&(u&&(r(n,s)||i(n,s,t[e]?""+t[e]:c.join(String(e)))),t===o?t[e]=n:a?t[e]?t[e]=n:i(t,e,n):(delete t[e],i(t,e,n)))})(Function.prototype,"toString",function(){return"function"==typeof this&&this[s]||a.call(this)})},function(t,e){var n=0,o=Math.random();t.exports=function(t){return"Symbol(".concat(void 0===t?"":t,")_",(++n+o).toString(36))}},function(t,e,n){var o=n(45),i=n(28);t.exports=Object.keys||function(t){return o(t,i)}},function(t,e){t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},function(t,e,n){var o=n(11),i=n(2),r=i["__core-js_shared__"]||(i["__core-js_shared__"]={});(t.exports=function(t,e){return r[t]||(r[t]=void 0!==e?e:{})})("versions",[]).push({version:o.version,mode:n(18)?"pure":"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})},function(t,e){t.exports=!1},function(t,e,n){var o=n(24);t.exports=function(t){return Object(o(t))}},function(t,e,n){"use strict";var o=n(2),i=n(8),r=n(6),s=n(0),a=n(13),c=n(62).KEY,u=n(7),l=n(17),h=n(26),f=n(14),p=n(1),d=n(43),v=n(44),y=n(63),g=n(42),m=n(12),b=n(3),w=n(19),x=n(9),S=n(21),O=n(16),k=n(30),P=n(67),E=n(49),N=n(47),j=n(5),L=n(15),T=E.f,C=j.f,_=P.f,M=o.Symbol,H=o.JSON,B=H&&H.stringify,F=p("_hidden"),I=p("toPrimitive"),R={}.propertyIsEnumerable,A=l("symbol-registry"),z=l("symbols"),D=l("op-symbols"),V=Object.prototype,W="function"==typeof M&&!!N.f,q=o.QObject,G=!q||!q.prototype||!q.prototype.findChild,K=r&&u(function(){return 7!=k(C({},"a",{get:function(){return C(this,"a",{value:7}).a}})).a})?function(t,e,n){var o=T(V,e);o&&delete V[e],C(t,e,n),o&&t!==V&&C(V,e,o)}:C,U=function(t){var e=z[t]=k(M.prototype);return e._k=t,e},Y=W&&"symbol"==typeof M.iterator?function(t){return"symbol"==typeof t}:function(t){return t instanceof M},J=function(t,e,n){return t===V&&J(D,e,n),m(t),e=S(e,!0),m(n),i(z,e)?(n.enumerable?(i(t,F)&&t[F][e]&&(t[F][e]=!1),n=k(n,{enumerable:O(0,!1)})):(i(t,F)||C(t,F,O(1,{})),t[F][e]=!0),K(t,e,n)):C(t,e,n)},X=function(t,e){m(t);for(var n,o=y(e=x(e)),i=0,r=o.length;r>i;)J(t,n=o[i++],e[n]);return t},Q=function(t){var e=R.call(this,t=S(t,!0));return!(this===V&&i(z,t)&&!i(D,t))&&(!(e||!i(this,t)||!i(z,t)||i(this,F)&&this[F][t])||e)},$=function(t,e){if(t=x(t),e=S(e,!0),t!==V||!i(z,e)||i(D,e)){var n=T(t,e);return!n||!i(z,e)||i(t,F)&&t[F][e]||(n.enumerable=!0),n}},Z=function(t){for(var e,n=_(x(t)),o=[],r=0;n.length>r;)i(z,e=n[r++])||e==F||e==c||o.push(e);return o},tt=function(t){for(var e,n=t===V,o=_(n?D:x(t)),r=[],s=0;o.length>s;)!i(z,e=o[s++])||n&&!i(V,e)||r.push(z[e]);return r};W||(a((M=function(){if(this instanceof M)throw TypeError("Symbol is not a constructor!");var t=f(arguments.length>0?arguments[0]:void 0),e=function(n){this===V&&e.call(D,n),i(this,F)&&i(this[F],t)&&(this[F][t]=!1),K(this,t,O(1,n))};return r&&G&&K(V,t,{configurable:!0,set:e}),U(t)}).prototype,"toString",function(){return this._k}),E.f=$,j.f=J,n(48).f=P.f=Z,n(29).f=Q,N.f=tt,r&&!n(18)&&a(V,"propertyIsEnumerable",Q,!0),d.f=function(t){return U(p(t))}),s(s.G+s.W+s.F*!W,{Symbol:M});for(var et="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),nt=0;et.length>nt;)p(et[nt++]);for(var ot=L(p.store),it=0;ot.length>it;)v(ot[it++]);s(s.S+s.F*!W,"Symbol",{for:function(t){return i(A,t+="")?A[t]:A[t]=M(t)},keyFor:function(t){if(!Y(t))throw TypeError(t+" is not a symbol!");for(var e in A)if(A[e]===t)return e},useSetter:function(){G=!0},useSimple:function(){G=!1}}),s(s.S+s.F*!W,"Object",{create:function(t,e){return void 0===e?k(t):X(k(t),e)},defineProperty:J,defineProperties:X,getOwnPropertyDescriptor:$,getOwnPropertyNames:Z,getOwnPropertySymbols:tt});var rt=u(function(){N.f(1)});s(s.S+s.F*rt,"Object",{getOwnPropertySymbols:function(t){return N.f(w(t))}}),H&&s(s.S+s.F*(!W||u(function(){var t=M();return"[null]"!=B([t])||"{}"!=B({a:t})||"{}"!=B(Object(t))})),"JSON",{stringify:function(t){for(var e,n,o=[t],i=1;arguments.length>i;)o.push(arguments[i++]);if(n=e=o[1],(b(e)||void 0!==t)&&!Y(t))return g(e)||(e=function(t,e){if("function"==typeof n&&(e=n.call(this,t,e)),!Y(e))return e}),o[1]=e,B.apply(H,o)}}),M.prototype[I]||n(4)(M.prototype,I,M.prototype.valueOf),h(M,"Symbol"),h(Math,"Math",!0),h(o.JSON,"JSON",!0)},function(t,e,n){var o=n(3);t.exports=function(t,e){if(!o(t))return t;var n,i;if(e&&"function"==typeof(n=t.toString)&&!o(i=n.call(t)))return i;if("function"==typeof(n=t.valueOf)&&!o(i=n.call(t)))return i;if(!e&&"function"==typeof(n=t.toString)&&!o(i=n.call(t)))return i;throw TypeError("Can't convert object to primitive value")}},function(t,e,n){var o=n(37);t.exports=function(t,e,n){if(o(t),void 0===e)return t;switch(n){case 1:return function(n){return t.call(e,n)};case 2:return function(n,o){return t.call(e,n,o)};case 3:return function(n,o,i){return t.call(e,n,o,i)}}return function(){return t.apply(e,arguments)}}},function(t,e){var n={}.toString;t.exports=function(t){return n.call(t).slice(8,-1)}},function(t,e){t.exports=function(t){if(null==t)throw TypeError("Can't call method on  "+t);return t}},function(t,e,n){"use strict";var o=n(7);t.exports=function(t,e){return!!t&&o(function(){e?t.call(null,function(){},1):t.call(null)})}},function(t,e,n){var o=n(5).f,i=n(8),r=n(1)("toStringTag");t.exports=function(t,e,n){t&&!i(t=n?t:t.prototype,r)&&o(t,r,{configurable:!0,value:e})}},function(t,e,n){var o=n(17)("keys"),i=n(14);t.exports=function(t){return o[t]||(o[t]=i(t))}},function(t,e){t.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(t,e){e.f={}.propertyIsEnumerable},function(t,e,n){var o=n(12),i=n(65),r=n(28),s=n(27)("IE_PROTO"),a=function(){},c=function(){var t,e=n(36)("iframe"),o=r.length;for(e.style.display="none",n(66).appendChild(e),e.src="javascript:",(t=e.contentWindow.document).open(),t.write("<script>document.F=Object<\/script>"),t.close(),c=t.F;o--;)delete c.prototype[r[o]];return c()};t.exports=Object.create||function(t,e){var n;return null!==t?(a.prototype=o(t),n=new a,a.prototype=null,n[s]=t):n=c(),void 0===e?n:i(n,e)}},function(t,e,n){"use strict";var o=n(68),i=n(69),r=n(32),s=n(9);t.exports=n(70)(Array,"Array",function(t,e){this._t=s(t),this._i=0,this._k=e},function(){var t=this._t,e=this._k,n=this._i++;return!t||n>=t.length?(this._t=void 0,i(1)):i(0,"keys"==e?n:"values"==e?t[n]:[n,t[n]])},"values"),r.Arguments=r.Array,o("keys"),o("values"),o("entries")},function(t,e){t.exports={}},function(t,e,n){n(44)("asyncIterator")},function(t,e,n){"use strict";var o=n(0),i=n(38)(0),r=n(25)([].forEach,!0);o(o.P+o.F*!r,"Array",{forEach:function(t){return i(this,t,arguments[1])}})},function(t,e,n){t.exports=!n(6)&&!n(7)(function(){return 7!=Object.defineProperty(n(36)("div"),"a",{get:function(){return 7}}).a})},function(t,e,n){var o=n(3),i=n(2).document,r=o(i)&&o(i.createElement);t.exports=function(t){return r?i.createElement(t):{}}},function(t,e){t.exports=function(t){if("function"!=typeof t)throw TypeError(t+" is not a function!");return t}},function(t,e,n){var o=n(22),i=n(39),r=n(19),s=n(40),a=n(60);t.exports=function(t,e){var n=1==t,c=2==t,u=3==t,l=4==t,h=6==t,f=5==t||h,p=e||a;return function(e,a,d){for(var v,y,g=r(e),m=i(g),b=o(a,d,3),w=s(m.length),x=0,S=n?p(e,w):c?p(e,0):void 0;w>x;x++)if((f||x in m)&&(y=b(v=m[x],x,g),t))if(n)S[x]=y;else if(y)switch(t){case 3:return!0;case 5:return v;case 6:return x;case 2:S.push(v)}else if(l)return!1;return h?-1:u||l?l:S}}},function(t,e,n){var o=n(23);t.exports=Object("z").propertyIsEnumerable(0)?Object:function(t){return"String"==o(t)?t.split(""):Object(t)}},function(t,e,n){var o=n(41),i=Math.min;t.exports=function(t){return t>0?i(o(t),9007199254740991):0}},function(t,e){var n=Math.ceil,o=Math.floor;t.exports=function(t){return isNaN(t=+t)?0:(t>0?o:n)(t)}},function(t,e,n){var o=n(23);t.exports=Array.isArray||function(t){return"Array"==o(t)}},function(t,e,n){e.f=n(1)},function(t,e,n){var o=n(2),i=n(11),r=n(18),s=n(43),a=n(5).f;t.exports=function(t){var e=i.Symbol||(i.Symbol=r?{}:o.Symbol||{});"_"==t.charAt(0)||t in e||a(e,t,{value:s.f(t)})}},function(t,e,n){var o=n(8),i=n(9),r=n(46)(!1),s=n(27)("IE_PROTO");t.exports=function(t,e){var n,a=i(t),c=0,u=[];for(n in a)n!=s&&o(a,n)&&u.push(n);for(;e.length>c;)o(a,n=e[c++])&&(~r(u,n)||u.push(n));return u}},function(t,e,n){var o=n(9),i=n(40),r=n(64);t.exports=function(t){return function(e,n,s){var a,c=o(e),u=i(c.length),l=r(s,u);if(t&&n!=n){for(;u>l;)if((a=c[l++])!=a)return!0}else for(;u>l;l++)if((t||l in c)&&c[l]===n)return t||l||0;return!t&&-1}}},function(t,e){e.f=Object.getOwnPropertySymbols},function(t,e,n){var o=n(45),i=n(28).concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return o(t,i)}},function(t,e,n){var o=n(29),i=n(16),r=n(9),s=n(21),a=n(8),c=n(35),u=Object.getOwnPropertyDescriptor;e.f=n(6)?u:function(t,e){if(t=r(t),e=s(e,!0),c)try{return u(t,e)}catch(t){}if(a(t,e))return i(!o.f.call(t,e),t[e])}},function(t,e,n){for(var o=n(31),i=n(15),r=n(13),s=n(2),a=n(4),c=n(32),u=n(1),l=u("iterator"),h=u("toStringTag"),f=c.Array,p={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},d=i(p),v=0;v<d.length;v++){var y,g=d[v],m=p[g],b=s[g],w=b&&b.prototype;if(w&&(w[l]||a(w,l,f),w[h]||a(w,h,g),c[g]=f,m))for(y in o)w[y]||r(w,y,o[y],!0)}},function(t,e,n){"use strict";var o=n(73),i={};i[n(1)("toStringTag")]="z",i+""!="[object z]"&&n(13)(Object.prototype,"toString",function(){return"[object "+o(this)+"]"},!0)},function(t,e,n){var o=n(19),i=n(15);n(74)("keys",function(){return function(t){return i(o(t))}})},function(t,e,n){"use strict";var o=n(0),i=n(38)(2);o(o.P+o.F*!n(25)([].filter,!0),"Array",{filter:function(t){return i(this,t,arguments[1])}})},function(t,e,n){var o=n(0);o(o.P,"Function",{bind:n(75)})},function(t,e,n){var o=n(0);o(o.S,"Object",{create:n(30)})},function(t,e,n){var o=n(0);o(o.S,"Object",{setPrototypeOf:n(81).set})},function(t,e,n){n(58),t.exports=n(82)},function(t,e){},function(t,e,n){t.exports=n(17)("native-function-to-string",Function.toString)},function(t,e,n){var o=n(61);t.exports=function(t,e){return new(o(t))(e)}},function(t,e,n){var o=n(3),i=n(42),r=n(1)("species");t.exports=function(t){var e;return i(t)&&("function"!=typeof(e=t.constructor)||e!==Array&&!i(e.prototype)||(e=void 0),o(e)&&null===(e=e[r])&&(e=void 0)),void 0===e?Array:e}},function(t,e,n){var o=n(14)("meta"),i=n(3),r=n(8),s=n(5).f,a=0,c=Object.isExtensible||function(){return!0},u=!n(7)(function(){return c(Object.preventExtensions({}))}),l=function(t){s(t,o,{value:{i:"O"+ ++a,w:{}}})},h=t.exports={KEY:o,NEED:!1,fastKey:function(t,e){if(!i(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!r(t,o)){if(!c(t))return"F";if(!e)return"E";l(t)}return t[o].i},getWeak:function(t,e){if(!r(t,o)){if(!c(t))return!0;if(!e)return!1;l(t)}return t[o].w},onFreeze:function(t){return u&&h.NEED&&c(t)&&!r(t,o)&&l(t),t}}},function(t,e,n){var o=n(15),i=n(47),r=n(29);t.exports=function(t){var e=o(t),n=i.f;if(n)for(var s,a=n(t),c=r.f,u=0;a.length>u;)c.call(t,s=a[u++])&&e.push(s);return e}},function(t,e,n){var o=n(41),i=Math.max,r=Math.min;t.exports=function(t,e){return(t=o(t))<0?i(t+e,0):r(t,e)}},function(t,e,n){var o=n(5),i=n(12),r=n(15);t.exports=n(6)?Object.defineProperties:function(t,e){i(t);for(var n,s=r(e),a=s.length,c=0;a>c;)o.f(t,n=s[c++],e[n]);return t}},function(t,e,n){var o=n(2).document;t.exports=o&&o.documentElement},function(t,e,n){var o=n(9),i=n(48).f,r={}.toString,s="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];t.exports.f=function(t){return s&&"[object Window]"==r.call(t)?function(t){try{return i(t)}catch(t){return s.slice()}}(t):i(o(t))}},function(t,e,n){var o=n(1)("unscopables"),i=Array.prototype;null==i[o]&&n(4)(i,o,{}),t.exports=function(t){i[o][t]=!0}},function(t,e){t.exports=function(t,e){return{value:e,done:!!t}}},function(t,e,n){"use strict";var o=n(18),i=n(0),r=n(13),s=n(4),a=n(32),c=n(71),u=n(26),l=n(72),h=n(1)("iterator"),f=!([].keys&&"next"in[].keys()),p=function(){return this};t.exports=function(t,e,n,d,v,y,g){c(n,e,d);var m,b,w,x=function(t){if(!f&&t in P)return P[t];switch(t){case"keys":case"values":return function(){return new n(this,t)}}return function(){return new n(this,t)}},S=e+" Iterator",O="values"==v,k=!1,P=t.prototype,E=P[h]||P["@@iterator"]||v&&P[v],N=E||x(v),j=v?O?x("entries"):N:void 0,L="Array"==e&&P.entries||E;if(L&&(w=l(L.call(new t)))!==Object.prototype&&w.next&&(u(w,S,!0),o||"function"==typeof w[h]||s(w,h,p)),O&&E&&"values"!==E.name&&(k=!0,N=function(){return E.call(this)}),o&&!g||!f&&!k&&P[h]||s(P,h,N),a[e]=N,a[S]=p,v)if(m={values:O?N:x("values"),keys:y?N:x("keys"),entries:j},g)for(b in m)b in P||r(P,b,m[b]);else i(i.P+i.F*(f||k),e,m);return m}},function(t,e,n){"use strict";var o=n(30),i=n(16),r=n(26),s={};n(4)(s,n(1)("iterator"),function(){return this}),t.exports=function(t,e,n){t.prototype=o(s,{next:i(1,n)}),r(t,e+" Iterator")}},function(t,e,n){var o=n(8),i=n(19),r=n(27)("IE_PROTO"),s=Object.prototype;t.exports=Object.getPrototypeOf||function(t){return t=i(t),o(t,r)?t[r]:"function"==typeof t.constructor&&t instanceof t.constructor?t.constructor.prototype:t instanceof Object?s:null}},function(t,e,n){var o=n(23),i=n(1)("toStringTag"),r="Arguments"==o(function(){return arguments}());t.exports=function(t){var e,n,s;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=Object(t),i))?n:r?o(e):"Object"==(s=o(e))&&"function"==typeof e.callee?"Arguments":s}},function(t,e,n){var o=n(0),i=n(11),r=n(7);t.exports=function(t,e){var n=(i.Object||{})[t]||Object[t],s={};s[t]=e(n),o(o.S+o.F*r(function(){n(1)}),"Object",s)}},function(t,e,n){"use strict";var o=n(37),i=n(3),r=n(76),s=[].slice,a={};t.exports=Function.bind||function(t){var e=o(this),n=s.call(arguments,1),c=function(){var o=n.concat(s.call(arguments));return this instanceof c?function(t,e,n){if(!(e in a)){for(var o=[],i=0;i<e;i++)o[i]="a["+i+"]";a[e]=Function("F,a","return new F("+o.join(",")+")")}return a[e](t,n)}(e,o.length,o):r(e,o,t)};return i(e.prototype)&&(c.prototype=e.prototype),c}},function(t,e){t.exports=function(t,e,n){var o=void 0===n;switch(e.length){case 0:return o?t():t.call(n);case 1:return o?t(e[0]):t.call(n,e[0]);case 2:return o?t(e[0],e[1]):t.call(n,e[0],e[1]);case 3:return o?t(e[0],e[1],e[2]):t.call(n,e[0],e[1],e[2]);case 4:return o?t(e[0],e[1],e[2],e[3]):t.call(n,e[0],e[1],e[2],e[3])}return t.apply(n,e)}},function(t,e,n){"use strict";n(78)("trim",function(t){return function(){return t(this,3)}})},function(t,e,n){var o=n(0),i=n(24),r=n(7),s=n(79),a="["+s+"]",c=RegExp("^"+a+a+"*"),u=RegExp(a+a+"*$"),l=function(t,e,n){var i={},a=r(function(){return!!s[t]()||"​"!="​"[t]()}),c=i[t]=a?e(h):s[t];n&&(i[n]=c),o(o.P+o.F*a,"String",i)},h=l.trim=function(t,e){return t=String(i(t)),1&e&&(t=t.replace(c,"")),2&e&&(t=t.replace(u,"")),t};t.exports=l},function(t,e){t.exports="\t\n\v\f\r   ᠎             　\u2028\u2029\ufeff"},function(t,e,n){"use strict";var o=n(0),i=n(46)(!1),r=[].indexOf,s=!!r&&1/[1].indexOf(1,-0)<0;o(o.P+o.F*(s||!n(25)(r)),"Array",{indexOf:function(t){return s?r.apply(this,arguments)||0:i(this,t,arguments[1])}})},function(t,e,n){var o=n(3),i=n(12),r=function(t,e){if(i(t),!o(e)&&null!==e)throw TypeError(e+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,e,o){try{(o=n(22)(Function.call,n(49).f(Object.prototype,"__proto__").set,2))(t,[]),e=!(t instanceof Array)}catch(t){e=!0}return function(t,n){return r(t,n),e?t.__proto__=n:o(t,n),t}}({},!1):void 0),check:r}},function(t,e,n){"use strict";n.r(e);n(34),n(20),n(50),n(31),n(51),n(52),n(10),n(53),n(54);var o=.75,i=10,r=!0,s=!0,a=!0,c=!1,u="driver-highlighted-element-stage",l='<div id="'.concat("driver-page-overlay",'"></div>'),h='<div id="'.concat(u,'"></div>');n(33),n(77);function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var p=function(t){var e=document.createElement("div");return e.innerHTML=t.trim(),e.firstChild},d=function t(e,n){if(arguments.length>2&&void 0!==arguments[2]&&arguments[2]){for(var o=["","-webkit-","-ms-","moz-","-o-"],i=0;i<o.length;i++){var r=t(e,o[i]+n);if(r)return r}return""}var s="";return e.currentStyle?s=e.currentStyle[n]:document.defaultView&&document.defaultView.getComputedStyle&&(s=document.defaultView.getComputedStyle(e,null).getPropertyValue(n)),s&&s.toLowerCase?s.toLowerCase():s},v=function(t){return t&&"object"===f(t)&&"nodeType"in t};function y(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}var g=function(){function t(e,n,o){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.options=e,this.highlightedElement=null,this.lastHighlightedElement=null,this.hideTimer=null,this.window=n,this.document=o,this.removeNode=this.removeNode.bind(this)}var e,n,o;return e=t,(n=[{key:"attachNode",value:function(){var t=this.document.getElementById("driver-page-overlay");t||(t=p(l),document.body.appendChild(t)),this.node=t,this.node.style.opacity="0",this.options.animate||this.node.parentElement&&this.node.parentElement.removeChild(this.node)}},{key:"highlight",value:function(t){t&&t.node?t.isSame(this.highlightedElement)||(this.window.clearTimeout(this.hideTimer),t.onHighlightStarted(),this.highlightedElement&&!this.highlightedElement.isSame(this.lastHighlightedElement)&&this.highlightedElement.onDeselected(),t.getCalculatedPosition().canHighlight()&&(this.lastHighlightedElement=this.highlightedElement,this.highlightedElement=t,this.show(),this.highlightedElement.onHighlighted())):console.warn("Invalid element to highlight. Must be an instance of `Element`")}},{key:"show",value:function(){var t=this;this.node&&this.node.parentElement||(this.attachNode(),window.setTimeout(function(){t.node.style.opacity="".concat(t.options.opacity),t.node.style.position="fixed",t.node.style.left="0",t.node.style.top="0",t.node.style.bottom="0",t.node.style.right="0"}))}},{key:"getHighlightedElement",value:function(){return this.highlightedElement}},{key:"getLastHighlightedElement",value:function(){return this.lastHighlightedElement}},{key:"clear",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.options.onReset&&this.options.onReset(this.highlightedElement),this.highlightedElement){this.highlightedElement.onDeselected(!0)}this.highlightedElement=null,this.lastHighlightedElement=null,this.node&&(this.window.clearTimeout(this.hideTimer),this.options.animate&&!t?(this.node.style.opacity="0",this.hideTimer=this.window.setTimeout(this.removeNode,300)):this.removeNode())}},{key:"removeNode",value:function(){this.node&&this.node.parentElement&&this.node.parentElement.removeChild(this.node)}},{key:"refresh",value:function(){this.highlightedElement&&(this.highlightedElement.showPopover(),this.highlightedElement.showStage())}}])&&y(e.prototype,n),o&&y(e,o),t}();n(80);function m(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}var b=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.left,o=void 0===n?0:n,i=e.top,r=void 0===i?0:i,s=e.right,a=void 0===s?0:s,c=e.bottom,u=void 0===c?0:c;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.left=o,this.right=a,this.top=r,this.bottom=u}var e,n,o;return e=t,(n=[{key:"canHighlight",value:function(){return this.left<this.right&&this.top<this.bottom}}])&&m(e.prototype,n),o&&m(e,o),t}();function w(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}var x=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.node,o=e.options,i=e.popover,r=e.stage,s=e.overlay,a=e.window,c=e.document;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.node=n,this.document=c,this.window=a,this.options=o,this.overlay=s,this.popover=i,this.stage=r,this.animationTimeout=null}var e,n,o;return e=t,(n=[{key:"isInView",value:function(){for(var t=this.node.offsetTop,e=this.node.offsetLeft,n=this.node.offsetWidth,o=this.node.offsetHeight,i=this.node;i.offsetParent;)t+=(i=i.offsetParent).offsetTop,e+=i.offsetLeft;return t>=this.window.pageYOffset&&e>=this.window.pageXOffset&&t+o<=this.window.pageYOffset+this.window.innerHeight&&e+n<=this.window.pageXOffset+this.window.innerWidth}},{key:"scrollManually",value:function(){var t=this.node.getBoundingClientRect().top+this.window.pageYOffset-this.window.innerHeight/2;this.window.scrollTo(0,t)}},{key:"bringInView",value:function(){if(this.node&&!this.isInView())if(this.node.scrollIntoView)try{this.node.scrollIntoView(this.options.scrollIntoViewOptions||{behavior:"instant",block:"center"})}catch(t){this.scrollManually()}else this.scrollManually()}},{key:"getCalculatedPosition",value:function(){var t=this.document.body,e=this.document.documentElement,n=this.window,o=this.window.pageYOffset||e.scrollTop||t.scrollTop,i=n.pageXOffset||e.scrollLeft||t.scrollLeft,r=this.node.getBoundingClientRect();return new b({top:r.top+o,left:r.left+i,right:r.left+i+r.width,bottom:r.top+o+r.height})}},{key:"getPopover",value:function(){return this.popover}},{key:"onDeselected",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.hidePopover(),t&&this.hideStage(),this.removeHighlightClasses(),this.window.clearTimeout(this.animationTimeout),this.options.onDeselected&&this.options.onDeselected(this)}},{key:"isSame",value:function(t){return!(!t||!t.node)&&t.node===this.node}},{key:"onHighlightStarted",value:function(){this.options.onHighlightStarted&&this.options.onHighlightStarted(this)}},{key:"onHighlighted",value:function(){this.isInView()||this.bringInView(),this.showPopover(),this.showStage(),this.addHighlightClasses(),this.options.onHighlighted&&this.options.onHighlighted(this)}},{key:"removeHighlightClasses",value:function(){this.node.classList.remove("driver-highlighted-element"),this.node.classList.remove("driver-position-relative");for(var t=this.document.querySelectorAll(".".concat("driver-fix-stacking")),e=0;e<t.length;e++)t[e].classList.remove("driver-fix-stacking")}},{key:"addHighlightClasses",value:function(){this.node.classList.add("driver-highlighted-element"),this.canMakeRelative()&&this.node.classList.add("driver-position-relative"),this.fixStackingContext()}},{key:"fixStackingContext",value:function(){for(var t=this.node.parentNode;t&&t.tagName&&"body"!==t.tagName.toLowerCase();){var e=d(t,"z-index"),n=parseFloat(d(t,"opacity")),o=d(t,"transform",!0),i=d(t,"transform-style",!0),r=d(t,"transform-box",!0),s=d(t,"filter",!0),a=d(t,"perspective",!0);(/[0-9]+/.test(e)||n<1||o&&"none"!==o||i&&"flat"!==i||r&&"border-box"!==r||s&&"none"!==s||a&&"none"!==a)&&t.classList.add("driver-fix-stacking"),t=t.parentNode}}},{key:"canMakeRelative",value:function(){var t=this.getStyleProperty("position");return-1===["absolute","fixed","relative"].indexOf(t)}},{key:"getStyleProperty",value:function(t){return d(this.node,t)}},{key:"showStage",value:function(){this.stage.show(this.getCalculatedPosition())}},{key:"getNode",value:function(){return this.node}},{key:"hideStage",value:function(){this.stage.hide()}},{key:"hidePopover",value:function(){this.popover&&this.popover.hide()}},{key:"showPopover",value:function(){var t=this;if(this.popover){var e=this.getCalculatedPosition(),n=300;this.options.animate&&this.overlay.lastHighlightedElement||(n=0),this.animationTimeout=this.window.setTimeout(function(){t.popover.show(e)},n)}}},{key:"getFullPageSize",value:function(){var t=this.document.body,e=this.document.documentElement;return{height:Math.max(t.scrollHeight,t.offsetHeight,e.scrollHeight,e.offsetHeight),width:Math.max(t.scrollWidth,t.offsetWidth,e.scrollWidth,e.offsetWidth)}}},{key:"getSize",value:function(){return{height:Math.max(this.node.scrollHeight,this.node.offsetHeight),width:Math.max(this.node.scrollWidth,this.node.offsetWidth)}}}])&&w(e.prototype,n),o&&w(e,o),t}();n(55),n(56);function S(t){return(S="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function O(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function k(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}function P(t,e){return!e||"object"!==S(e)&&"function"!=typeof e?function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t):e}function E(t){return(E=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function N(t,e){return(N=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}var j=function(t){function e(t,n,o){var i;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(i=P(this,E(e).call(this))).options=function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},o=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(n).filter(function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable}))),o.forEach(function(e){O(t,e,n[e])})}return t}({isFirst:!0,isLast:!0,totalCount:1,currentIndex:0,offset:0,showButtons:!0,closeBtnText:"Close",doneBtnText:"Done",startBtnText:"Next &rarr;",nextBtnText:"Next &rarr;",prevBtnText:"&larr; Previous"},t),i.window=n,i.document=o,i}var n,o,i;return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&N(t,e)}(e,x),n=e,(o=[{key:"attachNode",value:function(){var t=this.document.getElementById("driver-popover-item");t&&t.parentElement.removeChild(t),t=p(function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return'\n  <div id="'.concat("driver-popover-item",'" class="').concat(t,'">\n    <div class="').concat("driver-popover-tip",'"></div>\n    <div class="').concat("driver-popover-title",'">Popover Title</div>\n    <div class="').concat("driver-popover-description",'">Popover Description</div>\n    <div class="driver-clearfix ').concat("driver-popover-footer",'">\n      <button class="').concat("driver-close-btn",'">Close</button>\n      <span class="driver-btn-group ').concat("driver-navigation-btns",'">\n        <button class="').concat("driver-prev-btn",'">&larr; Previous</button>\n        <button class="').concat("driver-next-btn",'">Next &rarr;</button>\n      </span>\n    </div>\n  </div>')}(this.options.className)),document.body.appendChild(t),this.node=t,this.tipNode=t.querySelector(".".concat("driver-popover-tip")),this.titleNode=t.querySelector(".".concat("driver-popover-title")),this.descriptionNode=t.querySelector(".".concat("driver-popover-description")),this.footerNode=t.querySelector(".".concat("driver-popover-footer")),this.nextBtnNode=t.querySelector(".".concat("driver-next-btn")),this.prevBtnNode=t.querySelector(".".concat("driver-prev-btn")),this.closeBtnNode=t.querySelector(".".concat("driver-close-btn"))}},{key:"getTitleNode",value:function(){return this.titleNode}},{key:"getDescriptionNode",value:function(){return this.descriptionNode}},{key:"hide",value:function(){this.node&&(this.node.style.display="none")}},{key:"setInitialState",value:function(){this.node.style.display="block",this.node.style.left="0",this.node.style.top="0",this.node.style.bottom="",this.node.style.right="",this.node.querySelector(".".concat("driver-popover-tip")).className="driver-popover-tip"}},{key:"show",value:function(t){switch(this.attachNode(),this.setInitialState(),this.titleNode.innerHTML=this.options.title,this.descriptionNode.innerHTML=this.options.description||"",this.renderFooter(),this.options.position){case"left":case"left-top":this.positionOnLeft(t);break;case"left-center":this.positionOnLeftCenter(t);break;case"left-bottom":this.positionOnLeftBottom(t);break;case"right":case"right-top":this.positionOnRight(t);break;case"right-center":this.positionOnRightCenter(t);break;case"right-bottom":this.positionOnRightBottom(t);break;case"top":case"top-left":this.positionOnTop(t);break;case"top-center":this.positionOnTopCenter(t);break;case"top-right":this.positionOnTopRight(t);break;case"bottom":case"bottom-left":this.positionOnBottom(t);break;case"bottom-center":this.positionOnBottomCenter(t);break;case"bottom-right":this.positionOnBottomRight(t);break;case"mid-center":this.positionOnMidCenter(t);break;case"auto":default:this.autoPosition(t)}this.bringInView()}},{key:"renderFooter",value:function(){this.nextBtnNode.innerHTML=this.options.nextBtnText,this.prevBtnNode.innerHTML=this.options.prevBtnText,this.closeBtnNode.innerHTML=this.options.closeBtnText;var t=this.options.totalCount&&1!==this.options.totalCount;this.options.showButtons?(t?(this.nextBtnNode.style.display="inline-block",this.prevBtnNode.style.display="inline-block",this.closeBtnNode.classList.remove("driver-close-only-btn")):(this.nextBtnNode.style.display="none",this.prevBtnNode.style.display="none",this.closeBtnNode.classList.add("driver-close-only-btn")),this.footerNode.style.display="block",this.options.isFirst?(this.prevBtnNode.classList.add("driver-disabled"),this.nextBtnNode.innerHTML=this.options.startBtnText):this.prevBtnNode.classList.remove("driver-disabled"),this.options.isLast?this.nextBtnNode.innerHTML=this.options.doneBtnText:this.nextBtnNode.innerHTML=this.options.nextBtnText):this.footerNode.style.display="none"}},{key:"positionOnLeft",value:function(t){var e=this.getSize().width,n=this.options.padding+10;this.node.style.left="".concat(t.left-e-n,"px"),this.node.style.top="".concat(t.top+this.options.offset-this.options.padding,"px"),this.node.style.right="",this.node.style.bottom="",this.tipNode.classList.add("right")}},{key:"positionOnLeftBottom",value:function(t){var e=this.getSize(),n=e.width,o=this.options.padding+10;this.node.style.left="".concat(t.left-n-o,"px"),this.node.style.top="".concat(t.bottom+this.options.padding+this.options.offset-e.height,"px"),this.node.style.bottom="",this.node.style.right="",this.tipNode.classList.add("right","position-bottom")}},{key:"positionOnLeftCenter",value:function(t){var e=this.getSize(),n=e.width,o=e.height/2,i=this.options.padding+10,r=(t.bottom-t.top)/2,s=t.top-o+r+this.options.offset;this.node.style.left="".concat(t.left-n-i,"px"),this.node.style.top="".concat(s,"px"),this.node.style.right="",this.node.style.bottom="",this.tipNode.classList.add("right","position-center")}},{key:"positionOnRight",value:function(t){var e=this.options.padding+10;this.node.style.left="".concat(t.right+e,"px"),this.node.style.top="".concat(t.top+this.options.offset-this.options.padding,"px"),this.node.style.right="",this.node.style.bottom="",this.tipNode.classList.add("left")}},{key:"positionOnRightCenter",value:function(t){var e=this.getSize(),n=this.options.padding+10,o=e.height/2,i=(t.bottom-t.top)/2,r=t.top-o+i+this.options.offset;this.node.style.left="".concat(t.right+n,"px"),this.node.style.top="".concat(r,"px"),this.node.style.right="",this.node.style.bottom="",this.tipNode.classList.add("left","position-center")}},{key:"positionOnRightBottom",value:function(t){var e=this.options.padding+10,n=this.getSize();this.node.style.left="".concat(t.right+e,"px"),this.node.style.top="".concat(t.bottom+this.options.padding+this.options.offset-n.height,"px"),this.node.style.bottom="",this.node.style.right="",this.tipNode.classList.add("left","position-bottom")}},{key:"positionOnTop",value:function(t){var e=this.getSize().height,n=this.options.padding+10;this.node.style.top="".concat(t.top-e-n,"px"),this.node.style.left="".concat(t.left-this.options.padding+this.options.offset,"px"),this.node.style.right="",this.node.style.bottom="",this.tipNode.classList.add("bottom")}},{key:"positionOnTopCenter",value:function(t){var e=this.getSize(),n=e.height,o=e.width/2,i=this.options.padding+10,r=this.options.offset+t.left+(t.right-t.left)/2;this.node.style.top="".concat(t.top-n-i,"px"),this.node.style.left="".concat(r-o-this.options.padding,"px"),this.node.style.right="",this.node.style.bottom="",this.tipNode.classList.add("bottom","position-center")}},{key:"positionOnTopRight",value:function(t){var e=this.getSize(),n=e.height,o=this.options.padding+10;this.node.style.top="".concat(t.top-n-o,"px"),this.node.style.left="".concat(t.right+this.options.padding+this.options.offset-e.width,"px"),this.node.style.right="",this.node.style.bottom="",this.tipNode.classList.add("bottom","position-right")}},{key:"positionOnBottom",value:function(t){var e=this.options.padding+10;this.node.style.top="".concat(t.bottom+e,"px"),this.node.style.left="".concat(t.left-this.options.padding+this.options.offset,"px"),this.node.style.right="",this.node.style.bottom="",this.tipNode.classList.add("top")}},{key:"positionOnBottomCenter",value:function(t){var e=this.getSize().width/2,n=this.options.padding+10,o=this.options.offset+t.left+(t.right-t.left)/2;this.node.style.top="".concat(t.bottom+n,"px"),this.node.style.left="".concat(o-e-this.options.padding,"px"),this.node.style.right="",this.node.style.bottom="",this.tipNode.classList.add("top","position-center")}},{key:"positionOnBottomRight",value:function(t){var e=this.getSize(),n=this.options.padding+10;this.node.style.top="".concat(t.bottom+n,"px"),this.node.style.left="".concat(t.right+this.options.padding+this.options.offset-e.width,"px"),this.node.style.right="",this.node.style.bottom="",this.tipNode.classList.add("top","position-right")}},{key:"positionOnMidCenter",value:function(t){var e=this.getSize(),n=e.height,o=e.width/2,i=n/2,r=(t.bottom-t.top)/2,s=t.top-i+r+this.options.offset,a=this.options.offset+t.left+(t.right-t.left)/2;this.node.style.top="".concat(s,"px"),this.node.style.left="".concat(a-o-this.options.padding,"px"),this.node.style.right="",this.node.style.bottom="",this.tipNode.classList.add("mid-center")}},{key:"autoPosition",value:function(t){var e=this.getFullPageSize(),n=this.getSize(),o=e.height,i=n.height,r=this.options.padding+10;t.bottom+i+r>=o?this.positionOnTop(t):this.positionOnBottom(t)}}])&&k(n.prototype,o),i&&k(n,i),e}();function L(t){return(L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function T(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}function C(t,e){return!e||"object"!==L(e)&&"function"!=typeof e?function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t):e}function _(t){return(_=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function M(t,e){return(M=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}var H=function(t){function e(t,n,o){var i;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(i=C(this,_(e).call(this))).options=t,i.window=n,i.document=o,i}var n,o,i;return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&M(t,e)}(e,x),n=e,(o=[{key:"attachNode",value:function(){var t=this.document.getElementById(u);t||(t=p(h),document.body.appendChild(t)),this.node=t,this.options.animate?this.node.classList.remove("driver-stage-no-animation"):this.node.classList.add("driver-stage-no-animation")}},{key:"hide",value:function(){this.node&&this.node.parentElement&&this.node.parentElement.removeChild(this.node)}},{key:"setInitialStyle",value:function(){this.node.style.display="block",this.node.style.left="0",this.node.style.top="0",this.node.style.bottom="",this.node.style.right=""}},{key:"show",value:function(t){this.attachNode(),this.setInitialStyle();var e=2*this.options.padding,n=t.right-t.left+e,o=t.bottom-t.top+e;this.node.style.display="block",this.node.style.position="absolute",this.node.style.width="".concat(n,"px"),this.node.style.height="".concat(o,"px"),this.node.style.top="".concat(t.top-e/2,"px"),this.node.style.left="".concat(t.left-e/2,"px"),this.node.style.backgroundColor=this.options.stageBackground}}])&&T(n.prototype,o),i&&T(n,i),e}();function B(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},o=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(o=o.concat(Object.getOwnPropertySymbols(n).filter(function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable}))),o.forEach(function(e){F(t,e,n[e])})}return t}function F(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function I(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}n.d(e,"default",function(){return R});var R=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.options=B({animate:r,opacity:o,padding:i,scrollIntoViewOptions:null,allowClose:s,keyboardControl:a,overlayClickNext:c,stageBackground:"#ffffff",onHighlightStarted:function(){return null},onHighlighted:function(){return null},onDeselected:function(){return null},onReset:function(){return null},onNext:function(){return null},onPrevious:function(){return null}},e),this.document=document,this.window=window,this.isActivated=!1,this.steps=[],this.currentStep=0,this.currentMovePrevented=!1,this.overlay=new g(this.options,window,document),this.onResize=this.onResize.bind(this),this.onKeyUp=this.onKeyUp.bind(this),this.onClick=this.onClick.bind(this),this.moveNext=this.moveNext.bind(this),this.movePrevious=this.movePrevious.bind(this),this.preventMove=this.preventMove.bind(this),this.bind()}var e,n,u;return e=t,(n=[{key:"getSteps",value:function(){return this.steps}},{key:"setSteps",value:function(t){this.steps=t}},{key:"bind",value:function(){this.window.addEventListener("resize",this.onResize,!1),this.window.addEventListener("keyup",this.onKeyUp,!1),"ontouchstart"in document.documentElement?this.window.addEventListener("touchstart",this.onClick,!1):this.window.addEventListener("click",this.onClick,!1)}},{key:"onClick",value:function(t){if(this.isActivated&&this.hasHighlightedElement()){t.stopPropagation();var e=this.overlay.getHighlightedElement(),n=this.document.getElementById("driver-popover-item"),o=e.node.contains(t.target),i=n&&n.contains(t.target);if(o||i||!this.options.overlayClickNext)if(o||i||!this.options.allowClose){var r=t.target.classList.contains("driver-next-btn"),s=t.target.classList.contains("driver-prev-btn");t.target.classList.contains("driver-close-btn")?this.reset():r?this.handleNext():s&&this.handlePrevious()}else this.reset();else this.handleNext()}}},{key:"onResize",value:function(){this.isActivated&&this.refresh()}},{key:"refresh",value:function(){this.overlay.refresh()}},{key:"onKeyUp",value:function(t){if(this.isActivated&&this.options.keyboardControl)if(27!==t.keyCode){var e=this.getHighlightedElement();e&&e.popover&&(39===t.keyCode?this.handleNext():37===t.keyCode&&this.handlePrevious())}else this.reset()}},{key:"movePrevious",value:function(){var t=this.steps[this.currentStep-1];t?(this.overlay.highlight(t),this.currentStep-=1):this.reset()}},{key:"preventMove",value:function(){this.currentMovePrevented=!0}},{key:"handleNext",value:function(){this.currentMovePrevented=!1;var t=this.steps[this.currentStep];t&&t.options&&t.options.onNext&&t.options.onNext(this.overlay.highlightedElement),this.currentMovePrevented||this.moveNext()}},{key:"handlePrevious",value:function(){this.currentMovePrevented=!1;var t=this.steps[this.currentStep];t&&t.options&&t.options.onPrevious&&t.options.onPrevious(this.overlay.highlightedElement),this.currentMovePrevented||this.movePrevious()}},{key:"moveNext",value:function(){var t=this.steps[this.currentStep+1];t?(this.overlay.highlight(t),this.currentStep+=1):this.reset()}},{key:"hasNextStep",value:function(){return!!this.steps[this.currentStep+1]}},{key:"hasPreviousStep",value:function(){return!!this.steps[this.currentStep-1]}},{key:"reset",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.currentStep=0,this.isActivated=!1,this.overlay.clear(t)}},{key:"hasHighlightedElement",value:function(){var t=this.overlay.getHighlightedElement();return t&&t.node}},{key:"getHighlightedElement",value:function(){return this.overlay.getHighlightedElement()}},{key:"getLastHighlightedElement",value:function(){return this.overlay.getLastHighlightedElement()}},{key:"defineSteps",value:function(t){this.steps=[];for(var e=0;e<t.length;e++){var n=this.prepareElementFromStep(t[e],t,e);n&&this.steps.push(n)}}},{key:"prepareElementFromStep",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=B({},this.options),i=t,r="string"!=typeof t&&!v(t);if(!t||r&&!t.element)throw new Error("Element is required in step ".concat(n));r&&(i=t.element,o=B({},this.options,t));var s=v(i)?i:this.document.querySelector(i);if(!s)return console.warn("Element to highlight ".concat(i," not found")),null;var a=null;if(o.popover&&o.popover.title){var c=[this.options.className,o.popover.className].filter(function(t){return t}).join(" "),u=B({},o,o.popover,{className:c,totalCount:e.length,currentIndex:n,isFirst:0===n,isLast:0===e.length||n===e.length-1});a=new j(u,this.window,this.document)}var l=B({},o),h=new H(l,this.window,this.document);return new x({node:s,options:o,popover:a,stage:h,overlay:this.overlay,window:this.window,document:this.document})}},{key:"start",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if(!this.steps||0===this.steps.length)throw new Error("There are no steps defined to iterate");this.isActivated=!0,this.currentStep=t,this.overlay.highlight(this.steps[t])}},{key:"highlight",value:function(t){this.isActivated=!0;var e=this.prepareElementFromStep(t);e&&this.overlay.highlight(e)}}])&&I(e.prototype,n),u&&I(e,u),t}()}]).default});
//# sourceMappingURL=driver.min.js.map