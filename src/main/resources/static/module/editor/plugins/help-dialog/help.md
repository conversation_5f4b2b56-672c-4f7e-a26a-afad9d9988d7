##### Markdown语法教程 (Markdown syntax tutorial)

- [Markdown Syntax](http://daringfireball.net/projects/markdown/syntax/ "Markdown Syntax")
- [Mastering Markdown](https://guides.github.com/features/mastering-markdown/ "Mastering Markdown")
- [Markdown Basics](https://help.github.com/articles/markdown-basics/ "Markdown Basics")
- [GitHub Flavored Markdown](https://help.github.com/articles/github-flavored-markdown/ "GitHub Flavored Markdown")
- [Markdown 语法说明（简体中文）](http://www.markdown.cn/ "Markdown 语法说明（简体中文）")
- [Markdown 語法說明（繁體中文）](http://markdown.tw/ "Markdown 語法說明（繁體中文）")

##### 键盘快捷键 (Keyboard shortcuts)

> If Editor.md code editor is on focus, you can use keyboard shortcuts.
    
| Keyboard shortcuts (键盘快捷键)                 |   说明                            | Description                                        |
| :---------------------------------------------- |:--------------------------------- | :------------------------------------------------- |
| F9                                              | 切换实时预览                      | Switch watch/unwatch                               |
| F10                                             | 全屏HTML预览(按 Shift + ESC 退出) | Full preview HTML (Press Shift + ESC exit)         |
| F11                                             | 切换全屏状态                      | Switch fullscreen (Press ESC exit)                 |
| Ctrl + 1~6 / Command + 1~6                      | 插入标题1~6                       | Insert heading 1~6                                 |
| Ctrl + A / Command + A                          | 全选                              | Select all                                         |
| Ctrl + B / Command + B                          | 插入粗体                          | Insert bold                                        |
| Ctrl + D / Command + D                          | 插入日期时间                      | Insert datetime                                    |
| Ctrl + E / Command + E                          | 插入Emoji符号                     | Insert &#58;emoji&#58;                             |
| Ctrl + F / Command + F                          | 查找/搜索                         | Start searching                                    |
| Ctrl + G / Command + G                          | 切换到下一个搜索结果项            | Find next search results                           |
| Ctrl + H / Command + H                          | 插入水平线                        | Insert horizontal rule                             |
| Ctrl + I / Command + I                          | 插入斜体                          | Insert italic                                      |
| Ctrl + K / Command + K                          | 插入行内代码                      | Insert inline code                                 |
| Ctrl + L / Command + L                          | 插入链接                          | Insert link                                        |
| Ctrl + U / Command + U                          | 插入无序列表                      | Insert unordered list                              |
| Ctrl + Q                                        | 代码折叠切换                      | Switch code fold                                   |
| Ctrl + Z / Command + Z                          | 撤销                              | Undo                                               |
| Ctrl + Y / Command + Y                          | 重做                              | Redo                                               |
| Ctrl + Shift + A                                | 插入@链接                         | Insert &#64;link                                   |
| Ctrl + Shift + C                                | 插入行内代码                      | Insert inline code                                 |
| Ctrl + Shift + E                                | 打开插入Emoji表情对话框           | Open emoji dialog                                  |
| Ctrl + Shift + F / Command + Option + F         | 替换                              | Replace                                            |
| Ctrl + Shift + G / Shift + Command + G          | 切换到上一个搜索结果项            | Find previous search results                       |
| Ctrl + Shift + H                                | 打开HTML实体字符对话框            | Open HTML Entities dialog                          |
| Ctrl + Shift + I                                | 插入图片                          | Insert image &#33;[]&#40;&#41;                     |
| Ctrl + Shift + K                                | 插入TeX(KaTeX)公式符号            | Insert TeX(KaTeX) symbol &#36;&#36;TeX&#36;&#36;   |
| Ctrl + Shift + L                                | 打开插入链接对话框                | Open link dialog                                   |
| Ctrl + Shift + O                                | 插入有序列表                      | Insert ordered list                                |
| Ctrl + Shift + P                                | 打开插入PRE对话框                 | Open Preformatted text dialog                      |
| Ctrl + Shift + Q                                | 插入引用                          | Insert blockquotes                                 |
| Ctrl + Shift + R / Shift + Command + Option + F | 全部替换                          | Replace all                                        |
| Ctrl + Shift + S                                | 插入删除线                        | Insert strikethrough                               |
| Ctrl + Shift + T                                | 打开插入表格对话框                | Open table dialog                                  |
| Ctrl + Shift + U                                | 将所选文字转成大写                | Selection text convert to uppercase                |
| Shift + Alt + C                                 | 插入```代码                       | Insert code blocks (```)                           |
| Shift + Alt + H                                 | 打开使用帮助对话框                | Open help dialog                                   |
| Shift + Alt + L                                 | 将所选文本转成小写                | Selection text convert to lowercase                |
| Shift + Alt + P                                 | 插入分页符                        | Insert page break                                  |
| Alt + L                                         | 将所选文本转成小写                | Selection text convert to lowercase                |
| Shift + Alt + U                                 | 将所选的每个单词的首字母转成大写  | Selection words first letter convert to Uppercase  |
| Ctrl + Shift + Alt + C                          | 打开插入代码块对话框层            | Open code blocks dialog                            |
| Ctrl + Shift + Alt + I                          | 打开插入图片对话框层              | Open image dialog                                  |
| Ctrl + Shift + Alt + U                          | 将所选文本的第一个首字母转成大写  | Selection text first letter convert to uppercase   |
| Ctrl + Alt + G                                  | 跳转到指定的行                    | Goto line                                          |

##### Emoji表情参考 (Emoji reference)

- [Github emoji](http://www.emoji-cheat-sheet.com/ "Github emoji")
- [Twitter Emoji \(Twemoji\)](http://twitter.github.io/twemoji/preview.html "Twitter Emoji \(Twemoji\)")
- [FontAwesome icons emoji](http://fortawesome.github.io/Font-Awesome/icons/ "FontAwesome icons emoji")

##### 流程图参考 (Flowchart reference)

[http://adrai.github.io/flowchart.js/](http://adrai.github.io/flowchart.js/)

##### 时序图参考 (SequenceDiagram reference)

[http://bramp.github.io/js-sequence-diagrams/](http://bramp.github.io/js-sequence-diagrams/)

##### TeX/LaTeX reference

[http://meta.wikimedia.org/wiki/Help:Formula](http://meta.wikimedia.org/wiki/Help:Formula)
