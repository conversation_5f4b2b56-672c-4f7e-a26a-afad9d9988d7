/**

 layui官网
 By 贤心

*/


/* 布局 */
.site-inline{font-size: 0;}
.site-tree, .site-content{display: inline-block;  *display:inline; *zoom:1; vertical-align: top; font-size: 14px;}
.site-tree{width: 220px; min-height: 600px; padding: 5px 0 20px;}
.site-content{width: 899px; min-height: 600px; padding: 20px 0 10px 20px;}

/* 头部 */
.header{height: 59px; border-bottom: 1px solid #404553;  background-color: #393D49; color: #fff;}
.logo{position: absolute; left: 0; top: 13px;}
.logo img{width: 82px; height: 31px;}

.header .layui-nav{position: absolute; right: 0; top: 0; padding: 0; background: none;}
.header .layui-nav .layui-nav-item{margin: 0 20px; }
.header .layui-nav .layui-nav-item[mobile]{display: none;}


.menu{position: absolute; right: 0; top: 0; line-height: 65px;}
.menu a{display:inline-block; *display:inline; *zoom:1; vertical-align:top;}
.menu a{position: relative; padding: 0 20px; margin: 0 20px; color: #c2c2c2; font-size: 14px;}
.menu a:hover{color: #fff; transition: all .5s; -webkit-transition: all .5s}
.menu a.this{color: #fff}
.menu a.this::after{content: ''; position: absolute; left: 0; bottom: -1px; width: 100%; height: 5px; background-color: #5FB878;}

.header-index{background-color: #0C1206; border: none;}

/*.layui-layout-manager .header-demo{border-bottom: linear-gradient(0deg, #f90041 0%, #ff5e52 100%); background-color: #fff;}*/
.header-demo{height: 60px; border-bottom: none;}
.header-demo .logo{left: 40px;}
.header-demo .layui-nav{top: 0;}
.header-demo .layui-nav .layui-nav-item{margin: 0 10px;}

.header-demo .layui-nav .layui-this a{padding: 0 30px;}

/* 底部 */
.footer{padding: 30px 0; line-height: 30px; text-align: center; background-color: #eee; color: #666; font-weight: 300;}
body .layui-layout-manager .footer-demo{height: auto; padding: 15px 0; line-height: 26px;}
.footer a{padding: 0 5px;}

/* 首页banner部分 */
.site-banner{position: relative; height: 600px; text-align: center; overflow: hidden; background-color: #393D49;}
.site-banner-bg
,.site-banner-main{position: absolute; left: 0; top: 0; width: 100%; height: 100%;}
.site-banner-bg{background-position: center 0;}


.site-zfj{padding-top: 25px; height: 220px;}
.site-zfj i{position: absolute; left: 50%; top: 25px; width: 200px; height: 200px; margin-left: -100px; font-size: 200px; color: #c2c2c2;}

@-webkit-keyframes site-zfj {
  0% {opacity: 1;  -webkit-transform: translate3d(0, 0, 0) rotate(0deg) scale(1);}
  10% {opacity: 0.8; -webkit-transform: translate3d(-100px, 0px, 0) rotate(10deg) scale(0.7);}
  35% {opacity: 0.6; -webkit-transform: translate3d(100px, 0px, 0) rotate(30deg) scale(0.4);}
  50% {opacity: 0.4; -webkit-transform: translate3d(0, 0, 0) rotate(360deg) scale(0);}
  80% {opacity: 0.2; -webkit-transform: translate3d(0, 0, 0) rotate(720deg) scale(1);}
  90% {opacity: 0.1; -webkit-transform: translate3d(0, 0, 0) rotate(3600deg) scale(6);}
  100% {opacity: 1; -webkit-transform: translate3d(0, 0, 0) rotate(3600deg) scale(1);}
}
@keyframes site-zfj {
  0% {opacity: 1;  transform: translate3d(0, 0, 0) rotate(0deg) scale(1);}
  10% {opacity: 0.8; transform: translate3d(-100px, 0px, 0) rotate(10deg) scale(0.7);}
  35% {opacity: 0.6; transform: translate3d(100px, 0px, 0) rotate(30deg) scale(0.4);}
  50% {opacity: 0.4; transform: translate3d(0, 0, 0) rotate(360deg) scale(0);}
  80% {opacity: 0.2; transform: translate3d(0, 0, 0) rotate(720deg) scale(1);}
  90% {opacity: 0.1; transform: translate3d(0, 0, 0) rotate(3600deg) scale(6);}
  100% {opacity: 1; transform: translate3d(0, 0, 0) rotate(3600deg) scale(1);}
}

@-webkit-keyframes site-desc {
  0% { -webkit-transform: scale(1.1);}
  100% {opacity: 1; -webkit-transform: scale(1);}
}
@keyframes site-desc {
  0% { transform: scale(1.1);}
  100% {transform: scale(1);}
}

.layui-anim-scaleSpring{-webkit-animation-name: layui-scale-spring; animation-name: layui-scale-spring}
.site-zfj-anim i{-webkit-animation-name: site-zfj; animation-name: site-zfj; -webkit-animation-duration: 5s; animation-duration: 5s;  -webkit-animation-timing-function: linear; animation-timing-function: linear;}


.site-desc{position: relative; height: 70px; margin-top: 25px;  background: url(../images/layui/desc.png) center no-repeat;}
.site-desc-anim{-webkit-animation-name: site-desc; animation-name: site-desc;}

.site-desc cite{position: absolute; bottom: -40px; left: 0; width: 100%; color: #c2c2c2; font-style: normal;}
.site-download{margin-top: 80px; font-size: 0;}
.site-download a{position: relative; padding: 0 45px 0 90px; height: 60px; line-height: 60px; border: 1px solid rgba(255,255,255,.2); font-size: 24px; color: #ccc; transition: all .5s; -webkit-transition: all .5s;}
.site-download a:hover{border-color: rgba(255,255,255,.3); color: #fff; border-radius: 30px; }
.site-download a cite{position: absolute; left: 45px; font-size: 30px;}
.site-version{position: relative; margin-top: 15px; color: #ccc; font-size: 12px;}
.site-version span{padding: 0 3px;}
.site-version *{font-style: normal;}
.site-version a{color: #e2e2e2; text-decoration: underline;}

.site-banner-other{position: absolute; left: 0; bottom: 32px; width: 100%; text-align: center;}
.site-banner-other iframe{border: none;}

.site-idea{margin: 50px 0; font-size: 0; text-align: center; font-weight: 300;}
.site-idea li{display: inline-block; vertical-align: top; *display: inline; *zoom:1; font-size: 14px; }
.site-idea li{width: 298px; height: 150px; padding: 30px; line-height: 24px; margin-left: 30px; border: 1px solid #d2d2d2; text-align: left;}
.site-idea li:first-child{margin-left: 0}
.site-idea .layui-field-title{border-color: #d2d2d2}
.site-idea .layui-field-title legend{margin: 0 20px 20px 0; padding: 0 20px; text-align: center;}


/* 辅助 */
.site-tips{margin-bottom: 10px; padding: 15px; line-height: 22px; border-left: 5px solid #0078AD; background-color: #f2f2f2;}
body .site-tips p{margin: 0;}

/* 目录 */
.site-dir{display: none;}
.site-dir li{line-height: 26px; margin-left: 20px; overflow: visible; list-style-type: disc;}
.site-dir li a{display: block;}
.site-dir li a:active{color: #01AAED;}
.site-dir li a.layui-this{color: #01AAED;}
body .layui-layer-dir{box-shadow: none; border: 1px solid #d2d2d2;}
body .layui-layer-dir .layui-layer-content{padding: 10px; max-height: 280px; overflow: auto;}
.site-dir a em{padding-left: 5px; font-size: 12px; color: #c2c2c2; font-style: normal;}

/* 文档 */
.site-tree{border-right: 1px solid #eee; }
.site-tree .layui-tree{line-height: 32px;}
.site-tree .layui-tree li i{position: relative; font-size: 22px; color: #000}
.site-tree .layui-tree li a cite{padding: 0 8px;}
.site-tree .layui-tree .site-tree-noicon a cite{padding-left: 15px;}
.site-tree .layui-tree li a em{font-size: 12px; color: #bbb; padding-right: 5px; font-style: normal;}
.site-tree .layui-tree li h2{line-height: 36px; border-left: 5px solid #009E94; margin: 15px 0 5px; padding: 0 10px; background-color: #f2f2f2;}
.site-tree .layui-tree li ul{margin-left: 27px; line-height: 28px;}
.site-tree .layui-tree li ul a,
.site-tree .layui-tree li ul a i{color: #777;}
.site-tree .layui-tree li ul a:hover{color: #333;}
.site-tree .layui-tree li ul li{margin-left: 25px; overflow: visible; list-style-type: disc; /*list-style-position: inside;*/}
.site-tree .layui-tree li ul li cite,
.site-tree .layui-tree .site-tree-noicon ul li cite{padding-left: 0;}

.site-tree .layui-tree .layui-this a{color: #01AAED;}
.site-tree .layui-tree .layui-this .layui-icon{color: #01AAED;}

.site-fix .site-tree{position: fixed; top: 0; bottom: 0; z-index: 666; min-height: 0; overflow: auto;  background-color: #fff;}
.site-fix .site-content{margin-left: 220px;}
.site-fix-footer .site-tree{margin-bottom: 120px;}


.site-title{ margin: 30px 0 20px;}
.site-title fieldset{border: none; padding: 0; border-top: 1px solid #eee;}
.site-title fieldset legend{margin-left: 20px;  padding: 0 10px; font-size: 22px; font-weight: 300;}

.site-text a{color: #01AAED;}
.site-h1{margin-bottom: 20px; line-height: 60px; padding-bottom: 10px; color: #393D49; border-bottom: 1px solid #eee;  font-size: 28px; font-weight: 300;}
.site-h1 .layui-icon{position: relative; top: 5px; font-size: 50px; margin-right: 10px;}
.site-text{position:relative;}
.site-text p{margin-bottom: 10px;  line-height:22px;}
.site-text em{padding: 0 3px; font-weight: 500; font-style: italic; color: #666;}
.site-text code{margin:0 5px; padding: 3px 10px; border: 1px solid #e2e2e2; background-color: #fbfbfb; color: #666; border-radius: 2px;}

.site-table{width: 100%; margin: 10px 0;}
.site-table thead{background-color:#f2f2f2; }
.site-table th, 
.site-table td{padding: 6px 15px; min-height: 20px; line-height: 20px; border:1px solid #ddd; font-size: 14px; font-weight: 400;}
.site-table tr:nth-child(even){background: #fbfbfb;}

.site-block{padding: 20px; border: 1px solid #eee;}
.site-block .layui-form{margin-right: 200px;}


/* 演示 */
body .layui-layout-manager .site-demo{bottom: 82px; padding: 0;}
body .site-demo-nav .layui-nav-item{line-height: 40px}
.layui-nav-item .layui-icon{position: relative; font-size: 20px;}
.layui-nav-item a cite{padding: 0 10px;}
.site-demo .layui-main{margin: 15px; line-height: 22px;}
.site-demo-editor{position: absolute; top: 0; bottom: 0; left: 0; width: 50%; }
.site-demo-area{position: absolute; top: 0; bottom: 0; width: 100%;}
.site-demo-editor textarea{position: absolute; width: 100%; height: 100%; padding: 15px; border: none; resize: none; background-color: #F7FBFF; color: #504030; font-family: Courier New; font-size: 12px; -webkit-box-sizing: border-box !important; -moz-box-sizing: border-box !important; box-sizing: border-box !important;}
.site-demo-btn{position: absolute; bottom: 15px; right: 20px;}
.site-demo-zanzhu{position: absolute; bottom: 0; left: 0; width: 100%; height: 90px; text-align: center; background-color: #e2e2e2; overflow: hidden;}
.site-demo-zanzhu>*{position: relative; z-index: 1;}
.site-demo-zanzhu:before{content: ""; position: absolute; z-index: 0; top: 50%; left: 50%; width: 120px; margin: -10px 0px 0px -60px; text-align: center; color: rgb(170, 170, 170); font-size: 18px; font-weight: 300; }

.site-demo-result{position: absolute; right: 0; top: 0; bottom: 0; width: 50%;}
.site-demo-result iframe{position: absolute; width: 100%; height: 100%;}

.site-demo-button{margin-bottom: 30px;}
.site-demo-button div{margin: 20px 30px 10px;}
.site-demo-button .layui-btn+.layui-btn{margin-left: 0;}
.site-demo-button .layui-btn{margin: 0 7px 10px 0; }

.site-demo-text a{color: #01AAED;}


.site-demo-laytpl{text-align: center;}
.site-demo-laytpl textarea,
.site-demo-laytpl div span{width: 40%;  padding: 15px; margin: 0 15px;}
.site-demo-laytpl textarea{height: 300px; border: none; background-color: #3F3F3F; color: #E3CEAB; font-family: Courier New; resize: none;}
.site-demo-laytpl div span{display: inline-block; text-align: center; background: #101010; color: #fff;}
.site-demo-tplres{margin: 10px 0; text-align: center}
.site-demo-tplres .site-demo-tplh2,
.site-demo-tplres .site-demo-tplview{display: inline-block; width: 50%;}
.site-demo-tplres h2{padding: 15px; background: #e2e2e2;}
.site-demo-tplres h3{font-weight: 700;}
.site-demo-tplres div{padding: 14px; border: 1px solid #e2e2e2; text-align: left;}

.site-demo-upload,
.site-demo-upload img{width: 200px; height: 200px; border-radius: 100%;}
.site-demo-upload{position: relative; background: #e2e2e2;}
.site-demo-upload .site-demo-upbar{position: absolute; top: 50%; left: 50%; margin: -18px 0 0 -56px;}
.site-demo-upload .layui-upload-button{background-color: rgba(0,0,0,.2); color: rgba(255,255,255,1);}

.site-demo-util{position: relative; width: 300px;}
.site-demo-util img{width: 300px; border-radius: 100%;}
.site-demo-util span{position: absolute; left: 0; top: 0; width: 100%; height: 100%; background: #333; cursor: pointer;}
@-webkit-keyframes demo-fengjie {
  0% {-webkit-filter: blur(0); opacity: 1; background: #fff; height: 300px; border-radius: 100%;}
  80% {-webkit-filter: blur(50px);  opacity: 0.95;}
  100% {-webkit-filter: blur(20px); opacity: 0; background: #fff;}
}
@keyframes demo-fengjie {
  0% {filter: blur(0); opacity: 1; background: #fff; height: 300px; border-radius: 100%;}
  80% {filter: blur(50px);  opacity: 0.95;}
  100% {filter: blur(20px); opacity: 0; background: #fff;}
}
.site-demo-fengjie{-webkit-animation-name: demo-fengjie; animation-name: demo-fengjie; -webkit-animation-duration: 5s; animation-duration: 5s;}


.layui-layout-manager .site-demo-body{top: 106px;}
.site-demo-title{position: fixed; left: 200px; right: 0; top: 65px;}
.site-demo-code{position: absolute; left: 0; top: 0; width: 100%; height: 100%; border: none; padding: 10px; resize: none; font-size: 12px; background-color: #F7FBFF; color: #881280; font-family: Courier New;}

.site-demo-body .layui-elem-quote a{color: #01AAED;}
.site-demo-body .layui-elem-quote a:hover{color: #FF5722;}


/* 其它 */
#trans-tooltip,
#tip-arrow-bottom,
#tip-arrow-top{display: none !important;}


/* 独立组件 */
.alone{width:730px; margin:200px auto;}
.alone ul{margin-left:1px; font-size:0;}
.alone li{display:inline-block; width:181px; font-size: 16px; text-align:center; line-height:80px; margin:0 1px 1px 0; background-color:#393D49; color:#fff;}
.alone li:hover{opacity:0.8;}
.alone li a{display:block; color:#fff;}


/* 适配多设备 */
@media screen and (max-width: 750px) {
  .layui-main{width: auto; margin: 0 10px;}
  .logo,
  .header-demo .logo{left: 10px;}

  .site-nav-layim{display: none !important;}
  .header .layui-nav .layui-nav-item{margin: 0;}
  .header .layui-nav .layui-nav-item a{padding: 0 20px;}
  .header .layui-nav .layui-nav-item[pc]{display: none;}
  .header .layui-nav .layui-nav-item[mobile]{display: inline-block;}
  .site-banner{height: 300px;}
  .site-banner-bg{background-size: cover;}
  .site-zfj{height: 100px; padding-top: 5px;}
  .site-zfj i{top: 10px; width: 100px; height: 100px; margin-left: -50px; font-size: 100px;}
  .site-desc{background-size: 70%; margin: 0;}
  .site-desc cite{display: none;}
  .site-download{margin-top: 0; }
  .site-download a{height: 40px; line-height: 40px; padding: 0 25px 0 60px; border-radius: 30px; color: #fff; font-size: 16px;}
  .site-download a cite{left: 20px;}
  .site-banner-other{bottom: 15px;}

  .site-idea{margin: 20px 0;}
  .site-idea li{margin: 0 0 20px 0; width: 100%; height: auto; -webkit-box-sizing: border-box !important; -moz-box-sizing: border-box !important; box-sizing: border-box !important;}
  .site-hengfu img{max-width: 100%}

  .layui-layer-dir{display: none;}
  .site-tree{position: fixed; top: 0; bottom: 0; min-height: 0; overflow: auto; z-index: 1000; left: -260px; background-color: #fff;  transition: all .3s; -webkit-transition: all .3s;}
  .site-content{width: 100%; padding: 0; overflow: auto;}
  .site-content img{max-width: 100%;}
  .site-tree-mobile{display: block!important; position: fixed; z-index: 100000; bottom: 15px; left: 15px; width: 50px; height: 50px; line-height: 50px; border-radius: 2px; text-align: center; background-color: rgba(0,0,0,.7); color: #fff;}
  .site-home .site-tree-mobile{display: none!important;}
  .site-mobile .site-tree-mobile{display: none !important;}
  .site-mobile .site-tree{left: 0;}
  .site-mobile .site-mobile-shade{content: ''; position: fixed; top: 0; bottom: 0; left: 0; right: 0; background-color: rgba(0,0,0,.8); z-index: 999;}
  .site-tree-mobile i{font-size: 20px;}
  .layui-code-view{-webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box;}

  .layui-layout-manager .layui-side{position: fixed; top: 0; left: -260px; transition: all .3s; -webkit-transition: all .3s; z-index: 10000;}
  .layui-body{position: static; bottom: 0; left: 0;}
  .site-mobile .layui-side{left: 0;}
  body .layui-layout-manager .footer-demo{position: static;}

  .site-demo-area,
  .site-demo-editor,
  .site-demo-result,
  .site-demo-editor textarea,
  .site-demo-result iframe{position: static; width: 100%;}
  .site-demo-editor textarea{height: 350px;}
  .site-demo-zanzhu{display: none;}
  .site-demo-btn{bottom: auto; top: 370px;}
  .site-demo-result iframe{height: 500px;}

  .site-demo-laytpl textarea, .site-demo-laytpl div span{margin: 0;}
  .site-demo-tplres .site-demo-tplh2, .site-demo-tplres .site-demo-tplview{width: 100%; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box;}

  .site-demo-title{position: static; left: 0;}
  body .layui-layout-manager .site-demo{}
  .site-demo-code{position: static; height: 350px;}
}


