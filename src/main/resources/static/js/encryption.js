/**
 * 通用编码函数，支持 Base64 编码和 URL 编码
 * @param {string} data - 需要编码的字符串
 * @param {string} type - 编码类型，'base64' 或 'url'
 * @returns {string} - 编码后的字符串
 */
 function encrypt(data) {
    // 使用 TextEncoder 将文本转换为 UTF-8 字节流
    const encoder = new TextEncoder();
    const byteArray = encoder.encode(data);

    // 将字节流转换为 Base64 编码
    let binaryString = '';
    for (let i = 0; i < byteArray.length; i++) {
        binaryString += String.fromCharCode(byteArray[i]);
    }
    return btoa(binaryString);
}

/**
 * 通用解码函数，支持 Base64 解码和 URL 解码
 * @param {string} encodedData - 需要解码的编码字符串
 * @param {string} type - 解码类型，'base64' 或 'url'
 * @returns {string} - 解码后的字符串
 */
 function decrypt(encodedData) {
    // 对 Base64 字符串进行填充，确保长度是4的倍数
    const padding = '='.repeat((4 - encodedData.length % 4) % 4);
    const base64WithPadding = encodedData + padding;

    try {
        const decodedBinaryString = atob(base64WithPadding);

        // 将 Base64 解码后的二进制字符串转换为字节流
        const byteArray = new Uint8Array(decodedBinaryString.length);
        for (let i = 0; i < decodedBinaryString.length; i++) {
            byteArray[i] = decodedBinaryString.charCodeAt(i);
        }

        // 使用 TextDecoder 将字节流解码为字符串
        const decoder = new TextDecoder();
        return decoder.decode(byteArray);
    } catch (e) {
        return "Error: " + e.message;
    }
}


