body{
	padding:0px;
	margin:0px;
	text-align:center;
	min-width:1100px;
	overflow-x:hidden;
	font-family:Microsoft Yahei;
	line-height: 1.6;
}
* {
    margin: 0;
    padding: 0;
}
a{
    text-decoration:none;
}
/* 通用头部，包含LOGO、菜单 */
header{
	background-color: #383A35;
	height:105px;
	width:100%;
	color: white;
}
#logo{
	float:left;
	width: 290px;
    text-align: left;
    padding-left: 45px;
    padding-top: 20px;
}
#logo img{
	max-height: 70px;
    float: left;
    padding-right: 20px;
}
#logogramName{
	font-size: 36px;
	white-space: nowrap;
    width: 433px;
	cursor:pointer;
	font-family: serif;
}
nav{
	float:right;
	padding:12px;
	padding-top: 20px;
	padding-bottom: 0px;
	margin-right: 25px;
}
nav a{
	text-decoration:none;
	float:left;
	padding:15px;
	min-width:90px;
	max-width:180px;
	color:white;
	margin: 2px;
}
nav a:hover{
	background-color: gray;
}
nav ul{
	width:100px;
	float:left;
	margin-left: -100px;
	margin-top: 5px;
	list-style-type:none;
}

/* 头部所有页面通用的Banner图 */
#banner{
	background-color: gray;
	width:100%;
	position:relative;
	overflow:hidden;
	height:430px;
}
.banner_img{
	position:absolute;
	top:0;
	height: 100%;
    width: 100%;
}
.banner_img #img_banner{
	height:100%;
	width:100%;
}

/* 中间主体内容，包含公司简介、新闻列表、图片列表等 */
.content{
    height: auto;
    padding-top: 2%;
    padding-bottom: 2%;
    min-width: 960px;
    max-width: 1100px;
    margin: 0 auto;
    text-align: left;
}
/* 关于我们 */
.aboutUs{
	width:58%;
	padding-right:1%;
	padding-left:1%;
	float:left;
	height:300px;
	overflow:hidden;
}
.newsList{
	width:38%;
	float:left;
	height:300px;
	overflow:hidden;
	padding-right:1%;
	padding-left:1%;
}
.newsList .siteColumnContent{
	line-height: 23px;
}
.newsList .siteColumnContent a{
	text-decoration:none;
	float:left;
	padding:1%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	width:98%;
    color: rgb(102, 102, 102);
}
.imageList{
	width:98%;
	overflow:hidden;
	padding-right:1%;
	padding-left:1%;
	padding-top: 5px;
}
.imageList .siteColumnContent a{
    float: left;
    width: 24%;
    padding: 0.5%;
    text-align: center;
}
.imageList .siteColumnContent a img{
	width: 216px;
    height: 216px;
    border-radius: 10px;
}
.imageList .siteColumnContent a div{
	width: 100%;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: rgb(170, 170, 170);
}

/*首页通用栏目显示模块*/
.title{
	height: 20px;
	border-color: transparent transparent rgb(238, 238, 238);
	border-width: 0px 0px 1px;
	border-style: solid;
	padding: 10px;
}
/*栏目模块的标题区域*/
.title .siteColumnName{
	font-size: 20px;
    color: rgb(51, 51, 51);
}
.title .more{
	float:right;
	padding-right: 5px;
	font-family: Microsoft YaHei;
	color: rgb(170, 170, 170);
	margin-top: 3px;
}
/*栏目模块的内容区域*/
.siteColumnContent{
	padding-top:10px;
	color: rgb(102, 102, 102);
}

/*首页，关于我们里面的图片*/
.aboutUsImg{
	width: 30%;
    float: left;
    padding: 9px;
}
/*首页的各个栏目页面的栏目名字*/
.indexBody div .title .siteColumnName{
	font-size:18px;
}

/* 通用底部 */
footer{
	background-color: black;
    height: 256px;
    width: 100%;
    color: white;
}
/*微信二维码*/
footer .qrImg{
	float: left;
    height: 180px;
    margin-left: 5%;
    margin-top: 25px;
}
/*微信二维码的图*/
#qrImage{
    width: 150px;
    height: 150px;
}
/*底部站点相关信息*/
#footerSiteInfo{
    float: left;
    margin-left: 6%;
    margin-top:23px;
	line-height: 31px;
}
/*底部的栏目导航*/
#footerColumnList{
	display:none;
}
/*单位名或者个人的名字*/
#footerSiteInfo h3{
	text-align: left;
	margin: 0px;
}
#footerSiteInfo div {
	text-align:left;
}
#footerSiteInfo div span{
	font-family: Georgia, "Times New Roman", Times, serif;
	font-size: 16px;
	color: #ff0;
	font-style: oblique;
}
/*底部网址，避免因为网址太长导致换行*/
#footerSiteInfo .footUrl {
    white-space: nowrap;
}
/*版权区块*/
#footerCopyRight{
	float: left;
	margin-left: 60px;
	margin-top: 17px;
	width: 100%;
}
/*联系方式跟分享的区块*/
.footerPhoneAndShare{
	padding-top: 50px;
    padding-left: 100px;
}
.footerPhoneAndShare .footerPhone{
	padding-right: 100px;
    text-align: right;
}
.footerPhoneAndShare .footerPhone span{
	font-family: Georgia, "Times New Roman", Times, serif;
	font-size: 36px;
	color: #ff0;
	font-style: oblique;
	padding-top: 10px;
}
.footerPhoneAndShare .footerShare{
	float: right;
    margin-top: -136px;
    text-align: right;
	width: 380px;
}
.footerPhoneAndShare .footerShare .fenxiangdao{
	float: left;
	padding-top: 5px;
}

/*内页相关样式*/
/*正文内容*/
.pageText{
	padding-top:20px;
	line-height: 30px;
	min-height: 330px;
}
/*内页的标题栏*/
.insidePagesTitle{
	border-style: ridge;
	border-width: thick;
	padding: 18px;
}
.insidePagesTitle div{
	float: right;
    font-family: Microsoft YaHei;
    color: rgb(170, 170, 170);
    margin-top: -25px;
}
.insidePagesTitle div a{
	padding-left:15px;
	padding-right:5px;
	color: rgb(170, 170, 170);
}

/*列表页分页模块*/
.page{
	margin-top:5px;
    padding-bottom: 62px;
}
.page ul{
	list-style: none;
}
/* 分页的li总体控制。设置的display都是隐藏，通过js来动态控制其是否显示 */
.page ul li{
	float:left;
	border: 1px solid #808080;
	margin:5px;
	text-align:center;
	font-size: 16px;
	border-radius: 2px;
	padding: 4px;
	padding-left: 10px;
    padding-right: 10px;
}
.page ul li a{
	text-decoration: none;
	color:#808080;
}
/* 共有多少页 */
#page_allNum{
	/*width:120px;*/
	padding:4px 10px;
	border:0;
	color:#808080;
}
/* 首页，在列表页js控制，若没有上一页的内容，则首页根上一页都不显示。下一页、尾页也同理 */
#page_first{
	display:none;
}
/* 上一页 */
#page_up{
	display:none;
}
/* 比如当前在第5页，会有3、4、6、7页的链接，点击后到相应的页面 */
.page .page_pageList{
}
/* 当前第几页，数字 */
.page .page_currentPageNum{
	background-color: rgba(99, 86, 86, 0.13);;
}
/* 下一页 */
#page_next{
	display:none;
}
/* 尾页 */
#page_last{
	display:none;
}

/*新闻列表页面，新闻列表数据*/
.newsListPage .newsItem{
    border-color: transparent transparent rgb(238, 238, 238);
    border-width: 0px 0px 1px;
    border-style: dashed;
    padding: 10px;
}
.newsListPage .newsItem .newsItemName{
    font-weight: 700;
}
.newsListPage .newsItem .newsItemName a{
    color: black;
    width:100%;
    overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.newsListPage .newsItem .newsItemIntro a{
	color: black;
}
/*新闻列表，删除新闻按钮*/
.deleteNews{
	display:none;
}

/*图片列表页面，图片列表数据*/
.imageListPage{
	padding-top:22px;
}
.imageListPage .imagesItem{
	float:left;
	padding:1%;
	width:23%
}
.imageListPage .imagesItem .newsItemTitlePic img{
	width:220px;
	height:220px;
	border-radius:5px;
}
.imageListPage .imagesItem .newsItemName{
	text-align:center;
	width:95%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.imageListPage .imagesItem .newsItemTime{
	display:none;
}
.imageListPage .imagesItem .newsItemIntro{
	display:none;
}
/*图片列表，删除图片按钮*/
.deleteImages{
	display:none;
}

/*编辑区，正常状态下是隐藏的*/
#editPanel{
	display:none;
}