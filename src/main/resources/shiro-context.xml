<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
				http://www.springframework.org/schema/context
				 http://www.springframework.org/schema/context/spring-context-3.2.xsd
				http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.2.xsd
				 http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

    <!-- Shiro -->
    <bean id="shiroFilter" class="org.apache.shiro.spring.web.ShiroFilterFactoryBean">
        <property name="securityManager" ref="securityManager"/>
        <!-- 手机＋动态验证码登录为：phoneVerifyLogin.naii    用户名、邮箱、id＋密码登录为：login.naii -->
        <property name="loginUrl" value="/login.naii"/>
        <!-- 认证成功统一跳转到/user/info.naii ，建议不配置，shiro认证成功自动到上一个请求路径 -->
        <!-- <property name="successUrl" value="/user/info.naii"/> -->
        <property name="unauthorizedUrl" value="/index.jsp"/>
        <property name="filterChainDefinitions">
            <!-- 首页、根目录不做登录拦截 -->
            <value>
                /=anon
                /index.jsp = anon
                /cache/** = anon
                /*.jsp = anon
                /*.naii = anon
                /install/*.naii = anon
                /*.html = anon
                /style/** = anon
                /upload/** = anon
                /ueditor/** = anon
                /reg*.naii = anon
                /bbs/list.naii = anon
                /bbs/view.naii = anon
                /*.* = anon
                /** = authc
            </value>
        </property>
    </bean>

    <!-- securityManager安全管理器 -->
    <bean id="securityManager" class="org.apache.shiro.web.mgt.DefaultWebSecurityManager">
        <property name="realm" ref="customRealm"/>
        <!-- 注入缓存管理器 -->
        <property name="cacheManager" ref="cacheManager"/>
        <!-- 注入session管理器 -->
        <property name="sessionManager" ref="sessionManager"/>
        <!-- 记住我 -->
        <property name="rememberMeManager" ref="rememberMeManager"/>
    </bean>

    <!-- 手机＋发送的动态验证码登录，配置项systemConfig.xml sms节点 -->
    <bean id="customRealm" class="cn.edu.sjtu.gateway.vm.shiro.CustomRealm">
        <!-- 将凭证匹配器设置到realm中，realm按照凭证匹配器的要求进行散列 -->
        <property name="credentialsMatcher" ref="credentialsMatcher"/>
    </bean>

    <!-- 凭证匹配器 -->
    <bean id="credentialsMatcher"
          class="org.apache.shiro.authc.credential.HashedCredentialsMatcher">
        <property name="hashAlgorithmName" value="md5"/>
        <property name="hashIterations" value="2"/>
    </bean>

    <!-- 缓存管理器 -->
    <bean id="cacheManager" class="org.apache.shiro.cache.ehcache.EhCacheManager">
        <property name="cacheManagerConfigFile" value="classpath:shiro-ehcache.xml"/>
    </bean>

    <!-- 会话管理器 -->
    <bean id="sessionManager" class="org.apache.shiro.web.session.mgt.DefaultWebSessionManager">
        <!-- session的失效时长，单位毫秒，100分钟 -->
        <property name="globalSessionTimeout" value="6000000"/>
        <!-- 删除失效的session -->
        <property name="deleteInvalidSessions" value="true"/>
        <property name="sessionDAO" ref="sessionDAO"/>
    </bean>
    <bean id="sessionDAO" class="org.apache.shiro.session.mgt.eis.MemorySessionDAO">
    </bean>

    <!-- rememberMeManager管理器，写cookie，取出cookie生成用户信息 -->
    <bean id="rememberMeManager" class="org.apache.shiro.web.mgt.CookieRememberMeManager">
        <property name="cookie" ref="rememberMeCookie"/>
    </bean>
    <!-- 记住我cookie -->
    <bean id="rememberMeCookie" class="org.apache.shiro.web.servlet.SimpleCookie">
        <!-- rememberMe是cookie的名字 -->
        <constructor-arg value="rememberMe"/>
        <!-- 记住我cookie生效时间30天 -->
        <property name="maxAge" value="2592000"/>
    </bean>

</beans>
