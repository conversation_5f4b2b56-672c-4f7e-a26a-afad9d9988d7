<ehcache updateCheck="false" name="shiroCache">
    <!-- <PERSON><PERSON>ache配置文件 - 已优化，调整缓存大小和过期时间 -->

    <!-- 默认缓存配置 - 1小时过期 -->
    <defaultCache
            maxElementsInMemory="50000"
            eternal="false"
            timeToIdleSeconds="0"
            timeToLiveSeconds="3600"
            overflowToDisk="false"
            diskPersistent="false"
            diskExpiryThreadIntervalSeconds="120"
    />

    <!-- 用户认证缓存 - 5分钟空闲过期，30分钟绝对过期 -->
    <cache name="authenticationCache"
           maxElementsInMemory="3000"
           eternal="false"
           timeToIdleSeconds="300"
           timeToLiveSeconds="1800"
           overflowToDisk="false"
           diskPersistent="false"
    />

    <!-- 用户授权缓存 - 5分钟空闲过期，30分钟绝对过期 -->
    <cache name="authorizationCache"
           maxElementsInMemory="3000"
           eternal="false"
           timeToIdleSeconds="300"
           timeToLiveSeconds="1800"
           overflowToDisk="false"
           diskPersistent="false"
    />

    <!-- 用户会话缓存 - 1小时绝对过期，支持磁盘溢出 -->
    <cache name="sessionCache"
           maxElementsInMemory="5000"
           eternal="false"
           timeToIdleSeconds="0"
           timeToLiveSeconds="3600"
           overflowToDisk="true"
           diskPersistent="false"
    />
</ehcache>
