<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<!-- 系统配置文件 - 已优化，移除无效的AOP和拦截器引用 -->

	<!-- 文件上传配置 -->
	<attachmentFile>
		<!-- 允许上传的文件后缀列表，多个以|分割 -->
		<allowUploadSuffix>png|jpg|jpeg|gif|bmp|flv|swf|mkv|avi|rm|rmvb|mpeg|mpg|ogg|ogv|mov|wmv|mp4|webm|mp3|wav|mid|rar|zip|tar|gz|7z|bz2|cab|iso|doc|docx|xls|xlsx|ppt|pptx|pdf|txt|md|xml</allowUploadSuffix>
	</attachmentFile>

	<!-- 执行时间检测配置 -->
	<ExecuteTime>
		<!-- Controller执行时间检测 - 注意：引用的SystemInterceptor类不存在，此配置暂时保留但不会生效 -->
		<controller>
			<used>false</used>
		</controller>

		<!-- Service、Dao执行时间检测 - 注意：引用的AOP类不存在，此配置暂时保留但不会生效 -->
		<serviceDao>
			<used>false</used>
		</serviceDao>

		<!-- 执行时间阈值（毫秒），超过此时间才会输出到控制台 -->
		<recordTime>1000</recordTime>
	</ExecuteTime>

	<!-- 项目启动后的自动检测配置 -->
	<startAutoCheck>
		<!-- 数据库自动检测 - 必须开启，否则系统无法正常运行 -->
		<db>true</db>
		<!-- OSS自动检测 - 用于文件上传存储 -->
		<oss>true</oss>
	</startAutoCheck>
</configuration>