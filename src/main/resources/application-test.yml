api:
  suffix: .json

database:
  # 数据库所在ip地址，连接的ip，如 *************
  ip: ${NAII_DATABASES_IP:*************}
  # 数据库的名字，数据库名
  name: ${NAII_DATABASES_NAME:naii}

# 日志配置 - 测试环境
logging:
  level:
    cn:
      edu:
        sjtu: ${NAII_LOGGING_SJTU_LEVEL:info}
    root: ${NAII_LOGGING_LEVEL:warn}
    sql: ${NAII_LOGGING_SQL_LEVEL:warn}
    web: ${NAII_LOGGING_WEB_LEVEL:warn}
    org:
      springframework: warn
      hibernate: warn

# 服务器配置 - 测试环境
server:
  max-http-header-size: ${NAII_HEADER_SIZE:10MB}
  port: ${NAII_SERVER_PORT:8080}
  tomcat:
    max-http-form-post-size: ${NAII_POST_SIZE:10MB}
    connection-timeout: ${NAII_CONNECTION_TIMEOUT:60000}
    # 测试环境中等连接数配置
    max-connections: 500
    threads:
      max: 150
      min-spare: 20
  # JSP支持配置 - JAR包模式
  jsp-servlet:
    init-parameters:
      development: true
      compilerSourceVM: 17
      compilerTargetVM: 17
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      # 测试环境连接池配置 - 中等规模配置
      maximum-pool-size: 15
      minimum-idle: 8
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
      # 连接池名称，便于监控
      pool-name: NaiiTestHikariCP
    # MySQL 数据库配置 - 测试环境
    url: jdbc:mysql://${database.ip}:3307/${database.name}?useUnicode=true&characterEncoding=utf-8&useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai&rewriteBatchedStatements=true
    # 数据库连接的登录账号
    username: ${NAII_DATABASES_USERNAME:root}
    # 数据库连接的登录密码
    password: ${NAII_DATABASES_PASSWORD:Passw0rd@_}
  jpa:
    properties:
      hibernate:
        # 明确指定MySQL方言，解决DialectResolutionInfo错误
        dialect: org.hibernate.dialect.MySQL8Dialect
        hbm2ddl:
          # 使用update模式，允许Hibernate自动修正表结构差异
          auto: update
        # 测试环境适度的SQL优化
        format_sql: false
        use_sql_comments: false
        # 暂时关闭二级缓存，避免依赖问题
        cache:
          use_second_level_cache: false
          use_query_cache: false
    # 测试环境不显示SQL
    show-sql: false
  main:
    # 允许循环依赖 - Spring Boot 2.6+ 需要
    allow-circular-references: true
  mvc:
    # 测试环境静态资源配置 - 移除view配置避免与Java配置冲突
    static-path-pattern: /static/**
  servlet:
    multipart:
      # 文件上传限制 - 测试环境配置
      max-file-size: ${NAII_FILE_SIZE:200MB}
      max-request-size: ${NAII_REQUEST_SIZE:200MB}
      # 文件大小阈值，超过此大小将写入磁盘
      file-size-threshold: 2MB
      # 临时文件存储位置
      location: ${java.io.tmpdir}/naii-upload


url:
  # 访问的后缀名
  suffix: .naii

# 文件上传配置 - 测试环境
fileupload:
  allowUploadSuffix: png|jpg|jpeg|gif|bmp|flv|swf|mkv|avi|rm|rmvb|mpeg|mpg|ogg|ogv|mov|wmv|mp4|webm|mp3|wav|mid|rar|zip|tar|gz|7z|bz2|cab|iso|doc|docx|xls|xlsx|ppt|pptx|pdf|txt|md|xml
  domain: http://*************:8080/
  maxSize: 200MB
  storage:
    local:
      # 统一文件上传路径配置
      path: ./uploads/
