api:
  suffix: .json

database:
  # 数据库所在ip地址，连接的ip，如 127.0.0.1
  ip: ${NAII_DATABASES_IP:127.0.0.1}
  # 数据库的名字，数据库名
  name: ${NAII_DATABASES_NAME:naii}

# 日志配置 - 开发环境详细日志
logging:
  level:
    cn:
      edu:
        sjtu: ${NAII_LOGGING_SJTU_LEVEL:debug}
    root: ${NAII_LOGGING_LEVEL:info}
    sql: ${NAII_LOGGING_SQL_LEVEL:debug}
    web: ${NAII_LOGGING_WEB_LEVEL:debug}
    org:
      springframework:
        web: debug
      hibernate:
        SQL: debug
        type:
          descriptor:
            sql:
              BasicBinder: trace

# 服务器配置 - 开发环境
server:
  max-http-header-size: ${NAII_HEADER_SIZE:10MB}
  port: ${NAII_SERVER_PORT:8080}
  tomcat:
    max-http-form-post-size: ${NAII_POST_SIZE:10MB}
    connection-timeout: ${NAII_CONNECTION_TIMEOUT:60000}
    # 开发环境适中的连接数配置
    max-connections: 200
    threads:
      max: 200
      min-spare: 10
  # JSP支持配置 - JAR包模式
  jsp-servlet:
    init-parameters:
      development: true
      compilerSourceVM: 17
      compilerTargetVM: 17
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      # 开发环境连接池配置 - 适中配置便于调试
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
      # 连接池名称，便于监控
      pool-name: NaiiDevHikariCP
    # MySQL 数据库配置 - 开发环境优化
    url: jdbc:mysql://${database.ip}:3306/${database.name}?useUnicode=true&characterEncoding=utf-8&useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai&rewriteBatchedStatements=true
    # 数据库连接的登录账号
    username: ${NAII_DATABASES_USERNAME:root}
    # 数据库连接的登录密码
    password: ${NAII_DATABASES_PASSWORD:Passw0rd@_}
  jpa:
    properties:
      hibernate:
        # 明确指定MySQL方言，解决DialectResolutionInfo错误
        dialect: org.hibernate.dialect.MySQL8Dialect
        hbm2ddl:
          auto: update
        # 开发环境SQL格式化和统计
        format_sql: true
        use_sql_comments: true
        generate_statistics: true
        # 二级缓存配置
        cache:
          use_second_level_cache: false
          use_query_cache: false
    # 开发环境显示SQL便于调试
    show-sql: true
  main:
    # 允许循环依赖 - Spring Boot 2.6+ 需要
    allow-circular-references: true
    # 开发环境允许Bean覆盖
    allow-bean-definition-overriding: true
  mvc:
    # 开发环境静态资源配置 - 移除view配置避免与Java配置冲突
    static-path-pattern: /static/**
  servlet:
    multipart:
      # 文件上传限制 - 开发环境适中配置
      max-file-size: ${NAII_FILE_SIZE:100MB}
      max-request-size: ${NAII_REQUEST_SIZE:100MB}
      # 文件大小阈值，超过此大小将写入磁盘
      file-size-threshold: 2MB
      # 临时文件存储位置
      location: ${java.io.tmpdir}/naii-upload


url:
  # 访问的后缀名
  suffix: .naii

# 文件上传配置 - 开发环境
fileupload:
  allowUploadSuffix: png|jpg|jpeg|gif|bmp|flv|swf|mkv|avi|rm|rmvb|mpeg|mpg|ogg|ogv|mov|wmv|mp4|webm|mp3|wav|mid|rar|zip|tar|gz|7z|bz2|cab|iso|doc|docx|xls|xlsx|ppt|pptx|pdf|txt|md|xml
  domain: http://***********:8080/
  maxSize: 100MB
  storage:
    local:
      # 使用相对路径，避免硬编码Windows路径
      path: ./uploads/

# VM配置 - 修复配置键名不一致问题
vm:
  showsql: true
# 兼容旧配置
wm:
  showsql: true
# NAII 自定义配置 - 开发环境
naii:
  request:
    frequency:
      # 拦截过滤的请求后缀名，用这个后缀的请求，都是在被拦截保护的请求
      suffix: jsp,naii,json,html
      url:
        # 开发环境不限制URL访问频率，便于调试
        delay: 0
      ip:
        # 开发环境放宽IP请求限制
        requestNumber: 100
        delay: 1000
        # 触发禁止访问保护的持续时间（毫秒）- 开发环境缩短为5分钟
      forbidTime: 300000

# 开发环境性能监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env,configprops,beans
  endpoint:
    health:
      show-details: always
