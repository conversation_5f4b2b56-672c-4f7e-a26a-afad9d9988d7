<%@page import="cn.edu.sjtu.gateway.manager.G" %>
<%@page import="cn.edu.sjtu.gateway.vm.Global" %>
<%@ page import="cn.edu.sjtu.gateway.vm.util.SystemUtil" %>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://www.naii.edu.cn/java_naii/naii_tld" prefix="x" %>
<jsp:include page="../wm/common/head.jsp">
    <jsp:param name="title" value="代理欢迎页面"/>
</jsp:include>
<script src="/<%=Global.CACHE_FILE %>Role_role.js"></script>
<script src="<%=SystemUtil.get("STATIC_RESOURCE_PATH") %>js/fun.js"></script>
<script src="<%=SystemUtil.get("STATIC_RESOURCE_PATH") %>js/manager/commonedit.js?v=<%=Global.VERSION %>"
        type="text/javascript"></script>


<style type="text/css">
    .iw_table tbody tr .iw_table_td_view_name {
        width: 50%;
        padding-left: 25%;
    }
</style>

<div style="text-align:center; font-size:29px; padding-top:35px; padding-bottom: 10px;">
    欢迎使用 <%=Global.get("SITE_NAME") %>云建站系统
</div>


<div class="layui-tab" id="gonggao" style="display:none; margin-left: 30px; margin-right: 30px;">
    <ul class="layui-tab-title">
        <li class="layui-this">公告信息</li>
        <li>联系</li>
    </ul>
    <div class="layui-tab-content" style="font-size:14px;">
        <div class="layui-tab-item layui-show" id="parentAgencyNotice">${parentAgencyNotice }</div>
        <div class="layui-tab-item">
            名称：${parentAgency.name }<br/>
            电话：${parentAgency.phone }<br/>
            QQ：${parentAgency.qq }<br/>
            地址：${parentAgency.address }
        </div>
    </div>
</div>
<script>
    //注意：选项卡 依赖 element 模块，否则无法进行功能性操作
    layui.use('element', function () {
        var element = layui.element;
    });
    try {
        document.getElementById('parentAgencyNotice').innerHTML = document.getElementById('parentAgencyNotice').innerHTML.replace(/\n/g, "<br/>");
    } catch (e) {
    }
    try {
        if (document.getElementById('parentAgencyNotice').innerHTML.length > 1) {
            document.getElementById('gonggao').style.display = '';
        }
    } catch (e) {
    }
</script>

<table class="layui-table iw_table" lay-even lay-skin="nob" style="margin:3%; width:94%;">
    <tbody>
    <tr>
        <td class="iw_table_td_view_name">单位名称</td>
        <td class="ignore">${agency.name }</td>
    </tr>
    <tr>
        <td class="iw_table_td_view_name">到期时间</td>
        <td>
            <span class="ignore"><x:time linuxTime="${agency.expiretime }"/></span>
        </td>
    </tr>
    <tr>
        <td class="iw_table_td_view_name">权限</td>
        <td>
            <script type="text/javascript">writeName('${user.authority }');</script>
        </td>
    </tr>
    <tr>
        <td class="iw_table_td_view_name">最后登录</td>
        <td class="ignore"><x:time linuxTime="${user.lasttime }"/></td>
    </tr>
    <tr>
        <td class="iw_table_td_view_name">最后登录ip</td>
        <td class="ignore">${user.lastip }</td>
    </tr>
    </tbody>
</table>


<script type="text/javascript">



    //服务于上级代理显示的窗口
    function getTr(name, value) {
        if (typeof (value) == 'undefined' || value == null || value.length == 0) {
            //忽略
            return "";
        } else {
            return '<tr><td style="width:45px;">' + name + '</td><td>' + value + '</td></tr>';
        }
    }

    //弹出其上级代理的信息
    function jumpParentAgency() {
        content = '<table class="layui-table" style="margin:0px;"><tbody>'
            + getTr('名称', '${parentAgency.name}')
            + getTr('QQ', '${parentAgency.qq}')
            + getTr('手机', '${parentAgency.phone}')
            + getTr('地址', '${parentAgency.address}')
            + '</tbody></table>';

        layer.open({
            type: 1
            , title: '我的上级信息'
            , content: content
            , shade: false
            , resize: false
        });
    }
</script>
<jsp:include page="../wm/common/foot.jsp"/>
<style type="text/css">{
    display: block
;
} </style>