<%@page import="cn.edu.sjtu.gateway.vm.util.SystemUtil"%>
<%@page import="cn.edu.sjtu.gateway.vm.Global"%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://www.naii.edu.cn/java_naii/naii_tld" prefix="x" %>
<jsp:include page="../wm/common/head.jsp">
	<jsp:param name="title" value="代理后台"/>
</jsp:include>
<script src="<%=SystemUtil.get("STATIC_RESOURCE_PATH") %>js/fun.js"></script>
<script>
var masterSiteUrl = '<%=SystemUtil.get("MASTER_SITE_URL") %>';
</script>
<script src="<%=SystemUtil.get("STATIC_RESOURCE_PATH") %>js/manager/commonedit.js?v=<%=Global.VERSION %>"></script>

<style type="text/css">
body{margin: 0;padding: 0px;height: 100%;overflow: hidden;}
#editPanel{
	position: absolute;
	top: 0px;
	width:150px;
}
#editPanel span{
	width:100%;
}

.menu{
	width:150px;
	height:100%;
	background-color: #393D49;
	position: absolute;
}
.menu ul li{
	cursor: pointer;
}

/*左侧的一级菜单的图标*/
.firstMenuIcon{
	font-size:16px;
	padding-right:8px;
	font-weight: 700;
}
/*左侧的一级菜单的文字描述*/
.firstMenuFont{
	
}

/* 二级菜单 */
.menu .layui-nav-item .layui-nav-child .subMenuItem{
	padding-left:48px;
	font-size: 13px;
}
</style>

<div id="leftMenu" class="layui-nav layui-nav-tree layui-nav-side menu">
	<ul class="">
		
		<li class="layui-nav-item" id="agencyNotice">
			<a href="javascript:loadUrl('/agency/systemSet.naii');">
				<i class="layui-icon firstMenuIcon">&#xe620;</i>
				<span class="firstMenuFont">系统设置</span>
			</a>
		</li>
		<li class="layui-nav-item" id="sitemanage">
			<a href="javascript:loadUrl('/agency/userList.naii');">
				<i class="layui-icon firstMenuIcon">&#xe857;</i>
				<span class="firstMenuFont">网站管理</span>
			</a>
		</li>
		
		<li class="layui-nav-item" id="subagency">
			<a href="javascript:loadUrl('/agency/subAgencyList.naii?orderBy=expiretime_ASC');">
				<i class="layui-icon firstMenuIcon">&#xe612;</i>
				<span class="firstMenuFont">下级代理</span>
			</a>
		</li>
		<li class="layui-nav-item">
			<a href="javascript:updatePassword();" id="xiugaimima">
				<i class="layui-icon firstMenuIcon">&#xe642;</i>
				<span class="firstMenuFont">更改密码</span>
			</a>
		</li>
		<li class="layui-nav-item" id="plugin" style="display:none;">
			<a href="javascript:;">
				<i class="layui-icon firstMenuIcon">&#xe857;</i>
				<span class="firstMenuFont">功能插件2</span>
			</a>
			<dl class="layui-nav-child" id="plugin_submenu">${pluginMenu }</dl>
		</li>
		<script>
			if(document.getElementById('plugin_submenu').innerHTML.length > 5){
				document.getElementById('plugin').style.display = '';
			}
		</script>
		
		<div id="menuAppend" style="margin-left: 3px;">
			<!-- 插件扩展菜单项。追加的值如： -->
			<!-- <li class="layui-nav-item" >
				<a href="/user/logout.naii">
					<i class="layui-icon firstMenuIcon">&#xe633;</i>
					<span class="firstMenuFont">退出登录</span>
				</a>
			</li>
			 -->
		</div>
		

		<li class="layui-nav-item">
			<a href="<c:url value="/user/logout.naii"/>">
				<i class="layui-icon firstMenuIcon">&#xe633;</i>
				<span class="firstMenuFont">退出登录</span>
			</a>
		</li>
	</ul>
</div>


<div id="content" style="width: 100%;height:100%;position: absolute;left: 150px;word-wrap: break-word;border-right: 150px;box-sizing: border-box; border-right-style: dotted;">
	<iframe name="iframe" id="iframe" frameborder="0" style="width:100%;height:100%;box-sizing: border-box;"></iframe>
</div>

<script>
layui.use('element', function(){
	var element = layui.element;
});

/**
 * 在主体内容区域iframe中加载制定的页面
 * url 要加载的页面的url
 */
function loadUrl(url){
	document.getElementById("iframe").src=url;
}

//加载登录后的默认页面
loadUrl('welcome.naii');
//向扩展菜单的div中，加入html。也就是往里再增加别的菜单。 appendHtml要追加的html，这里一般都是追加li
function menuAppend(appendHtml){
	document.getElementById("menuAppend").innerHTML = document.getElementById("menuAppend").innerHTML + appendHtml; 
}

</script>


${pluginAppendHtml}
<jsp:include page="../wm/common/foot.jsp"/>