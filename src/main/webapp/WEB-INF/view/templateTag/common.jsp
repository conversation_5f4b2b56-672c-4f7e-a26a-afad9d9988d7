<%@page import="cn.edu.sjtu.gateway.vm.util.SystemUtil"%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>通用标签_动态标签_网市场模版标签</title>
	<link href="<%=SystemUtil.get("STATIC_RESOURCE_PATH") %>module/editor/css/editormd.css" rel="stylesheet">
</head>
<body style="">
	<div class="layui-main site-inline doc1dakuang" style="">
		<div class="site-content markdown-body editormd-html-preview" id="content" style="box-sizing: border-box;">
			<h1 id="iw_title" style="">通用标签</h1>
			<h2 id="h2-u9002u7528u8303u56F4">
				<a name="适用范围" class="reference-link"></a>
				<span class="header-link octicon octicon-link"></span>适用范围
			</h2>
			<table>
				<thead>
					<tr>
						<th>功能模块</th>
						<th>分类</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td><a href="/templateTag/home.naii">模板页面</a></td>
						<td>√ <a href="/templateTag/home.naii">首页</a></td>
						<td>√ <a href="/templateTag/list.naii">列表页</a></td>
						<td>√<a href="/templateTag/details.naii">详情页</a></td>
					</tr>
					<tr>
						<td>√ <a href="/templateTag/var.naii">模板变量</a></td>
					</tr>
				</tbody>
			</table>
			<h2 id="h2-u6807u7B7Eu5217u8868">
				<a name="标签列表" class="reference-link"></a>
				<span class="header-link octicon octicon-link"></span>
				标签列表
			</h2>
				
			<table>
				<thead>
					<tr>
						<th>标签</th>
						<th>说明</th>
						<th>类型</th>
						<th>调出值（示例）</th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td class="ignore">{site.name}</td>
						<td>网站名称</td>
						<td>字符串</td>
						<td>${site.name}</td>
					</tr>
					<tr>
						<td class="ignore">{site.id}</td>
						<td>网站编号</td>
						<td>整数</td>
						<td>${site.id}</td>
					</tr>
					<tr>
						<td class="ignore">{site.domain}</td>
						<td>网站分配的免费二级域名前缀</td>
						<td>字符串</td>
						<td>${site.domain}</td>
					</tr>
					<tr>
						<td class="ignore">{site.username}</td>
						<td>网站的联系人姓名</td>
						<td>字符串</td>
						<td>${site.username}</td>
					</tr>
					<tr>
						<td class="ignore">{site.phone}</td>
						<td>网站的联系人手机</td>
						<td>数字</td>
						<td>${site.phone}</td>
					</tr>
					<tr>
						<td class="ignore">{site.qq}</td>
						<td>网站的联系人QQ</td>
						<td>数字</td>
						<td>${site.qq}</td>
					</tr>
					<tr>
						<td class="ignore">{site.address}</td>
						<td>当前网站的联系人办公地址</td>
						<td>字符串</td>
						<td>${site.address}</td>
					</tr>
					<tr>
						<td class="ignore">{site.companyName}</td>
						<td>当前网站的单位名</td>
						<td>字符串</td>
						<c:if test="${site.companyName == ''}">
							<td>例:阿里巴巴网络技术有限单位</td>
						</c:if>
						<c:if test="${site.companyName != ''}">
							<td>${site.companyName}</td>
						</c:if>
					</tr>
					<tr>
						<td class="ignore">{linuxTime}</td>
						<td>当前10位Unix时间戳</td>
						<td>整数</td>
						<td class="ignore">${linuxTime}</td>
					</tr>
					<tr>
						<td class="ignore">index.html</td>
						<td>网站首页</td>
						<td class="ignore">URL</td>
						<td><span class="ignore">index.html </span> (固定，只要是本系统做的网站，首页一定是这个 )</td>
					</tr>
					<tr>
						<td class="ignore">{masterSiteUrl}</td>
						<td>调取你主站(管理后台)的域名</td>
						<td class="ignore">URL</td>
						<td class="ignore"><a href="${masterSiteUrl}">${masterSiteUrl}</a></td>
					</tr>
					<tr>
						<td class="ignore">{templatePath}</td>
						<td>调取模版资源文件的url前缀，开发模版使用。<a href="/templateTag/resource.naii">详细点此查看</a></td>
						<td class="ignore">URL</td>
						<td class="ignore"><a href="${templatePath }">${templatePath }</a></td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>

<jsp:include page="../common/translate.jsp"/>