<%@page import="cn.edu.sjtu.gateway.vm.util.SafetyUtil" %>
<%@ page import="cn.edu.sjtu.gateway.vm.util.SystemUtil" %>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://www.naii.edu.cn/java_naii/naii_tld" prefix="x" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%
    // 获取用户输入并安全过滤
    String username = SafetyUtil.filter(request.getParameter("username"));
    username = (username == null || "null".equalsIgnoreCase(username)) ? "" : username;

    String password = SafetyUtil.filter(request.getParameter("password"));
    password = (password == null || "null".equalsIgnoreCase(password)) ? "" : password;
    // 静态资源路径
    String staticPath = SystemUtil.get("STATIC_RESOURCE_PATH");
%>
<jsp:include page="../common/head.jsp">
    <jsp:param name="title" value="登录"/>
</jsp:include>
<!-- Meta tag Keywords -->
<meta name="viewport" content="width=device-width, initial-scale=1">

<link rel="stylesheet" href="<%=SystemUtil.get("STATIC_RESOURCE_PATH") %>plugin/login/css/style.css" type="text/css"
      media="all"/>
<link rel="stylesheet" href="<%=SystemUtil.get("STATIC_RESOURCE_PATH") %>plugin/login/css/font-awesome.min.css"
      type="text/css" media="all">

<style type="text/css">
    .content-wthree input {
        background: #FFFFFF !important;
        z-index: -100;
    }

    .codeBox {
        background-color: #FFFFFF;
        overflow: hidden;
        display: flex;
        align-items: center;
    }
</style>

<div id="block" class="w3lvide-content"
     style="background-image: url('<%=SystemUtil.get("STATIC_RESOURCE_PATH") %>naii/images/image.jpg') !important; background-size: 100% auto;background-color: rgba(13, 18, 28, 0.50);background-blend-mode: multiply; ; background-position: center center; background-repeat: no-repeat;">
    <div class="workinghny-form-grid" style="width: 100%">
        <div class="main-hotair">
            <img src="<%=SystemUtil.get("STATIC_RESOURCE_PATH") %>naii/images/title.png"
                 style="width:300px;height: auto" alt="">
            <div class="content-wthree" style="margin: 30px 50px">
                <form>
                    <label>
                        <input type="text" class="text" name="username" value="<%=username %>"
                               placeholder="请输入用户名" required lay-verify="required" autocomplete="off">
                    </label>
                    <label for="password"><input type="password" id="password" class="password" name="password"
                                                 value="<%=password %>"
                                                 placeholder="请输入密码" required lay-verify="required"
                                                 autocomplete="off"></label>
                    <div style="position: relative">
                        <label>
                            <input type="text" class="text" name="code" placeholder="请输入右边验证码" required
                                   lay-verify="required" autocomplete="off" style="display: block">
                        </label>
                        <div class="layui-word-aux codeBox"><img id="code" src="captcha.naii" onclick="reloadCode();"
                                                                 style="cursor: pointer;"/></div>
                    </div>

                    <div class="layui-form-item" style="display:none">
                        <label class="layui-form-label">记住密码1</label>
                        <div class="layui-input-block">
                            <label>
                                <input type="checkbox" name="switch" lay-skin="switch">
                            </label>
                        </div>
                    </div>

                    <button class="btn" style="background: linear-gradient(0deg, #f90041 0%, #ff5e52 100%);" lay-submit
                            lay-filter="formDemo">立即登录
                    </button>
                </form>
            </div>
        </div>
        <div class="copyright text-center" style="padding-top:1rem;">
            <p class="copy-footer-29" style="font-size: 12px; color: #FFFFFF;"><a href="https://naii.sjtu.edu.cn/"
                                                                                  target="_black"
                                                                                  style="color: #FFFFFF;">
                版权所有 © 2024 上海交通大学宁波人工智能研究院 All Rights Reserved 沪交ICP备20200103
            </a></p>
        </div>
    </div>
</div>
<!-- js -->
<script src="/plugin/login/js/jquery.min.js"></script>
<!-- //js -->
<script>
    var STATIC_RESOURCE_PATH = '/';
</script>
<script src="/plugin/login/js/jquery.vide.js"></script>
<script>
    //Demo
    layui.use('form', function () {
        var form = layui.form;

        //监听提交
        form.on('submit(formDemo)', function (data) {
            msg.loading("登录中");
            $.post("naiiLoginSubmit.naii", $("form").serialize(), function (result) {
                msg.close();
                var result = JSON.parse(result);
                if (result.result == '1') {
                    localStorage.setItem('token', result.token);
                    msg.success("登录成功", function () {
                        window.location.href = result.info;
                    });
                } else if (result.result == '0') {
                    //登录失败
                    msg.failure(result.info);
                    reloadCode();
                } else if (result.result == '11') {
                    //网站已过期。弹出提示
                    reloadCode();
                    layer.open({
                        title: '到期提示'
                        , content: result.info
                    });
                } else {
                    reloadCode();
                    msg.failure(result);
                }
            }, "text");
            return false;
        });
    });

    //重新加载验证码
    function reloadCode() {
        $('#code').attr('src', 'captcha.naii?' + Math.random());
    }
</script>
<jsp:include page="../common/foot.jsp"/>
<style type="text/css">{
    display: block
;
    background-color: rgba(255, 255, 255, .1)
;
    color: #ff9800
} </style>