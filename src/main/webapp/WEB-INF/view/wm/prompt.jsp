<%@ page import="cn.edu.sjtu.gateway.vm.Global" %>
<%@ page import="cn.edu.sjtu.gateway.vm.util.SystemUtil" %>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<jsp:include page="../wm/common/head.jsp">
    <jsp:param name="title" value="欢迎使用"/>
</jsp:include>
<%
    String redirectUrl = request.getAttribute("redirectUrl").toString();
    if (redirectUrl.contains("http")) {
    } else if (redirectUrl.contains("avascript")) {
    } else {
        redirectUrl = "/" + redirectUrl;
    }

    int state = (int) request.getAttribute("state");

    Object infoObj = request.getAttribute("info");
    String info = "";
    if (infoObj != null) {
        info = infoObj.toString();
    }

    String tishiIco; //默认对号
    if (state == Global.PROMPT_STATE_SUCCESS) {
        tishiIco = "&#xe605;";
        if (info.isEmpty()) {
            info = "执行成功";
        }
    } else {
        tishiIco = "&#x1006;";
        if (info.isEmpty()) {
            info = "执行失败";
        }
    }

%>
<style>
    .myForm {
		width: 495px;
        height: auto;
        border-width: 1px 1px 1px 1px;
        padding: 0;
        overflow: hidden;
        -webkit-box-shadow: 0 0 10px #e2e2e2;
        -moz-box-shadow: 0 0 10px #e2e2e2;
        box-shadow: 0 0 3px #e2e2e2;
        position: absolute;
        left: 50%;
        top: 50%;
		margin: -181px auto 0 -248px;
		text-align: center;
    }

    .tishitubiao {
        float: left;
        margin-left: 70px;
        padding-bottom: 20px;
        padding-right: 25px;
    }

    @media screen and (max-width: 600px) {
        .myForm {
			width: 100%;
            height: 100%;
            border-width: 0;
            padding: 0;
            border-radius: 0px;
            overflow: auto;
            -webkit-box-shadow: 0 0 0 #e2e2e2;
            -moz-box-shadow: 0 0 0 #e2e2e2;
            box-shadow: 0 0 0 #e2e2e2;
            position: static;
            left: 0;
            top: 0;
			margin: 0 auto 0 0;
		}

        .tishitubiao {
            width: 100%;
            margin-left: 0;
            padding-right: 0;
        }
    }
</style>

<div style="width: 100%; height: 100vh; background-image: url('<%=SystemUtil.get("STATIC_RESOURCE_PATH") %>naii/images/image.jpg') !important; background-size: 100% 100%; background-position: top center; background-repeat: no-repeat;">
    <div class="myForm" style="background: #fff; border-radius: 8px;">
        <div style="padding-bottom: 60px; padding-top: 60px;">
            <div class="tishitubiao">
                <i class="layui-icon" style="font-size: 80px; color: #5FB878;"><%=tishiIco %>
                </i>
            </div>
            <div style="padding: 30px 50px 40px 50px; font-size: 22px;color: black;opacity: 0.9; min-height: 70px;">
                <%=info %>
            </div>
            <div style="font-size: 20px;opacity: 0.6;">
                <span id="time">3</span>秒后自动&nbsp;<b><a href="<%=redirectUrl %>"
                                                            style="text-decoration: underline;">跳转</a></b>&nbsp;...
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    function run() {
        var s = document.getElementById("time");
        if (s.innerHTML == 1) {
            window.location.href = '<%=redirectUrl %>';
            return false;
        }
        s.innerHTML = s.innerHTML * 1 - 1;
    }

    window.setInterval("run();", 1000);
</script>

<jsp:include page="../wm/common/foot.jsp"/>