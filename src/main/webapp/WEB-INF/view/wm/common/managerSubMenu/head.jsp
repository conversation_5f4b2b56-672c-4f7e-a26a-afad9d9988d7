<%@page import="cn.edu.sjtu.gateway.vm.Global" %>
<%@ page import="cn.edu.sjtu.gateway.vm.util.SystemUtil" %>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%
    //标题
    String title = request.getParameter("title");
%>
<jsp:include page="../head.jsp">
    <jsp:param name="title" value=""/>
</jsp:include>
<link href="<%=SystemUtil.get("STATIC_RESOURCE_PATH") %>css/site_two_subMenu.css?v=<%=Global.VERSION %>"
      rel="stylesheet">
<style type="text/css">
    /* 避免出现左右滚动条 */
    body {
        width: 100%;
        overflow-x: hidden;
    }

    .managerSubMenu_title {
        height: 65px;
        text-align: left;
        line-height: 65px;
        font-size: 16px;
        font-weight: 700;
        color: black;
        padding-left: 18px;
        display: <%=(title != null && !title.isEmpty())? "":"none" %>;
    }
</style>

<div style="width:100%;height:100%; background-color: #fff; overflow-x: hidden;">

    <div class="layui-nav layui-nav-tree layui-nav-side menu">
        <div class="managerSubMenu_title">
            <%=title %>
        </div>
    </div>
</div>
			