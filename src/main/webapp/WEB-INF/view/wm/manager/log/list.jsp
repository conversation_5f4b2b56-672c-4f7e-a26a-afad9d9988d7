<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://www.naii.edu.cn/java_naii/naii_tld" prefix="x" %>
<jsp:include page="../../common/head.jsp">
    <jsp:param name="title" value="动作日志列表"/>
</jsp:include>

<jsp:include page="../../common/list/formSearch_formStart.jsp"/>
<jsp:include page="../../common/list/formSearch_input.jsp">
    <jsp:param name="iw_label" value="关键词"/>
    <jsp:param name="iw_name" value="queryString"/>
</jsp:include>
<input class="layui-btn iw_list_search_submit" type="submit" value="搜索"/>
<table class="layui-table iw_table">
    <thead>
    <tr>
        <th>动作</th>
        <th>备注</th>
        <th>目标id</th>
        <th>操作用户</th>
        <th>操作时间</th>
    </tr>
    </thead>
    <tbody>
    <% int i = 0; %>
    <c:forEach items="${list}" var="log">
        <% i++; %>
        <tr>
            <td style="cursor: pointer;" id="xnx3_<%=i %>" onclick="xnx3_<%=i %>_onclick();"><x:substring maxLength="18"
                                                                                                          text="${log['action'] }"/></td>
            <td style="cursor: pointer;" onclick="xnx3_<%=i %>_onclick();"><x:substring maxLength="18"
                                                                                        text="${log['remark'] }"/></td>
            <td class="ignore" style="width:50px; cursor: pointer;"
                onclick="xnx3_<%=i %>_onclick();">${log['goalid'] }</td>
            <td class="ignore" style="width:100px; cursor: pointer;" onclick="userView('${log['userid'] }');">
                <x:substring maxLength="18" text="${log['username'] }"/></td>
            <td class="ignore" style="width:140px;"><x:time linuxTime="${log['time'] }"/></td>
        </tr>
        <script>
            //<table class="layui-table iw_table"> 这个湿table显示，但是不含<table>的table头，因为鼠标跟随提示跟点击弹出层的class是不一样的
            var xnx3_<%=i %>_table_content = '' +
                '<tr><td style="width:80px;">动作</td><td>${log['action']}</td></tr>' +
                '<tr><td>描述</td><td>${log['remark']}</td></tr>' +
                '<tr><td>目标id</td><td class="ignore">${log['goalid']}</td></tr>' +
                '<tr><td>操作用户</td><td class="ignore">${log['username']}</td></tr>' +
                '<tr><td>操作人<span class="ignore"> IP</span></td><td class="ignore">${log['ip']}</td></tr>' +
                '<tr><td>时间</td><td class="ignore"><x:time linuxTime="${log['time'] }"/></td></tr>' +
                '<tr><td>访问网址</td><td class="ignore">${log['url']}</td></tr>' +
                '<tr><td>GET参数</td><td class="ignore">${log['param']}</td></tr>' +
                '<tr><td>来源网址</td><td class="ignore">${log['referer']}</td></tr>' +
                '<tr><td>UserAgent</td><td class="ignore">${log['userAgent']}</td></tr>' +
                '<tr><td>触发类</td><td class="ignore">${log['className']}</td></tr>' +
                '<tr><tr><td>触发函数</td><td class="ignore">${log['methodName']}</td></tr>' +
                '</table>';

            //鼠标跟随提示
            $(function () {
                var xnx3_<%=i %>_index = 0;
                $("#xnx3_<%=i %>").hover(function () {
                    xnx3_<%=i %>_index = layer.tips('<table>' + xnx3_<%=i %>_table_content, '#xnx3_<%=i %>', {
                        tips: [1, 'linear-gradient(0deg, #f90041 0%, #ff5e52 100%)'], //还可配置颜色
                        time: 0,
                        tipsMore: true,
                        area: ['480px', 'auto']
                    });
                }, function () {
                    layer.close(xnx3_<%=i %>_index);
                })
            });

            function xnx3_<%=i %>_onclick() {
                view('<table class="layui-table iw_table">' + xnx3_<%=i %>_table_content);
            }
        </script>
    </c:forEach>
    </tbody>
</table>
<!-- 通用分页跳转 -->
<jsp:include page="../../common/page.jsp"/>

<script>
    //查看某条动作详情
    function view(content) {
        layer.open({
            type: 1,
            title: '查看动作详情',
            area: ['490px', 'auto'],
            shadeClose: true, //开启遮罩关闭
            content: content
        });
    }

    //查看用户详情信息
    function userView(id) {
        if (id.length < 1) {
            return;
        }
        layer.open({
            type: 2,
            title: '查看用户信息',
            area: ['460px', '630px'],
            shadeClose: true, //开启遮罩关闭
            content: '/manager/user/view.naii?id=' + id
        });
    }
</script>

<jsp:include page="../../common/foot.jsp"/>