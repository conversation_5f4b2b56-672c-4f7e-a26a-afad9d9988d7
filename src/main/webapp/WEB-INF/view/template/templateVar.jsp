<%@page import="cn.edu.sjtu.gateway.vm.util.SystemUtil" %>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<jsp:include page="../wm/common/head.jsp">
    <jsp:param name="title" value="编辑模版变量"/>
</jsp:include>

<%
    String staticResourcePath = SystemUtil.get("STATIC_RESOURCE_PATH");
%>

<!-- 编辑模式资源 -->
<link rel="stylesheet" href="<%=staticResourcePath %>module/editor/css/editormd.css"/>
<script src="<%=staticResourcePath %>module/editor/editormd.js"></script>

<form id="form" class="layui-form layui-form-pane" method="post" style="padding:5px;">
    <input type="hidden" name="id" value="${templateVar.id}"/>

    <!-- 变量名输入 -->
    <div class="layui-form-item">
        <label class="layui-form-label">变量名</label>
        <div class="layui-input-inline">
            <input type="text" name="varName" lay-verify="varName" placeholder="请输入变量名字"
                   autocomplete="off" class="layui-input" value="${templateVar.varName}">
        </div>
        <div class="layui-form-mid">
            <p>
                可在模版中使用 <span class="ignore">{include=变量名}</span> 调用。<br/>
                建议设置后不要修改，模版调用、备份还原均基于此。
            </p>
        </div>
    </div>

    <!-- 备注输入 -->
    <div class="layui-form-item">
        <label class="layui-form-label">备注</label>
        <div class="layui-input-inline">
            <label>
                <input type="text" name="remark" lay-verify="remark" placeholder="给自己备注，无其他作用"
                       autocomplete="off" class="layui-input" value="${templateVar.remark}">
            </label>
        </div>
        <div class="layui-form-mid">仅供操作人员参考，无实际作用</div>
    </div>

    <!-- 模版变量代码编辑 -->
    <div class="layui-form-item layui-form-text" style="height: 80%;">
        <label class="layui-form-label">模版变量代码</label>
        <div class="layui-input-block">
            <div id="editormd" style="width:100%; height:auto; min-height:400px;">
                <label for="html_textarea"></label><textarea id="html_textarea" name="text" lay-verify="text"
                                                             placeholder="请输入模版变量代码，勿包含 head、body 标签！"
                                                             class="layui-textarea"
                                                             style="height: 95%;">数据加载中...</textarea>
            </div>
        </div>
    </div>

    <!-- 标签帮助 -->
    <div style="font-size:14px; margin-top:-5px;">
        可用标签：
        <a href="javascript:popupTemplateTagHelp('通用标签', '/templateTag/common.naii#%E6%A0%87%E7%AD%BE%E5%88%97%E8%A1%A8', '415', '590');"
           class="help-link">通用标签</a>
        <a href="javascript:popupTemplateTagHelp('动态栏目调用', '/templateTag/dynamic.naii', '770', '560');"
           class="help-link">动态栏目调用标签</a>
    </div>

    <!-- 提交按钮 -->
    <div class="layui-form-item" style="text-align:center;">
        <button class="layui-btn" lay-submit lay-filter="submitForm">保存</button>
    </div>
</form>

<script>
    $(document).ready(function () {
        layui.use(['form'], function () {
            const form = layui.form;

            // 自定义验证规则
            form.verify({
                varName: function (value) {
                    if (!value) return '请输入变量的名字';
                    if (value.length > 20) return '变量名字不能超过20个字符';
                }
            });

            // 监听提交
            form.on('submit(submitForm)', function (data) {
                parent.msg.loading('保存中...');
                let textVar = testEditor.getValue();
                textVar = encrypt(textVar);
                let formData = {
                    id: $('input[name="id"]').val(),
                    varName: $('input[name="varName"]').val(),
                    remark: $('input[name="remark"]').val(),
                    text: textVar
                };

                // 发送数据到后端
                $.ajax({
                    type: "POST",
                    url: "/template/saveTemplateVar.naii",
                    data: formData,
                    success: function (result) {
                        parent.msg.close();
                        if (result.result === 1) {
                            parent.msg.success("保存成功");
                            window.location.href = "templateVarList.naii";
                        } else {
                            parent.msg.error(result.info || '保存失败');
                        }
                    },
                    error: function () {
                        parent.msg.error("保存失败，请稍后重试");
                    }
                });

                return false; // 阻止表单默认提交
            });
        });
    });

    // 弹出标签帮助窗口
    function popupTemplateTagHelp(title, url, width, height) {
        layer.open({
            type: 2,
            title,
            area: [`${width}px`, `${height}px`],
            shade: 0,
            maxmin: true,
            content: url,
            success: function (layero) {
                layer.setTop(layero);
            }
        });
    }

    // 加载模版变量内容
    $.ajax({
        url: `getTemplateVarText.naii?varName=${templateVar.varName}`,
        method: 'POST',
        success: function (data) {
            parent.msg.close();
            let decryptedData = decrypt(data);
            const value = typeof decryptedData === 'string' ? decryptedData : ' ';
            testEditor = editormd("editormd", {
                width: "100%",
                height: "650px",
                placeholder: "请输入模版变量代码",
                value: value || ' ',
                theme: "default",
                mode: "text/html",
                path: '<%=staticResourcePath %>module/editor/lib/'
            });
        },
        error: function () {
            parent.msg.error("加载失败，请稍后重试");
        }
    });
</script>

<jsp:include page="../wm/common/foot.jsp"/>
