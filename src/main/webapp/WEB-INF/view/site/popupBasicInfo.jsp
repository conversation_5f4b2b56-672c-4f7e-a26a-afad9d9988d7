<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="cn.edu.sjtu.gateway.vm.Global"%>
<%@ page import="cn.edu.sjtu.gateway.vm.util.SystemUtil" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://www.naii.edu.cn/java_naii/naii_tld" prefix="x" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<jsp:include page="../wm/common/head.jsp">
	<jsp:param name="title" value="基本信息"/>
</jsp:include>
<!-- weui，一个UI框架，这个包含weui，依赖Jquery -->
<script src="<%=SystemUtil.get("STATIC_RESOURCE_PATH") %>js/jquery-weui.js"></script>
<link rel="stylesheet" href="<%=SystemUtil.get("STATIC_RESOURCE_PATH") %>css/jquery-weui.css">
<link rel="stylesheet" href="<%=SystemUtil.get("STATIC_RESOURCE_PATH") %>css/weui.min.css">


<body style="text-align:left; min-width:10px; background-color: white;">
<script src="/<%=Global.CACHE_FILE %>SiteGenerate_mShowBanner.js"></script>

<div class="weui_cells weui_cells_access" style="margin-top: 0em;">
	
<a class="weui_cell" href="../productPrice.naii" style="display:none;">
	<div class="weui_cell_bd weui_cell_primary">
		<p>产品介绍</p>
	</div>
	<div class="weui_cell_ft">按量计费</div>
</a>
<a class="weui_cell" href="../user/invite.naii" style="display:none;">
	<div class="weui_cell_bd weui_cell_primary">
		<p>我的下线</p>
	</div>
	<div class="weui_cell_ft">下线列表</div>
</a>

</div>

<div class="weui_cells" style="margin-top:0px;">
	
	<div class="weui_cell" style="display:none;">
		<div class="weui_cell_bd weui_cell_primary">
			<p>我的<%=Global.get("CURRENCY_NAME") %></p>
		</div>
		<div class="weui_cell_ft" style="color: #9717F7;cursor: pointer;" onclick="window.parent.openMoneyIndex();">${user.currency }<%=Global.get("CURRENCY_NAME") %></div>
	</div>
	<div class="weui_cell" hidden="hidden">
		<div class="weui_cell_bd weui_cell_primary">
			<p>siteid</p>
		</div>
		<div class="weui_cell_ft ignore">
			${site.id }
		</div>
	</div>
	<div class="weui_cell">
		<div class="weui_cell_bd weui_cell_primary">
			<p>登录账户</p>
		</div>
		<div class="weui_cell_ft ignore">
			${user.username }
		</div>
	</div>
	<div class="weui_cell">
		<div class="weui_cell_bd weui_cell_primary">
			<p>到期时间</p>
		</div>
		<a href="javascript:parent.jumpParentAgency();;" id="yanchangriqi" class="layui-btn layui-btn-primary" style="height: 30px;line-height: 30px;padding: 0 10px;font-size: 12px;margin-right: 10px;">延长</a>
		<div class="weui_cell_ft ignore">
			<x:time linuxTime="${site.expiretime }" format="yyyy-MM-dd"/>
		</div>
	</div>
	<div class="weui_cell">
		<div class="weui_cell_bd weui_cell_primary">
			<p>空间占用</p>
		</div>
		<div class="weui_cell_ft ignore" id="ossSize">计算中...</div>
	</div>
	<div class="weui_cell" style="display:none;">
		<div class="weui_cell_bd weui_cell_primary">
			<p>空间总量</p>
		</div>
		<div class="weui_cell_ft ignore">
			${site.attachmentSizeHave}&nbsp;MB
			<!-- <a href="../productPrice.naii" class="weui_btn weui_btn_mini weui_btn_primary" style="margin-left:10px;">升级</a> -->
		</div>
	</div>
	<div class="weui_cell" style="display:none;">
		<div class="weui_cell_bd weui_cell_primary">
			<p>剩余空间</p>
		</div>
		<div class="weui_cell_ft ignore" id="residueSize">计算中...</div>
	</div>
	<div class="weui_cell">
		<div class="weui_cell_bd weui_cell_primary">
			<p>注册时间</p>
		</div>
		<div class="weui_cell_ft ignore"><x:time linuxTime="${user.regtime }"/></div>
	</div>
	<div class="weui_cell">
		<div class="weui_cell_bd weui_cell_primary">
			<p>最后登录</p>
		</div>
		<div class="weui_cell_ft ignore"><x:time linuxTime="${user.lasttime }"/></div>
	</div>
</div>

<script>
//自适应弹出层大小
var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
parent.layer.iframeAuto(index);


$(function(){
	//延长期限按钮
	var yanchangriqi_tipindex = 0;
	$("#yanchangriqi").hover(function(){
		yanchangriqi_tipindex = layer.tips('点击按钮联系我们，为您延长使用期限', '#yanchangriqi', {
			tips: [2, 'linear-gradient(0deg, #f90041 0%, #ff5e52 100%)'], //还可配置颜色
			time:0,
			tipsMore: true,
			area : ['200px' , 'auto']
		});
	},function(){
		layer.close(yanchangriqi_tipindex);
	})
});

$.post("/sites/getOSSSize.naii", function(data){
	if(data.result == '1'){
		document.getElementById('ossSize').innerHTML = (data.info/1000)+'&nbsp;MB';
		document.getElementById('residueSize').innerHTML = (${site.attachmentSizeHave}-(data.info/1000))+'&nbsp;MB';
	}else{
		parent.msg.failure(data.info);
	}
});

</script>

<jsp:include page="../wm/common/foot.jsp"/>