<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@page import="cn.edu.sjtu.gateway.vm.util.SystemUtil"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://www.naii.edu.cn/java_naii/naii_tld" prefix="x" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<jsp:include page="../wm/common/head.jsp">
	<jsp:param name="title" value="网站基本设置"/>
</jsp:include>
<!-- weui，一个UI框架，这个包含weui，依赖Jquery -->
<script src="<%=SystemUtil.get("STATIC_RESOURCE_PATH") %>js/jquery-weui.js"></script>
<link rel="stylesheet" href="<%=SystemUtil.get("STATIC_RESOURCE_PATH") %>css/jquery-weui.css">
<link rel="stylesheet" href="<%=SystemUtil.get("STATIC_RESOURCE_PATH") %>css/weui.min.css">

<body style="text-align:left; min-width:10px;">
<div class="weui_cells weui_cells_access" style="margin-top: 0em;">
	<a class="weui_cell" href="javascript:updateTitle();parent.msg.close();">
		<div class="weui_cell_bd weui_cell_primary">
			<p>网站标题</p>
		</div>
		<div class="weui_cell_ft ignore" id="nameDiv">${site.name }</div>
	</a>
	<a class="weui_cell" href="javascript:;" style="cursor: text;"> <!-- v6.1 去掉了更改自动分配二级域名的入口 updateDomain_info() -->
		<div class="weui_cell_bd weui_cell_primary">
			<p>分配域名</p>
		</div>
		<div class="weui_cell_ft ignore" id="domainInput">
			<span style="float: left;">
				<div id="domainInput_naii">http://${site.domain }.${autoAssignDomain }</div>
			</span>
		</div>
	</a>
	<a class="weui_cell" href="javascript:updateBindDomain_info();">
		<div class="weui_cell_bd weui_cell_primary">
			<p>绑定域名</p>
		</div>
		<div class="weui_cell_ft ignore" id="domainInput">
			<span style="float: left;">
				<div id="domainInput_bind">${site.bindDomain }</div>
			</span>
		</div>
	</a>
	<a class="weui_cell" href="javascript:updateDiBuLianXi();parent.msg.close();">
		<div class="weui_cell_bd weui_cell_primary">
			<p>联系人姓名</p>
		</div>
		<div class="weui_cell_ft ignore">${site.username }</div>
	</a>
	<a class="weui_cell" href="javascript:updateDiBuLianXi();parent.msg.close();">
		<div class="weui_cell_bd weui_cell_primary">
			<p>联系电话</p>
		</div>
		<div class="weui_cell_ft ignore">${site.phone }</div>
	</a>
	<a class="weui_cell" href="javascript:updateDiBuLianXi();parent.msg.close();">
		<div class="weui_cell_bd weui_cell_primary">
			<p>联系QQ</p>
		</div>
		<div class="weui_cell_ft ignore">${site.qq }</div>
	</a>
	<a class="weui_cell" href="javascript:updateDiBuLianXi();parent.msg.close();">
		<div class="weui_cell_bd weui_cell_primary">
			<p>单位(团体)名</p>
		</div>
		<div class="weui_cell_ft ignore">${site.companyName }</div>
	</a>
	<a class="weui_cell" href="javascript:updateDiBuLianXi();parent.msg.close();">
		<div class="weui_cell_bd weui_cell_primary">
			<p>办公地址</p>
		</div>
		<div class="weui_cell_ft ignore">${site.address }</div>
	</a>
	
	<% //判断是否已开启邮件发送功能
		//if(MailUtil.username != null && MailUtil.username.length() > 0 || MailUtil.password != null && MailUtil.password.length() > 0){
	%>
		<!-- <a class="weui_cell" href="javascript:;" style="display:none;">
			<div class="weui_cell_bd weui_cell_primary">
				<p>我的邮箱</p>
			</div>
			<div class="weui_cell_ft">${user.email }</div>
		</a>  -->
	<% //} %>
	
	

</div>
<script>
//自适应弹出层大小
var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
parent.layer.iframeAuto(index);

//改网站名字
function updateTitle(){
	parent.layer.close(index);
	//window.parent.siteNameClick();
	parent.updateSiteName();
}

//更改网站二级域名
function updateDomain_info(){
	parent.layer.close(index);
	parent.$('#subWindowsParam').text('${site.domain }');
	parent.updateDomain();
}

//更改网站自己绑定的域名
function updateBindDomain_info(){
	//parent.layer.close(index);
	//parent.$('#subWindowsParam').text('${site.bindDomain }');
	parent.updateBindDomain();
}

//更改网站的相关联系信息
function updateDiBuLianXi(){
	parent.layer.close(index);
	parent.updateFooterSiteInfo();
}
</script>

<jsp:include page="../wm/common/foot.jsp"/>