<%@page import="cn.edu.sjtu.gateway.manager.G" %>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<jsp:include page="../wm/common/head.jsp">
    <jsp:param name="title" value="欢迎使用网市场云建站系统"/>
</jsp:include>

<style type="text/css">
    html, body, .content {

    }

    .content {
        width: 600px;
        min-height: 700px;
        margin: 0 auto;
        box-shadow: rgba(0, 0, 0, 0.06) 0px 1px 10px 2px;
        padding: 30px;
        margin-top: 50px;
    }

    .title {
        border-bottom: 1px solid #eee;
        padding-top: 20px;
        padding-left: 10px;
        padding-bottom: 20px;
        font-size: 28px;
        margin-bottom: 20px;
    }

    .content ul {
        padding-left: 20px;
    }

    .content ul li {
        list-style-type: decimal;
        padding-left: 10px;
        padding-bottom: 4px;
    }

    .content ul li img {
        max-width: 250px;
        padding: 4px;
        padding-left: 40px;
    }

    .info {
        font-size: 14px;
        line-height: 22px;
    }

    .info h2, h3, h4, h5 {
        border-bottom: 1px solid #eee;
        padding-top: 23px;
        margin-bottom: 10px;
        padding-bottom: 5px;
    }

    @media only screen and (max-width: 700px) {
        .content {
            width: auto;
            margin-top: 0px;
            box-shadow: rgba(0, 0, 0, 0.06) 0px 0px 0px 0px;
        }

    }

    a {
        color: blue;
    }
</style>

<div class="content">
    <div class="title">
        欢迎使用 网市场云建站系统 v<%=G.VERSION %>
    </div>
    <div style="padding-top:100px;; padding-bottom:110px; text-align:center;">
        <a href="/install/selectAttachment.naii" class="layui-btn layui-btn-primary"
           style="line-height: 0px;padding: 30px;font-size: 20px;">点击此处开始安装本系统</a>
    </div>
    <div class="info" onclick="huodong();" style="display:none; cursor: pointer; padding-top:20px;">
        如果你是用做网站来拉客户或者盈利，做网站的数量比较多，比如50个以上，考虑到系统的可维护性、升级方便、以及网站数量持续增长之后的可扩展性、稳定性，我们建议你参与我们的活动，我们帮你安装、调试好、有入门文档教你怎么更好的使用，让这套建站系统真正给你创造利益。[点此查看更多]
    </div>
    <div class="info" style="    position: initial; bottom: 25px;padding: 20px; color: gray; padding-top: 170px;">
        私有化部署自己的 SAAS 云建站系统，可通过后台任意开通多个网站，每个网站使用自己的账号进行独立管理。延续了织梦、帝国
        CMS 的模版方式，性能高度优化，一台 1 核 1G 服务器可建立几万个独立网站。
    </div>
    <div style="width: 100%;text-align: center; padding-top: 30px;">
        <a href="https://naii.sjtu.edu.cn" target="_black"
           style="color: gray;font-size: 8px;">上海交通大学宁波人工智能研究院</a>
    </div>
</div>

<script>
    //修改系统相关配置，也就是安装的系统参数
    function updateSystemConfig() {
        layer.open({
            title: '修改方式'
            ,
            content: '当您安装成功后，可使用账号 manager  密码 manager 进行登录，登录成功后，在 系统设置 - 系统变量 中，可进行修改相关配置参数。<br/>本次安装，便是针对系统变量的一些重要参数进行简单的引导设定。<br/>注意：看不懂的建议别随便改'
        });
    }
</script>

<jsp:include page="../wm/common/foot.jsp"/>
<style type="text/css">{
    display: block
;
} </style>