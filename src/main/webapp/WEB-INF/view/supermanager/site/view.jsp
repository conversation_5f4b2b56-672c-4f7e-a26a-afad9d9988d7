<%@page import="cn.edu.sjtu.gateway.vm.Global"%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://www.naii.edu.cn/java_naii/naii_tld" prefix="x" %>
<jsp:include page="../../wm/common/head.jsp">
	<jsp:param name="title" value="站点信息"/>
</jsp:include>
<script src="/<%=Global.CACHE_FILE %>SiteGenerate_client.js"></script>

<table class="layui-table iw_table">
	<tbody>
		<tr>
			<td class="iw_table_td_view_name">站点编号</td>
			<td class="ignore">${site.id }</td>
		</tr>
		<tr>
			<td class="iw_table_td_view_name">站点名称</td>
			<td class="ignore">${site.name }</td>
		</tr>
		<tr>
			<td>网站类型</td>
			<td><script type="text/javascript">document.write(client[${site['client']}]);</script></td>
		</tr>
		
		<tr>
			<td class="iw_table_td_view_name">联系人</td>
			<td class="ignore">${site.username }</td>
		</tr>
		<tr>
			<td class="iw_table_td_view_name">联系电话</td>
			<td class="ignore">${site.phone }</td>
		</tr>
		<tr>
			<td class="iw_table_td_view_name">站点QQ</td>
			<td class="ignore">${site.qq }</td>
		</tr>
		<tr>
			<td class="iw_table_td_view_name">二级域名</td>
			<td class="ignore"><a href="http://${site.domain }.<%=cn.edu.sjtu.gateway.manager.G.getFirstAutoAssignDomain() %>" target="_black">${site.domain }.<%=cn.edu.sjtu.gateway.manager.G.getFirstAutoAssignDomain() %></a></td>
		</tr>
		<tr>
			<td class="iw_table_td_view_name">绑定域名</td>
			<td class="ignore"><a href="http://${site.bindDomain }" target="_black">${site.bindDomain }</a></td>
		</tr>
		<tr>
			<td>单位名</td>
			<td class="ignore">${site.companyName }</td>
		</tr>
		<tr>
			<td>地址</td>
			<td class="ignore">${site.address }</td>
		</tr>
		<tr>
			<td class="iw_table_td_view_name">建立时间</td>
			<td class="ignore"><x:time linuxTime="${site['addtime'] }"/></td>
		</tr>
	</tbody>
</table>


<script type="text/javascript">
//自适应弹出层大小
var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
parent.layer.iframeAuto(index);

function deleteNews(id){
	if(confirm("确定要删除吗？删除后不可恢复！")){
		window.location.href="delete.naii?id="+id;
	}
}
function cancelLegitimate(id){
	window.location.href="cancelLegitimate.naii?id="+id;
}
</script>

<jsp:include page="../../wm/common/foot.jsp"/>