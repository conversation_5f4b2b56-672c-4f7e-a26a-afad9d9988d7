<%@page import="cn.edu.sjtu.gateway.vm.Global"%>
<%@page import="cn.edu.sjtu.gateway.vm.util.SystemUtil"%>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://www.naii.edu.cn/java_naii/naii_tld" prefix="x" %>
<%@ taglib prefix="shiro" uri="http://shiro.apache.org/tags" %>
<jsp:include page="../wm/common/head.jsp">
	<jsp:param name="title" value="管理后台"/>
</jsp:include>
<script src="<%=SystemUtil.get("STATIC_RESOURCE_PATH") %>js/fun.js"></script>
<script>
var masterSiteUrl = '<%=SystemUtil.get("MASTER_SITE_URL") %>';
</script>
<script src="/js/manager/commonedit.js?v=${version }"></script>


<style type="text/css">
.iw_table tbody tr .iw_table_td_view_name{
	width:50%;
	padding-left:25%;
}
</style>

<div style="text-align:center; font-size:29px; padding-top:7%; padding-bottom: 10px;">
	欢迎使用NAII云建站系统
</div>


<table class="layui-table iw_table" lay-even lay-skin="nob" style="margin:3%; width:94%;">
	<tbody>
		<tr>
			<td class="iw_table_td_view_name">当前版本</td>
			<td>
				<span style="font-size:18px; padding-right:15px;" class="ignore">v${version }</span>
				<span id="versionTishi" style="font-size:14px;">(最新版本检测中...)</span>
			</td>
		</tr>
		<tr>
			<td class="iw_table_td_view_name">作者</td>
			<td class="ignore">manager</td>
		</tr>
		<tr>
			<td class="iw_table_td_view_name">官网</td>
			<td class="ignore"><a href="https://naii.sjtu.edu.cn" target="_black">naii.sjtu.edu.cn</a></td>
		</tr>
	</tbody>
</table>

<script>
//检测最新版本
function checkVersion(){
	$.getJSON("../../getNewVersion.naii",function(result){
		document.getElementById('versionTishi').innerHTML = '（已是最新版本）';
	});
}
checkVersion();
</script>

<jsp:include page="../../wm/common/foot.jsp"/>
<style type="text/css">{ display:block; } </style>