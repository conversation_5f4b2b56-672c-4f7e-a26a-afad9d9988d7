<%@page import="cn.edu.sjtu.gateway.vm.util.ApplicationPropertiesUtil" %>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<jsp:include page="../wm/common/head.jsp">
    <jsp:param name="title" value="选择图片等附件的存储方式"/>
</jsp:include>

<%
    Object selectStorage = ApplicationPropertiesUtil.getProperty("naii.install.selectStorage");
    if (selectStorage != null && selectStorage.toString().trim().equalsIgnoreCase("false")) {
        //不使用这个页面，那么直接跳转到下个域名设置页面
        response.sendRedirect("/install/domainSet.naii");
    }
%>
<style type="text/css">

    .content {
        width: 600px;
        min-height: 80%;
        margin: 0 auto;
        box-shadow: rgba(0, 0, 0, 0.06) 0px 1px 10px 2px;
        padding: 30px;
        margin-top: 50px;
    }

    .title {
        border-bottom: 1px solid #eee;
        padding-top: 20px;
        padding-left: 10px;
        padding-bottom: 20px;
        font-size: 28px;
        margin-bottom: 20px;
        text-align: center;
    }

    .content ul {
        padding-left: 20px;
    }

    .content ul li {
        list-style-type: decimal;
        padding-left: 10px;
        padding-bottom: 4px;
    }

    .content ul li img {
        max-width: 250px;
        padding: 4px;
        padding-left: 40px;
    }

    .info {
        font-size: 14px;
    }

    .info h2, h3, h4, h5 {
        border-bottom: 1px solid #eee;
        padding-top: 15px;
        margin-bottom: 5px;
    }

    @media only screen and (max-width: 700px) {
        .content {
            width: auto;
            margin-top: 0px;
            box-shadow: rgba(0, 0, 0, 0.06) 0px 0px 0px 0px;
        }

    }

    a {
        color: blue;
    }
</style>


<style type="text/css">
    .select {
        padding: 20px;
    }

    .select > div {
        padding: 30px;
        border: 1px;
        border-style: solid;
        border-color: #d2d2d236;
        margin-top: 20px;
    }

    .select div .title {
        font-size: 20px;
    }

    .select div .intro {
        font-size: 14px;
    }


    .lilist {
        padding-left: 40px;
    }

    .lilist > li {
        list-style: decimal;
    }

    .selectItem {
        cursor: pointer;
    }

    .selectItem:HOVER {
        border-color: #000000;
        background-color: #FAFAFA;
    }
</style>

<script>

    function mianfeiban() {
        if (window.location.hostname.indexOf('localhost') > -1 || window.location.hostname.indexOf('127.0.0.1') > -1) {

            layer.open({
                type: 1
                ,
                title: false
                ,
                closeBtn: false
                ,
                area: '590px;'
                ,
                shade: 0.8
                ,
                id: 'versionUpdateTipsss'
                ,
                resize: false
                ,
                btn: ['本地快速安装', '完整安装']
                ,
                btnAlign: 'c'
                ,
                moveType: 1
                ,
                content: '<div style="padding: 50px; line-height: 22px; background-color: #393D49; color: #fff; font-weight: 300; text-align:left;">'
                    + '<div style="width:100%; text-align:center; font-size:22px;"> <span style="font-size:28px; padding-right:15px;"></span>友情提示</div><br>'
                    + '<style type="text/css"> .xnx3_gxc li{list-style-type: decimal;} </style>'
                    + '<ul style="list-style-type: decimal;" class="xnx3_gxc">'
                    + '<div>监测到您访问域名是本地的，如果是本地体验功能、或者做模版，您可以点下面的 本地快速安装 。 </div>'
                    + '</ul></div>'

                ,
                yes: function (index, layero) {
                    window.location.href = "setLocalDomain.naii?host=" + window.location.host;
                }
                ,
                btn2: function (index, layero) {
                    window.location.href = 'domainSet.naii';
                }
            });


        } else {
            window.location.href = 'domainSet.naii';
        }
    }

    //选择使用obs存储
    function selectObs() {
        /* msg.popups({
            text:'此种方式只有战略合作伙伴才会安装配置',
            width:'200px'
        }); */

        msg.loading('正在判断是否存在华为云OBS配置引导插件');
        post('/plugin/huaWeiYunServiceCreate/isExist.json', {}, function (result) {
            msg.close();
            if (result.result == '1') {
                //成功，有这个插件，跳转进入
                window.location.href = '/plugin/huaWeiYunServiceCreate/index.naii';
            }
        });

        //3秒要是还没获取到响应，那肯定就是没这个插件
        setTimeout(function () {
            msg.close();
            msg.alert('当前系统未安装华为云OBS自动安装引导插件，如果您资金允许，可购买企业版授权，我们将安排专人为您完成安装、调试工作，并为您的使用提供指导咨询。联系微信 xnx3com');
        }, 3000);

        /* msg.confirm('使用华为云OBS存储？此种方式只有战略合作伙伴才会安装配置',function(){
            msg.confirm({
                text:'您可在安装完成后，登录总管理后台（账号密码都是manager），找到 【功能插件】 下的 华为云 插件即可完成配置',
                buttons:{
                    确定:function(){
                        window.location.href='domainSet.naii';
                    }
                }
            });

        }); */
    }
</script>

<jsp:include page="../wm/common/foot.jsp"/>
<style type="text/css">{
    display: block
;
} </style>