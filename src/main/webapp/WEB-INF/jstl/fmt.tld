<?xml version="1.0" encoding="UTF-8" ?>

<taglib xmlns="http://java.sun.com/xml/ns/j2ee"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee http://java.sun.com/xml/ns/j2ee/web-jsptaglibrary_2_0.xsd"
    version="2.0">
    
  <description>JSTL 1.1 i18n-capable formatting library</description>
  <display-name>JSTL fmt</display-name>
  <tlib-version>1.1</tlib-version>
  <short-name>fmt</short-name>
  <uri>http://java.sun.com/jsp/jstl/fmt</uri>

  <validator>
    <description>
        Provides core validation features for JSTL tags.
    </description>
    <validator-class>
        org.apache.taglibs.standard.tlv.JstlFmtTLV
    </validator-class>
  </validator>

  <tag>
    <description>
        Sets the request character encoding
    </description>
    <name>requestEncoding</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.fmt.RequestEncodingTag</tag-class>
    <body-content>empty</body-content>
    <attribute>
        <description>
Name of character encoding to be applied when
decoding request parameters.
        </description>
        <name>value</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <description>
        Stores the given locale in the locale configuration variable
    </description>
    <name>setLocale</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.fmt.SetLocaleTag</tag-class>
    <body-content>empty</body-content>
    <attribute>
        <description>
A String value is interpreted as the
printable representation of a locale, which
must contain a two-letter (lower-case)
language code (as defined by ISO-639),
and may contain a two-letter (upper-case)
country code (as defined by ISO-3166).
Language and country codes must be
separated by hyphen (-) or underscore
(_).        
	</description>
        <name>value</name>
        <required>true</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Vendor- or browser-specific variant.
See the java.util.Locale javadocs for
more information on variants.
        </description>
        <name>variant</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Scope of the locale configuration variable.
        </description>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <description>
        Specifies the time zone for any time formatting or parsing actions
        nested in its body
    </description>
    <name>timeZone</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.fmt.TimeZoneTag</tag-class>
    <body-content>JSP</body-content>
    <attribute>
        <description>
The time zone. A String value is interpreted as
a time zone ID. This may be one of the time zone
IDs supported by the Java platform (such as
"America/Los_Angeles") or a custom time zone
ID (such as "GMT-8"). See
java.util.TimeZone for more information on
supported time zone formats.
        </description>
        <name>value</name>
        <required>true</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <description>
        Stores the given time zone in the time zone configuration variable
    </description>
    <name>setTimeZone</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.fmt.SetTimeZoneTag</tag-class>
    <body-content>empty</body-content>
    <attribute>
        <description>
The time zone. A String value is interpreted as
a time zone ID. This may be one of the time zone
IDs supported by the Java platform (such as
"America/Los_Angeles") or a custom time zone
ID (such as "GMT-8"). See java.util.TimeZone for
more information on supported time zone
formats.
        </description>
        <name>value</name>
        <required>true</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Name of the exported scoped variable which
stores the time zone of type
java.util.TimeZone.
        </description>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Scope of var or the time zone configuration
variable.
        </description>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <description>
        Loads a resource bundle to be used by its tag body
    </description>
    <name>bundle</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.fmt.BundleTag</tag-class>
    <body-content>JSP</body-content>
    <attribute>
        <description>
Resource bundle base name. This is the bundle's
fully-qualified resource name, which has the same
form as a fully-qualified class name, that is, it uses
"." as the package component separator and does not
have any file type (such as ".class" or ".properties")
suffix.
        </description>
        <name>basename</name>
        <required>true</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Prefix to be prepended to the value of the message
key of any nested &lt;fmt:message&gt; action.
        </description>
        <name>prefix</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <description>
        Loads a resource bundle and stores it in the named scoped variable or
        the bundle configuration variable
    </description>
    <name>setBundle</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.fmt.SetBundleTag</tag-class>
    <body-content>empty</body-content>
    <attribute>
        <description>
Resource bundle base name. This is the bundle's
fully-qualified resource name, which has the same
form as a fully-qualified class name, that is, it uses
"." as the package component separator and does not
have any file type (such as ".class" or ".properties")
suffix.
        </description>
        <name>basename</name>
        <required>true</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Name of the exported scoped variable which stores
the i18n localization context of type
javax.servlet.jsp.jstl.fmt.LocalizationC
ontext.
        </description>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Scope of var or the localization context
configuration variable.
        </description>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <description>
        Maps key to localized message and performs parametric replacement
    </description>
    <name>message</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.fmt.MessageTag</tag-class>
    <body-content>JSP</body-content>
    <attribute>
        <description>
Message key to be looked up.
        </description>
        <name>key</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Localization context in whose resource
bundle the message key is looked up.
        </description>
        <name>bundle</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Name of the exported scoped variable
which stores the localized message.
        </description>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Scope of var.
        </description>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <description>
        Supplies an argument for parametric replacement to a containing
        &lt;message&gt; tag
    </description>
    <name>param</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.fmt.ParamTag</tag-class>
    <body-content>JSP</body-content>
    <attribute>
        <description>
Argument used for parametric replacement.
        </description>
        <name>value</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <description>
        Formats a numeric value as a number, currency, or percentage
    </description>
    <name>formatNumber</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.fmt.FormatNumberTag</tag-class>
    <body-content>JSP</body-content>
    <attribute>
        <description>
Numeric value to be formatted.
        </description>
        <name>value</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Specifies whether the value is to be
formatted as number, currency, or
percentage.
        </description>
        <name>type</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Custom formatting pattern.
        </description>
        <name>pattern</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
ISO 4217 currency code. Applied only
when formatting currencies (i.e. if type is
equal to "currency"); ignored otherwise.
        </description>
        <name>currencyCode</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Currency symbol. Applied only when
formatting currencies (i.e. if type is equal
to "currency"); ignored otherwise.
        </description>
        <name>currencySymbol</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Specifies whether the formatted output
will contain any grouping separators.
        </description>
        <name>groupingUsed</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Maximum number of digits in the integer
portion of the formatted output.
        </description>
        <name>maxIntegerDigits</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Minimum number of digits in the integer
portion of the formatted output.
        </description>
        <name>minIntegerDigits</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Maximum number of digits in the
fractional portion of the formatted output.
        </description>
        <name>maxFractionDigits</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Minimum number of digits in the
fractional portion of the formatted output.
        </description>
        <name>minFractionDigits</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Name of the exported scoped variable
which stores the formatted result as a
String.
        </description>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Scope of var.
        </description>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <description>
        Parses the string representation of a number, currency, or percentage
    </description>
    <name>parseNumber</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.fmt.ParseNumberTag</tag-class>
    <body-content>JSP</body-content>
    <attribute>
        <description>
String to be parsed.
        </description>
        <name>value</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Specifies whether the string in the value
attribute should be parsed as a number,
currency, or percentage.
        </description>
        <name>type</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Custom formatting pattern that determines
how the string in the value attribute is to be
parsed.
        </description>
        <name>pattern</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Locale whose default formatting pattern (for
numbers, currencies, or percentages,
respectively) is to be used during the parse
operation, or to which the pattern specified
via the pattern attribute (if present) is
applied.
        </description>
        <name>parseLocale</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Specifies whether just the integer portion of
the given value should be parsed.
        </description>
        <name>integerOnly</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Name of the exported scoped variable which
stores the parsed result (of type
java.lang.Number).
        </description>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Scope of var.
        </description>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <description>
        Formats a date and/or time using the supplied styles and pattern
    </description>
    <name>formatDate</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.fmt.FormatDateTag</tag-class>
    <body-content>empty</body-content>
    <attribute>
        <description>
Date and/or time to be formatted.
        </description>
        <name>value</name>
        <required>true</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Specifies whether the time, the date, or both
the time and date components of the given
date are to be formatted. 
        </description>
        <name>type</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Predefined formatting style for dates. Follows
the semantics defined in class
java.text.DateFormat. Applied only
when formatting a date or both a date and
time (i.e. if type is missing or is equal to
"date" or "both"); ignored otherwise.
        </description>
        <name>dateStyle</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Predefined formatting style for times. Follows
the semantics defined in class
java.text.DateFormat. Applied only
when formatting a time or both a date and
time (i.e. if type is equal to "time" or "both");
ignored otherwise.
        </description>
        <name>timeStyle</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Custom formatting style for dates and times.
        </description>
        <name>pattern</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Time zone in which to represent the formatted
time.
        </description>
        <name>timeZone</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Name of the exported scoped variable which
stores the formatted result as a String.
        </description>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Scope of var.
        </description>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <description>
        Parses the string representation of a date and/or time
    </description>
    <name>parseDate</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.fmt.ParseDateTag</tag-class>
    <body-content>JSP</body-content>
    <attribute>
        <description>
Date string to be parsed.
        </description>
        <name>value</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Specifies whether the date string in the
value attribute is supposed to contain a
time, a date, or both.
        </description>
        <name>type</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Predefined formatting style for days
which determines how the date
component of the date string is to be
parsed. Applied only when formatting a
date or both a date and time (i.e. if type
is missing or is equal to "date" or "both");
ignored otherwise.
        </description>
        <name>dateStyle</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Predefined formatting styles for times
which determines how the time
component in the date string is to be
parsed. Applied only when formatting a
time or both a date and time (i.e. if type
is equal to "time" or "both"); ignored
otherwise.
        </description>
        <name>timeStyle</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Custom formatting pattern which
determines how the date string is to be
parsed.
        </description>
        <name>pattern</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Time zone in which to interpret any time
information in the date string.
        </description>
        <name>timeZone</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Locale whose predefined formatting styles
for dates and times are to be used during
the parse operation, or to which the
pattern specified via the pattern
attribute (if present) is applied.
        </description>
        <name>parseLocale</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Name of the exported scoped variable in
which the parsing result (of type
java.util.Date) is stored.
        </description>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Scope of var.
        </description>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

</taglib>
