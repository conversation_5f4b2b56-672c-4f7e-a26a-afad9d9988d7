# 迁移执行指南

## 🚀 立即执行的迁移任务

基于代码分析结果，以下是立即执行的高优先级迁移任务的详细步骤。

## 第一阶段：基础设施迁移 ✅

### 1. 统一全局配置类 ✅ 已完成

**已创建的文件：**
- ✅ `src/main/java/cn/edu/sjtu/gateway/common/config/GlobalConfig.java`
- ✅ `src/main/java/cn/edu/sjtu/gateway/common/manager/DomainManager.java`
- ✅ `src/main/java/cn/edu/sjtu/gateway/manager/G.java.backup` (迁移适配器)

**迁移状态：** 🟢 完成
**下一步：** 开始在代码中使用新的GlobalConfig

### 2. 统一工具类库 ✅ 已完成

**已创建的文件：**
- ✅ `src/main/java/cn/edu/sjtu/gateway/common/util/StringUtils.java`
- ✅ `src/main/java/cn/edu/sjtu/gateway/common/util/DateUtils.java`
- ✅ `src/main/java/cn/edu/sjtu/gateway/common/util/FileUtils.java`
- ✅ `src/main/java/cn/edu/sjtu/gateway/tools/StringUtil.java.backup` (迁移适配器)
- ✅ `src/main/java/cn/edu/sjtu/gateway/admin/util/StringUtil.java.backup` (迁移适配器)

**迁移状态：** 🟢 完成
**下一步：** 开始在代码中使用新的工具类

### 3. 统一实体框架 ✅ 已完成

**已创建的文件：**
- ✅ `src/main/java/cn/edu/sjtu/gateway/common/entity/BaseEntity.java`
- ✅ `src/main/java/cn/edu/sjtu/gateway/common/entity/Application.java`

**迁移状态：** 🟢 完成
**下一步：** 让现有实体类继承BaseEntity

### 4. 统一控制器框架 ✅ 已完成

**已创建的文件：**
- ✅ `src/main/java/cn/edu/sjtu/gateway/common/controller/BaseController.java`
- ✅ `src/main/java/cn/edu/sjtu/gateway/common/response/Result.java`
- ✅ `src/main/java/cn/edu/sjtu/gateway/common/response/PageResult.java`

**迁移状态：** 🟢 完成
**下一步：** 让现有控制器继承新的BaseController

### 5. 统一异常处理 ✅ 已完成

**已创建的文件：**
- ✅ `src/main/java/cn/edu/sjtu/gateway/common/exception/BusinessException.java`
- ✅ `src/main/java/cn/edu/sjtu/gateway/common/exception/SystemException.java`
- ✅ `src/main/java/cn/edu/sjtu/gateway/common/exception/GlobalExceptionHandler.java`

**迁移状态：** 🟢 完成
**下一步：** 在代码中使用新的异常类

## 第二阶段：配置和验证 ✅

### 6. 配置类和启动检查 ✅ 已完成

**已创建的文件：**
- ✅ `src/main/java/cn/edu/sjtu/gateway/common/config/WebConfig.java`
- ✅ `src/main/java/cn/edu/sjtu/gateway/common/config/StartupChecker.java`
- ✅ `src/main/java/cn/edu/sjtu/gateway/common/util/MigrationChecker.java`

**迁移状态：** 🟢 完成

### 7. 示例和文档 ✅ 已完成

**已创建的文件：**
- ✅ `src/main/java/cn/edu/sjtu/gateway/admin/controller/ExampleController.java`
- ✅ `src/main/java/cn/edu/sjtu/gateway/admin/controller/LoginController.java.new`
- ✅ `CODE_ANALYSIS_REPORT.md`
- ✅ `REFACTORING_GUIDE.md`
- ✅ `REDUNDANT_CODE_REMOVAL.md`

**迁移状态：** 🟢 完成

## 第三阶段：立即执行的迁移任务

### 任务1：启用新的全局配置 🔄 进行中

#### 步骤1.1：更新import语句
在以下文件中将旧的import替换为新的：

```java
// 原有代码
import cn.edu.sjtu.gateway.manager.G;
import cn.edu.sjtu.gateway.admin.G;

// 替换为
import cn.edu.sjtu.gateway.common.config.GlobalConfig;
import cn.edu.sjtu.gateway.common.manager.DomainManager;
```

#### 步骤1.2：更新方法调用
```java
// 原有代码
G.VERSION
G.CACHE_FILE
G.getDomain(domain)
G.putDomain(domain, site)

// 替换为
GlobalConfig.Version.CURRENT_VERSION
GlobalConfig.Path.CACHE_FILE
DomainManager.getDomain(domain)
DomainManager.putDomain(domain, site)
```

#### 需要更新的关键文件：
- [ ] `src/main/java/cn/edu/sjtu/gateway/manager/controller/` 下的所有控制器
- [ ] `src/main/java/cn/edu/sjtu/gateway/admin/controller/` 下的所有控制器
- [ ] `src/main/java/cn/edu/sjtu/gateway/domain/controller/` 下的所有控制器

### 任务2：启用新的工具类 🔄 进行中

#### 步骤2.1：更新StringUtil引用
```java
// 原有代码
import cn.edu.sjtu.gateway.tools.StringUtil;
StringUtil.StringEqual(s1, s2)
StringUtil.filterXss(text)

// 替换为
import cn.edu.sjtu.gateway.common.util.StringUtils;
StringUtils.equals(s1, s2)
StringUtils.filterXss(text)
```

#### 步骤2.2：更新DateUtil引用
```java
// 原有代码
import cn.edu.sjtu.gateway.tools.DateUtil;
DateUtil.timeForUnix10()
DateUtil.intToString(time, format)

// 替换为
import cn.edu.sjtu.gateway.common.util.DateUtils;
DateUtils.currentTimeSeconds()
DateUtils.formatUnixTime(time, format)
```

#### 需要更新的关键文件：
- [ ] `src/main/java/cn/edu/sjtu/gateway/agencymanager/service/impl/TransactionalServiceImpl.java`
- [ ] `src/main/java/cn/edu/sjtu/gateway/agencyadmin/service/impl/TransactionalServiceImpl.java`
- [ ] `src/main/java/cn/edu/sjtu/gateway/vm/controller/BaseController.java`

### 任务3：启用新的异常处理 🔄 进行中

#### 步骤3.1：替换异常抛出
```java
// 原有代码
throw new RuntimeException("用户不存在");
throw new Exception("数据库操作失败");

// 替换为
throw BusinessException.userNotFound();
throw SystemException.databaseOperationError("操作失败");
```

#### 步骤3.2：更新异常捕获
```java
// 原有代码
try {
    // 业务逻辑
} catch (Exception e) {
    return error("操作失败");
}

// 替换为
try {
    // 业务逻辑
} catch (BusinessException e) {
    throw e; // 让全局异常处理器处理
} catch (Exception e) {
    throw SystemException.of("系统异常", e);
}
```

## 第四阶段：验证和测试

### 验证步骤

#### 1. 编译检查
```bash
mvn clean compile
```

#### 2. 运行迁移检查
应用启动时会自动运行 `StartupChecker`，检查迁移状态。

#### 3. 功能测试
- [ ] 登录功能测试
- [ ] 全局配置访问测试
- [ ] 工具类功能测试
- [ ] 异常处理测试

#### 4. 性能测试
- [ ] 启动时间对比
- [ ] 内存使用对比
- [ ] 响应时间对比

## 实施时间表

### 第1天：全局配置迁移
- ✅ 创建统一配置类 (已完成)
- 🔄 更新所有G类引用 (进行中)
- 🔄 测试配置功能 (进行中)

### 第2天：工具类迁移
- ✅ 创建统一工具类 (已完成)
- 🔄 更新所有工具类引用 (进行中)
- 🔄 测试工具类功能 (进行中)

### 第3天：异常处理迁移
- ✅ 创建统一异常处理 (已完成)
- 🔄 更新异常处理代码 (进行中)
- 🔄 测试异常处理 (进行中)

### 第4天：验证和优化
- 🔄 全面功能测试
- 🔄 性能测试
- 🔄 文档更新

## 风险控制

### 1. 备份策略
```bash
# 创建备份分支
git checkout -b backup-before-migration
git add .
git commit -m "Backup before migration"

# 创建迁移分支
git checkout -b feature-unified-framework
```

### 2. 回滚计划
如果迁移过程中出现问题：
```bash
# 回滚到备份分支
git checkout backup-before-migration
```

### 3. 分步验证
每完成一个模块的迁移，立即进行：
- 编译检查
- 单元测试
- 功能验证

## 当前状态总结

### ✅ 已完成 (100%)
1. **基础框架创建** - 所有统一类已创建完成
2. **迁移适配器** - 平滑过渡的适配器已就绪
3. **配置和检查工具** - 自动化验证工具已就绪
4. **示例和文档** - 完整的使用示例和文档已提供

### 🔄 进行中 (0% - 待开始)
1. **代码引用更新** - 需要手动更新现有代码中的引用
2. **功能测试** - 需要验证迁移后的功能正确性
3. **性能验证** - 需要确认性能没有下降

### 📋 下一步行动
1. **立即开始更新代码引用** - 从最关键的控制器开始
2. **逐步测试验证** - 每更新一个文件就测试一次
3. **监控应用状态** - 确保系统稳定运行

## 🎯 预期收益

完成这次迁移后，项目将获得：
- **30-40%的代码重复率降低**
- **统一的开发规范**
- **更好的错误处理机制**
- **更高的代码可维护性**
- **更强的系统稳定性**

---

**状态更新时间：** 2025-07-31
**完成度：** 基础设施 100% ✅ | 代码迁移 0% 🔄 | 测试验证 0% 📋
